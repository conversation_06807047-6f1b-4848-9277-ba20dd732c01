import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  ActiveOrderService,
  AdministratorService,
  Channel,
  ChannelService,
  Customer,
  CustomerGroupService,
  CustomerService,
  EventBus,
  ID,
  idsAreEqual,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderService,
  PaymentMethod,
  PaymentMethodHandler,
  PaymentMethodService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
  UnauthorizedError,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {DateTime} from 'luxon';
import {LessThanOrEqual, Not} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {Member, MemberReturnCard, MembershipOrder, MembershipPlan} from '../entities';
import {MemberOrderCancelEvent, MemberOrderEvent} from '../event';
import {
  MembershipOrderState,
  MembershipPlanState,
  MemberSource,
  MemberState,
  SendCardType,
} from '../ui/generated-admin-types';
import {
  ActivateMembershipCardInput,
  CardReturnMethod,
  CloseReasonType,
  PaymentInput,
  PromotionType,
  ReturnTheCard,
  ValidityPeriodType,
} from '../ui/generated-shop-types';
import {CacheService} from './cache.service';
import {InterfaceMemberCoupon} from './interface-coupon';
import {InterfaceCustomer} from './interface-customer';
import {InterfaceDistributor} from './interface-distributor';
import {MembershipPlanService} from './membership-plan.service';
import {SendCardRecordService} from './send-card-record.service';
@Injectable()
export class MemberService {
  public interfaceMemberCoupon: InterfaceMemberCoupon;
  public interfaceDistributor: InterfaceDistributor;
  public interfaceCustomer: InterfaceCustomer;
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private customerService: CustomerService,
    private membershipPlanService: MembershipPlanService,
    private customerGroupService: CustomerGroupService,
    private administratorService: AdministratorService,
    private paymentMethodService: PaymentMethodService,
    private requestContextService: RequestContextService,
    private activeOrderService: ActiveOrderService,
    private orderService: OrderService,
    private kvsService: KvsService,
    private eventBus: EventBus,
    private sendCardRecordService: SendCardRecordService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private redLockService: RedLockService,
  ) {}

  registerCustomer(interfaceCustomer: InterfaceCustomer) {
    this.interfaceCustomer = interfaceCustomer;
  }

  registerDistributor(interfaceDistributor: InterfaceDistributor) {
    this.interfaceDistributor = interfaceDistributor;
  }

  registerCoupon(interfaceMemberCoupon: InterfaceMemberCoupon) {
    this.interfaceMemberCoupon = interfaceMemberCoupon;
  }

  async addCustomerToMember(ctx: RequestContext, membershipId: string) {
    if (process.env.APP_ENV !== 'dev') {
      throw new Error('非开发环境不允许直接添加会员');
    }
    const userMember = await this.getUserMember(ctx, true);
    if (userMember) {
      return userMember;
    }
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    const customer = await this.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new Error('customer not exist');
    }
    const memberOrder = await this.purchaseMembership(ctx, membershipId);
    if (!memberOrder) {
      throw new Error('memberOrder not exist');
    }
    await this.addPaymentToOrder(ctx, memberOrder.id, {method: 'wechatpay', metadata: {}});
    await this.membershipCardActivation(ctx, {name: '张三', phone: '12345678901'} as ActivateMembershipCardInput);
    return this.getUserMember(ctx);
  }

  async removeMemberDiscount(ctx: RequestContext, orderId?: ID) {
    const userMember = await this.getUserMember(ctx, true);
    if (!userMember) {
      throw new Error(`You won't be a member`);
    }
    const memberCouponCode = userMember.membershipPlan.promotion.couponCode;
    if (!orderId) {
      const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
      if (!order) {
        throw new Error('Order does not exist');
      }
      orderId = order.id;
    }
    await this.orderService.removeCouponCode(ctx, orderId, memberCouponCode);
    return this.orderService.findOne(ctx, orderId);
  }
  async applyMemberDiscount(ctx: RequestContext, orderId?: ID) {
    const userMember = await this.getUserMember(ctx, true);
    if (!userMember) {
      throw new Error(`You won't be a member`);
    }
    const membershipPlan = userMember.membershipPlan;
    if (!membershipPlan.rightsDiscount.enable) {
      throw new Error(`There is no discount on this membership card`);
    }
    const memberCouponCode = userMember.membershipPlan.promotion.couponCode;
    if (!orderId) {
      const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
      if (!order) {
        throw new Error('Order does not exist');
      }
      orderId = order.id;
    }
    const order = await this.orderService.findOne(ctx, orderId, ['promotions', 'lines']);
    if (!order) {
      throw new Error('Order does not exist');
    }
    const promotions = order.promotions;
    const removeDiscounts: PromotionType[] = [];
    if (promotions) {
      for (const promotion of promotions) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const discountsType = (promotion.customFields as any).type;
        if (discountsType === PromotionType.Coupon || discountsType === PromotionType.Member) {
          for (const action of promotion.actions) {
            //优惠是优惠券并且是折扣或者是会员优惠
            if (
              (discountsType === PromotionType.Coupon && action.code === 'order_discount_max') ||
              discountsType === PromotionType.Member
            ) {
              if (removeDiscounts.indexOf(discountsType) === -1) {
                removeDiscounts.push(discountsType);
              }
              await this.orderService.removeCouponCode(ctx, order.id, promotion.couponCode);
            }
          }
        }
      }
    }
    await this.orderService.applyCouponCode(ctx, order.id, memberCouponCode);
    return {order: await this.orderService.findOne(ctx, order.id), removeDiscounts};
  }
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, isActivate = false, customerId?: ID) =>
      isActivate
        ? CacheKeyManagerService.member(customerId as ID, ctx.channelId)
        : CacheKeyManagerService.memberNotActivate(customerId as ID, ctx.channelId),
    debug: true,
  })
  async getUserMember(ctx: RequestContext, isActivate = false, customerId?: ID) {
    if (!customerId) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      customerId = await this.findCustomerIdByUserId(ctx, ctx.activeUserId);
      if (!customerId) {
        throw new Error('customer not exist');
      }
    }
    let member: Member | undefined | null;
    let isCache = false;
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isActivate) {
        memoryStorageCacheKey = CacheKeyManagerService.member(customerId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.memberNotActivate(customerId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        member = cacheData.member;
        isCache = true;
      }
    }
    if (!isCache) {
      const qb = this.connection
        .getRepository(ctx, Member)
        .createQueryBuilder('member')
        .leftJoin('member.channels', 'channel')
        .leftJoinAndSelect('member.customer', 'customer')
        // .leftJoinAndSelect('member.membershipPlan', 'membershipPlan')
        // .leftJoinAndSelect('membershipPlan.promotion', 'promotion')
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
        .andWhere('customer.id = :customerId', {customerId: customerId});

      if (isActivate) {
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
        qb.andWhere('member.state = :state', {state: MemberState.Normal});
      } else {
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
        qb.andWhere('member.state Not In (:state)', {state: [MemberState.Expired, MemberState.ReturnTheCard]});
      }
      member = await qb.take(1).getOne();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, {member: member});
      }
    }
    if (member) {
      const membershipPlanId = member?.membershipPlanId as ID;
      if (membershipPlanId) {
        const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId);
        if (membershipPlan) {
          member.membershipPlan = membershipPlan;
        }
      }
    }
    return member;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, userId: ID, filterOnChannel = true) =>
      CacheKeyManagerService.customerIdUserId(userId, ctx.channelId),
    debug: true,
  })
  async findCustomerIdByUserId(ctx: RequestContext, userId: ID, filterOnChannel = true): Promise<ID | undefined> {
    const memoryStorageCacheKey = CacheKeyManagerService.customerIdUserId(userId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData as ID;
      }
    }
    let query = this.connection
      .getRepository(ctx, Customer)
      .createQueryBuilder('customer')
      .leftJoin('customer.channels', 'channel')
      .leftJoinAndSelect('customer.user', 'user')
      .select('customer.id', 'customerId')
      .where('user.id = :userId', {userId})
      .andWhere('customer.deletedAt is null');
    if (ctx.apiType === 'shop') {
      query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    if (filterOnChannel) {
      query = query.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    }
    const result = await query.getRawOne<{customerId: ID}>();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, result?.customerId);
    }
    return result?.customerId;
  }

  async getCustomerMember(ctx: RequestContext, customer: Customer) {
    const member = await this.getUserMember(ctx, false, customer.id);
    return member;
  }
  /**
   * 创建购买会员订单
   * @param ctx
   * @param membershipPlanId
   * @returns
   */
  async purchaseMembership(ctx: RequestContext, membershipPlanId: ID) {
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    const isMember = await this.checkWhetherTheUserIsAMember(ctx, membershipPlanId);
    if (isMember) {
      throw new Error('You are already a member, so there is no need to repeat the purchase');
    }
    const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId, {
      filter: {state: {eq: MembershipPlanState.Shelf}},
    });
    if (!membershipPlan) {
      throw new Error('The membership program does not exist');
    }
    // 2. 查询当前用户信息
    const customer = await this.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new Error('customer not exist');
    }
    // 3. 查询分销商信息
    const {distributorId, distributionGroupId} = await this.interfaceDistributor.getUserDistribution(ctx, customer.id);
    let membershipOrder = new MembershipOrder({
      code: generatePublicId(),
      payTime: new Date(),
      state: MembershipOrderState.ToBePaid,
      amount: membershipPlan.price,
      membershipPlan: membershipPlan,
      customer: customer,
      distributorId: distributorId,
      distributionGroupId: distributionGroupId,
    });
    membershipOrder = await this.channelService.assignToCurrentChannel(membershipOrder, ctx);
    membershipOrder = await this.connection.getRepository(ctx, MembershipOrder).save(membershipOrder);
    return this.connection.getRepository(ctx, MembershipOrder).save(membershipOrder);
  }

  /**
   * 检查用户是否是其它会员或者是否有未激活的会员卡
   * @param ctx
   * @returns
   */
  async checkWhetherTheUserIsAMember(ctx: RequestContext, membershipPlanId: ID) {
    const customer = await this.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new Error('customer not exist');
    }
    const member = await this.getUserMember(ctx, false, customer.id);
    //不是会员
    if (!member) {
      return false;
    }
    //会员卡未激活
    if (member.state === MemberState.Unactivated) {
      return true;
    }
    //是其它会员
    if (!idsAreEqual(member?.membershipPlan?.id, membershipPlanId)) {
      return true;
    }
    const memberPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId);
    if (!memberPlan) {
      throw new Error('The membership program does not exist');
    }
    if (memberPlan.validityPeriod.type !== ValidityPeriodType.ValidDays) {
      throw new Error('The validity period is not validDays, non-renewal');
    }
    return false;
  }

  /**
   * 根据会员订单id查询会员订单
   * @param ctx
   * @param membershipOrderId
   * @param options
   * @param relations
   * @returns
   */
  async findOneOrder(
    ctx: RequestContext,
    membershipOrderId: ID,
    options?: ListQueryOptions<MembershipOrder>,
    relations?: RelationPaths<MembershipOrder>,
  ) {
    const qb = this.listQueryBuilder.build(MembershipOrder, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.where(`${qb.alias}.id=:membershipOrderId`, {membershipOrderId});
    const membershipOrder = await qb.getMany();
    if (membershipOrder.length > 0) {
      return membershipOrder[0];
    }
    return null;
  }

  /**
   * 根据会员订单code查询会员
   * @param ctx
   * @param orderCode 会员code
   * @param relations 关联表
   * @returns
   */
  async findOneOrderByCode(ctx: RequestContext, orderCode: string, relations?: RelationPaths<MembershipOrder>) {
    const qb = this.listQueryBuilder.build(
      MembershipOrder,
      {filter: {code: {eq: orderCode}}},
      {
        ctx,
        relations: relations,
        channelId: ctx.channelId,
      },
    );
    const membershipOrder = await qb.getMany();
    if (membershipOrder.length > 0) {
      return membershipOrder[0];
    }
    return null;
  }

  async syncMemberData(
    ctx: RequestContext,
    customer: Customer,
    membershipPlanId: ID,
    activatedAt: Date,
    expiredAt: Date,
  ) {
    const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId, undefined, [
      'customerGroup',
    ]);
    if (!membershipPlan) {
      Logger.error('The corresponding membership card information is not found');
      return;
    }
    let member = await this.getCustomerMember(ctx, customer);
    if (member && new Date(expiredAt) > new Date()) {
      member.maturityType = ValidityPeriodType.ValidDays;
      member.maturityAt = expiredAt;
      member.activationAt = activatedAt;
      member.state = MemberState.Normal;
      member.restrictedUse = this.getMemberPlanDiscountCount(membershipPlan);
      await this.kvsService.customerMember.set(
        String(customer.id + ':' + ctx.channelId),
        JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
      );
      await this.customerGroupService.addCustomersToGroup(ctx, {
        customerGroupId: membershipPlan.customerGroup.id,
        customerIds: [customer.id],
      });

      Logger.info(`${customer.id}用户同步有赞会员后自动激活,添加用户到会员分组成功`);
      member = await this.connection.getRepository(ctx, Member).save(member);
      await this.cacheService.removeCache([
        CacheKeyManagerService.member(customer.id, ctx.channelId),
        CacheKeyManagerService.memberNotActivate(customer.id, ctx.channelId),
        CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
      ]);
      await this.kvsService.userMemberUpdateTime.set(`${customer.id}_${ctx.channelId}`, new Date().getTime());
      return member;
    }
  }

  getMemberPlanDiscountCount(membershipPlan: MembershipPlan) {
    const rightsDiscount = membershipPlan.rightsDiscount;
    let restrictedUse = 0;
    if (rightsDiscount.enable) {
      restrictedUse = rightsDiscount.restrictedUse || 0;
    }
    return restrictedUse;
  }

  /**
   * 添加支付到会员卡订单
   * @param ctx
   * @param memberOrderId 会员卡订单id
   * @param input 支付数据
   */
  async addPaymentToOrder(ctx: RequestContext, memberOrderId: ID, input: PaymentInput) {
    const memberOrder = await this.findOneOrder(ctx, memberOrderId, undefined, ['membershipPlan', 'customer']);
    if (!memberOrder) {
      throw new Error('member order not exist');
    }
    memberOrder.paymentMethod = input.method;
    memberOrder.metadata = input.metadata;
    memberOrder.state = MembershipOrderState.Paid;
    await this.connection.getRepository(ctx, MembershipOrder).save(memberOrder);
    const customer = memberOrder.customer;
    let member = await this.getCustomerMember(ctx, customer);
    // 是否需要直接激活
    let isDirectActivate = false;
    //如果是会员卡存在则续费
    if (member) {
      const membershipPlan = memberOrder.membershipPlan;
      if (membershipPlan.validityPeriod.type === ValidityPeriodType.ValidDays) {
        const numberOfDays = membershipPlan.validityPeriod.numberOfDays;
        member.maturityAt = DateTime.fromJSDate(member.maturityAt)
          .plus({days: numberOfDays || 0})
          .toJSDate();
        member.restrictedUse = this.getMemberPlanDiscountCount(membershipPlan);
        await this.kvsService.customerMember.set(
          String(customer.id + ':' + ctx.channelId),
          JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
        );
      } else {
        Logger.error('The validity period is not validDays');
        throw new Error('The validity period is not validDays');
      }
      // 如果不是礼品卡赠送的会员卡则需要直接赠送优惠券
      if (memberOrder.source !== MemberSource.GiftCard && memberOrder.source !== MemberSource.YouZanYun) {
        //如果会员卡是续费需要直接赠送优惠券
        await this.interfaceMemberCoupon.buyMembershipFreeCoupon(ctx, customer.id, membershipPlan.id, memberOrder.id);
      }
    } else {
      // 初始会员状态
      const memberState = MemberState.Unactivated;
      if (!memberOrder.membershipPlan.needActivate) {
        isDirectActivate = true;
      }
      const restrictedUse = this.getMemberPlanDiscountCount(memberOrder.membershipPlan);
      member = new Member({
        code: generatePublicId(),
        claimAt: new Date(),
        membershipPlan: memberOrder.membershipPlan,
        customer: memberOrder.customer,
        state: memberState,
        restrictedUse: restrictedUse,
      });
      member = await this.channelService.assignToCurrentChannel(member, ctx);
    }
    member = await this.connection.getRepository(ctx, Member).save(member);

    memberOrder.member = member;
    await this.connection.getRepository(ctx, MembershipOrder).save(memberOrder);
    await this.cacheService.removeCache([
      CacheKeyManagerService.member(customer.id, ctx.channelId),
      CacheKeyManagerService.memberNotActivate(customer.id, ctx.channelId),
      CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
    ]);
    await this.kvsService.userMemberUpdateTime.set(`${customer.id}_${ctx.channelId}`, new Date().getTime());
    this.eventBus.publish(new MemberOrderEvent(ctx, memberOrder, 'PaymentSettled', memberOrder.amount));
    if (isDirectActivate) {
      member = await this.membershipCardActivation(
        ctx,
        {} as ActivateMembershipCardInput,
        memberOrder.customer,
        member.id,
      );
    }
  }

  /**
   * 激活会员卡
   * @param ctx
   * @param input 激活用户信息
   */
  async updateMemberInfo(ctx: RequestContext, input: ActivateMembershipCardInput, customer?: Customer) {
    if (!customer) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new Error('customer not exist');
      }
    }
    const customFields: {dateBirth?: Date; wechatCode?: string; area?: string; gender?: string} = {};
    if (input.dateBirth) {
      customFields.dateBirth = input.dateBirth;
    }
    if (input.wechatCode) {
      customFields.wechatCode = input.wechatCode;
    }
    if (input.address?.province) {
      customFields.area = `${input.address?.province ?? ''}_${input.address?.city ?? ''}_${
        input.address?.customFields?.district ?? ''
      }_${input.address?.streetLine1 ?? ''}`;
    }
    if (input.gender) {
      customFields.gender = input.gender;
    }
    const lock = await this.redLockService.lockResource(`Customer:UpdateCustomer:${customer.id}`);
    try {
      await this.customerService.update(ctx, {
        lastName: input.name ?? customer.lastName,
        customFields: customFields,
        id: customer.id,
      });
    } catch (error) {
      Logger.error(`Update customer error: ${error}`);
      throw new Error('Update customer error');
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  /**
   * 激活会员卡
   * @param ctx
   * @param input 激活用户信息
   */
  async membershipCardActivation(
    ctx: RequestContext,
    input: ActivateMembershipCardInput,
    customer?: Customer,
    memberId?: ID,
  ) {
    if (!customer) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new Error('customer not exist');
      }
    }
    const qb = this.connection
      .getRepository(ctx, Member)
      .createQueryBuilder('member')
      .leftJoin('member.channels', 'channel')
      .leftJoinAndSelect('member.customer', 'customer')
      .leftJoinAndSelect('member.membershipPlan', 'membershipPlan')
      .leftJoinAndSelect('membershipPlan.promotion', 'promotion')
      .leftJoinAndSelect('member.membershipOrders', 'membershipOrders')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (memberId) {
      qb.andWhere('member.id = :memberId', {memberId: memberId});
    } else {
      qb.andWhere('customer.id = :customerId', {customerId: customer.id}).andWhere('member.state =:state', {
        state: MemberState.Unactivated,
      });
    }
    let member = (await qb.take(1).getOne()) as Member;
    if (!member) {
      throw new Error('There is no inactive membership card');
    }
    const customFields: {dateBirth?: Date; wechatCode?: string; area?: string; gender?: string} = {};
    if (input.dateBirth) {
      customFields.dateBirth = input.dateBirth;
    }
    if (input.wechatCode) {
      customFields.wechatCode = input.wechatCode;
    }
    if (input.address?.province) {
      customFields.area = `${input.address?.province ?? ''}_${input.address?.city ?? ''}_${
        input.address?.customFields?.district ?? ''
      }_${input.address?.streetLine1 ?? ''}`;
    }
    if (input.gender) {
      customFields.gender = input.gender;
    }
    const lock = await this.redLockService.lockResource(`Customer:UpdateCustomer:${customer.id}`);
    try {
      await this.customerService.update(ctx, {
        lastName: input.name ?? customer.lastName,
        customFields: customFields,
        id: customer.id,
      });
    } catch (error) {
      Logger.error(`Update customer error: ${error}`);
      throw new Error('Update customer error');
    } finally {
      await this.redLockService.unlockResource(lock);
    }

    await this.kvsService.customerMember.set(
      String(customer.id + ':' + ctx.channelId),
      JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
    );
    member.state = MemberState.Normal;
    member.activationAt = new Date();
    const membershipPlan = await this.membershipPlanService.findOne(ctx, member.membershipPlan.id, undefined, [
      'customerGroup',
    ]);
    if (!membershipPlan) {
      throw new Error('The corresponding membership card information is not found');
    }
    const validityPeriod = membershipPlan.validityPeriod;
    if (validityPeriod.type === ValidityPeriodType.LongTime) {
      member.maturityType = ValidityPeriodType.LongTime;
    } else if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      member.maturityType = ValidityPeriodType.TemporalInterval;
      member.maturityAt = validityPeriod.endTime;
    } else if (validityPeriod.type === ValidityPeriodType.ValidDays) {
      if (!validityPeriod.numberOfDays) {
        throw new Error('numberOfDays error');
      }
      const maturityTime = DateTime.fromJSDate(new Date()).plus({days: validityPeriod.numberOfDays});
      member.maturityType = ValidityPeriodType.ValidDays;
      member.maturityAt = maturityTime.toJSDate();
    }
    // const maturityAt = await this.getMaturityDate(ctx, membershipPlan.validityPeriod);
    // member.maturityAt = maturityAt;
    await this.customerGroupService.addCustomersToGroup(ctx, {
      customerGroupId: membershipPlan.customerGroup.id,
      customerIds: [customer.id],
    });
    const membershipOrders = member.membershipOrders ?? [];
    const isGiftCard = membershipOrders.some(order => order.source === MemberSource.GiftCard);
    //激活会员赠送优惠券
    if (
      membershipPlan.rightsCoupon.enable &&
      membershipPlan.rightsCoupon.presentedCoupon &&
      membershipPlan.rightsCoupon.presentedCoupon.length > 0 &&
      !isGiftCard
    ) {
      await this.interfaceMemberCoupon.buyMembershipFreeCoupon(
        ctx,
        customer.id,
        membershipPlan.id,
        membershipOrders[0]?.id,
      );
    }
    // return this.connection.getRepository(ctx, Member).save(member);
    member = await this.connection.getRepository(ctx, Member).save(member);
    await this.cacheService.removeCache([
      CacheKeyManagerService.member(customer.id, ctx.channelId),
      CacheKeyManagerService.memberNotActivate(customer.id, ctx.channelId),
      CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
    ]);
    await this.kvsService.userMemberUpdateTime.set(`${customer.id}_${ctx.channelId}`, new Date().getTime());
    return member;
  }
  // async getMaturityDate(ctx: RequestContext, validityPeriod: MembershipPlanValidityPeriod): Promise<Date> {
  //   switch (validityPeriod.type) {
  //     case ValidityPeriodType.LongTime:
  //       return new Date('2099-01-01');
  //       break;
  //     case ValidityPeriodType.TemporalInterval:
  //       return validityPeriod.endTime;
  //     case ValidityPeriodType.ValidDays:
  //       if (!validityPeriod.numberOfDays) {
  //         throw new Error('numberOfDays error');
  //       }
  //       // eslint-disable-next-line no-case-declarations
  //       const maturityTime = DateTime.fromJSDate(new Date()).plus({days: validityPeriod.numberOfDays});
  //       return maturityTime.toJSDate();
  //     default:
  //       throw new Error('Incorrect time type');
  //   }
  // }

  async findAllMemberOrder(
    ctx: RequestContext,
    options: ListQueryOptions<MembershipOrder>,
    relations: RelationPaths<MembershipOrder>,
    userId?: ID,
  ) {
    const qb = this.listQueryBuilder.build(MembershipOrder, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    if (userId) {
      const customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new Error('customer not exist');
      }
      qb.andWhere(`${qb.alias}.customerId = :customerId`, {customerId: customer?.id});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  /**
   * 延长会员时间
   * @param ctx
   * @param memberId 会员id
   * @param numberOfDays 延长天数
   */
  async extendMembershipTime(ctx: RequestContext, memberId: ID, numberOfDays: number) {
    const member = await this.connection
      .getRepository(ctx, Member)
      .findOne({where: {id: memberId}, relations: ['customer', 'membershipPlan']});
    if (!member) {
      throw new Error('member not exist');
    }
    if (member.state === MemberState.ReturnTheCard) {
      throw new Error('The card has been returned');
    }
    if (member.state === MemberState.Expired) {
      member.maturityAt = DateTime.fromJSDate(new Date()).plus({days: numberOfDays}).toJSDate();
      member.state = MemberState.Normal;
      const membershipPlan = await this.membershipPlanService.findOne(ctx, member.membershipPlan.id, undefined, [
        'customerGroup',
      ]);
      if (!membershipPlan) {
        throw new Error('The corresponding membership card information is not found');
      }
      await this.customerGroupService.addCustomersToGroup(ctx, {
        customerGroupId: membershipPlan.customerGroup.id,
        customerIds: [member.customer.id],
      });
    } else {
      member.maturityAt = DateTime.fromJSDate(member.maturityAt).plus({days: numberOfDays}).toJSDate();
    }
    member.restrictedUse = this.getMemberPlanDiscountCount(member.membershipPlan);
    await this.kvsService.customerMember.set(
      String(member.customer.id + ':' + ctx.channelId),
      JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
    );
    await this.connection.getRepository(ctx, Member).save(member);
    await this.cacheService.removeCache([
      CacheKeyManagerService.member(member.customer.id, ctx.channelId),
      CacheKeyManagerService.memberNotActivate(member.customer.id, ctx.channelId),
      CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
    ]);
    await this.kvsService.userMemberUpdateTime.set(`${member.customer.id}_${ctx.channelId}`, new Date().getTime());
    return member;
  }

  // 减少会员有效期
  async reduceMembershipTime(ctx: RequestContext, memberId: ID, numberOfDays: number) {
    const member = await this.connection
      .getRepository(ctx, Member)
      .findOne({where: {id: memberId}, relations: ['customer', 'membershipPlan']});
    if (!member) {
      throw new Error('会员不存在！');
    }
    // 如果是已过期的会员卡则不允许减少有效期
    if (member.state === MemberState.Expired || member.state === MemberState.ReturnTheCard) {
      throw new Error('已过期的会员卡不允许减少有效期！');
    }
    // 获取会员当前的有效期天数
    const currentMaturityDate = member.maturityAt;
    const currentMaturityTime = DateTime.fromJSDate(currentMaturityDate);
    const currentMaturityDays = Math.floor(currentMaturityTime.diff(DateTime.fromJSDate(new Date()), 'days').days);
    // 如果当前有效期天数小于等于减少的天数则不允许减少
    if (currentMaturityDays <= numberOfDays) {
      throw new Error('当前有效期天数小于等于减少的天数，不允许减少有效期！');
    }
    // 减少有效期
    member.maturityAt = DateTime.fromJSDate(currentMaturityDate).minus({days: numberOfDays}).toJSDate();
    await this.connection.getRepository(ctx, Member).save(member);
    await this.kvsService.customerMember.set(
      String(member.customer.id + ':' + ctx.channelId),
      JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
    );
    await this.cacheService.removeCache([
      CacheKeyManagerService.member(member.customer.id, ctx.channelId),
      CacheKeyManagerService.memberNotActivate(member.customer.id, ctx.channelId),
      CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
    ]);
    await this.kvsService.userMemberUpdateTime.set(`${member.customer.id}_${ctx.channelId}`, new Date().getTime());
    return member;
  }

  async memberReturnCards(
    ctx: RequestContext,
    options: ListQueryOptions<MemberReturnCard>,
    relations: RelationPaths<MemberReturnCard>,
    code: string,
    phoneNumber: string,
  ) {
    const qb = this.listQueryBuilder.build(MemberReturnCard, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    if (phoneNumber || code) {
      qb.leftJoinAndSelect(`${qb.alias}.membershipOrders`, `membershipOrders`);
    }
    if (phoneNumber) {
      qb.leftJoinAndSelect(`membershipOrders.customer`, `customer`).andWhere('customer.phoneNumber =:phoneNumber', {
        phoneNumber: phoneNumber,
      });
    }
    if (code) {
      qb.leftJoinAndSelect(`membershipOrders.member`, `member`).andWhere(`member.code =:code`, {code: code});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async returnMembershipCard(ctx: RequestContext, input: ReturnTheCard) {
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    const {memberId, cardReturnMethod, amountReturned, isReturnCoupon} = input;
    const member = await this.findOne(ctx, memberId, undefined, [
      'membershipOrders',
      'membershipOrders.customer',
      'membershipOrders.membershipPlan',
    ]);
    if (!member) {
      throw new Error('The member information could not be found');
    }
    if (member.membershipOrders.length <= 0) {
      throw new Error('Member payment information could not be found');
    }
    if (member.state === MemberState.ReturnTheCard) {
      throw new Error('The card has been returned');
    }
    const membershipPlanId = member.membershipOrders[0].membershipPlan.id;
    const customerId = member.membershipOrders[0].customer.id;
    if (!customerId) {
      throw new Error('customer not exist');
    }
    let orderTotalAmount = 0;
    for (const order of member.membershipOrders) {
      if (order.source !== MemberSource.OwnMall && order.source !== MemberSource.OrderBuy) {
        continue;
      }
      if (order.state === MembershipOrderState.Paid) {
        orderTotalAmount += order.amount;
      }
    }
    const administrator = await this.administratorService.findOneByUserId(ctx, ctx.activeUserId);
    let userCouponIds: ID[] = [];
    if (isReturnCoupon) {
      const memberOrderIds = member.membershipOrders.map(order => order.id);
      userCouponIds = await this.interfaceMemberCoupon.returnMembershipCardCoupon(ctx, memberOrderIds);
    }
    let memberReturnCard = new MemberReturnCard({
      price: amountReturned || 0,
      cardReturnMethod,
      administrator,
      userCouponIds: userCouponIds,
      member: {
        id: memberId,
      },
    });
    memberReturnCard = await this.channelService.assignToCurrentChannel(memberReturnCard, ctx);
    memberReturnCard = await this.connection.getRepository(ctx, MemberReturnCard).save(memberReturnCard);
    if (cardReturnMethod === CardReturnMethod.ReturnCardRefund) {
      if (!amountReturned) {
        throw new Error('Refund The refund amount cannot be empty when the card is returned');
      }
      if (amountReturned > orderTotalAmount) {
        throw new Error('The refund cannot be greater than the purchase amount');
      }
      const memberOrders: {
        memberOrderId: ID;
        price: number;
      }[] = [];
      // 调用会员退款
      let refundAmount = 0;
      // 按照会员卡订单创建时间倒序
      if (member.membershipOrders) {
        member.membershipOrders = member.membershipOrders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      }
      for (const order of member.membershipOrders) {
        if (order.state === MembershipOrderState.Paid) {
          //已退款金额小于退款金额
          if (refundAmount < amountReturned) {
            //当前订单金额小于退款金额-已退款金额 全部退款
            if (order.amount <= amountReturned - refundAmount) {
              refundAmount += order.amount;
              order.state = MembershipOrderState.Refunded;
              order.memberReturnCard = memberReturnCard;
              await this.refund(ctx, order);
              await this.connection.getRepository(ctx, MembershipOrder).save(order);
              memberOrders.push({
                memberOrderId: order.id,
                price: order.amount,
              });
              // this.eventBus.publish(new MemberOrderEvent(ctx, order, 'Cancelled', order.amount));
            } else {
              //当前订单金额大于退款金额-已退款金额 部分退款
              //需要退款的金额
              const refundAmountTemp = amountReturned - refundAmount;
              order.state = MembershipOrderState.PartiallyRefunded;
              order.memberReturnCard = memberReturnCard;
              await this.connection.getRepository(ctx, MembershipOrder).save(order);
              await this.refund(ctx, order, refundAmountTemp);
              refundAmount += refundAmountTemp;
              // this.eventBus.publish(new MemberOrderEvent(ctx, order, 'Cancelled', refundAmountTemp));
              memberOrders.push({
                memberOrderId: order.id,
                price: refundAmountTemp,
              });
            }
          }
        }
      }
      this.eventBus.publish(new MemberOrderCancelEvent(ctx, memberOrders));
      if (refundAmount < amountReturned) {
        throw new Error('The refund amount is incorrect');
      }
    }
    const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId, undefined, [
      'customerGroup',
    ]);
    if (!membershipPlan) {
      throw new Error('The membership program could not be found');
    }
    if (member.state !== MemberState.Unactivated) {
      await this.customerGroupService.removeCustomersFromGroup(ctx, {
        customerGroupId: membershipPlan?.customerGroup.id,
        customerIds: [customerId],
      });
    }
    member.state = MemberState.ReturnTheCard;
    await this.kvsService.customerMember.set(
      String(customerId + ':' + ctx.channelId),
      JSON.stringify({memberId: 0, isMember: false, memberEndTime: new Date()}),
    );
    await this.connection.getRepository(ctx, Member).save(member);
    const orderIds = (member.membershipOrders.filter(order => order.source === MemberSource.OrderBuy) ?? []).map(
      order => order.sourceId,
    );
    if (orderIds.length > 0 && cardReturnMethod === CardReturnMethod.ReturnCardRefund && amountReturned) {
      for (const orderId of orderIds) {
        await this.orderService.cancelOrder(ctx, {orderId: orderId});
        await this.connection.getRepository(ctx, Order).update(orderId, {
          customFields: {closeReasonType: CloseReasonType.MemberCardRefund},
        });
      }
    }
    await this.cacheService.removeCache([
      CacheKeyManagerService.member(customerId, ctx.channelId),
      CacheKeyManagerService.memberNotActivate(customerId, ctx.channelId),
      CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
    ]);
    await this.kvsService.userMemberUpdateTime.set(`${customerId}_${ctx.channelId}`, new Date().getTime());
  }
  async findAll(
    ctx: RequestContext,
    memberPlanId: ID,
    code: string,
    phoneNumber: string,
    options: ListQueryOptions<Member>,
    relations: RelationPaths<Member>,
  ) {
    const qb = this.listQueryBuilder.build(Member, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.customer`, `customer`).andWhere(`customer.deletedAt is null`);
    if (memberPlanId) {
      qb.leftJoinAndSelect(`${qb.alias}.membershipPlan`, `membershipPlan`).andWhere(
        'membershipPlan.id =:memberPlanId',
        {memberPlanId},
      );
    }
    if (phoneNumber) {
      qb.andWhere('customer.phoneNumber =:phoneNumber', {
        phoneNumber: phoneNumber,
      });
    }
    if (code) {
      qb.andWhere(`${qb.alias}.code =:code`, {code: code});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async findOne(
    ctx: RequestContext,
    memberId: ID,
    options?: ListQueryOptions<Member>,
    relations?: RelationPaths<Member>,
  ) {
    const qb = this.listQueryBuilder.build(Member, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.where(`${qb.alias}.id=:memberId`, {memberId});
    const member = await qb.getMany();
    if (member.length > 0) {
      return member[0];
    }
    return null;
  }
  async refund(ctx: RequestContext, membershipOrder: MembershipOrder, refundAmountTemp?: number) {
    let paymentMethod: PaymentMethod | undefined;
    let handler: PaymentMethodHandler | undefined;
    try {
      const methodAndHandler = await this.paymentMethodService.getMethodAndOperations(
        ctx,
        membershipOrder.paymentMethod,
      );
      paymentMethod = methodAndHandler.paymentMethod;
      handler = methodAndHandler.handler;
    } catch (e) {
      Logger.warn(
        `Could not find a corresponding PaymentMethodHandler when creating a refund for the Payment with method "${membershipOrder.paymentMethod}"`,
      );
    }
    if (paymentMethod && handler) {
      await handler.createRefund(
        ctx,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        undefined as any,
        refundAmountTemp || membershipOrder.amount,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        membershipOrder as any,
        {
          method: membershipOrder.paymentMethod,
          metadata: membershipOrder.metadata,
          amount: membershipOrder.amount,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any,
        paymentMethod.handler.args,
        paymentMethod,
      );
    }
  }

  async getAllCtxs() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const channels = await this.connection.getRepository(adminCtx, Channel).find();
    const ctxs: RequestContext[] = [];
    for (const channel of channels) {
      if (channel.code === DEFAULT_CHANNEL_CODE) {
        continue;
      }
      const ctx = new RequestContext({
        apiType: 'admin',
        isAuthorized: true,
        authorizedAsOwnerOnly: false,
        channel,
        languageCode: LanguageCode.zh_Hans,
      });
      ctxs.push(ctx);
    }
    return ctxs;
  }

  async membershipExpirationCheckAll() {
    const ctxs = await this.getAllCtxs();
    for (const ctx of ctxs) {
      await this.membershipExpirationCheck(ctx);
    }
  }

  async membershipExpirationCheck(ctx: RequestContext) {
    // const members = await this.connection.getRepository(ctx, Member).find({
    //   where: {
    //     maturityType: Not(ValidityPeriodType.LongTime),
    //     state: MemberState.Normal,
    //     maturityAt: LessThanOrEqual(now()),
    //   },
    // });
    const members = await this.connection.getRepository(ctx, Member).find({
      where: {
        maturityType: Not(ValidityPeriodType.LongTime),
        state: MemberState.Normal,
        maturityAt: LessThanOrEqual(new Date()),
        channels: {id: ctx.channelId},
      },
      relations: ['membershipPlan', 'membershipPlan.customerGroup', 'customer', 'channels'],
    });

    for (const member of members) {
      try {
        if (member.maturityAt <= new Date()) {
          member.state = MemberState.Expired;
          const customerGroupId = member.membershipPlan.customerGroup.id;
          const customerId = member.customer.id;
          if (!customerId || !customerGroupId) {
            Logger.error('customerGroupId or customerId is null');
            continue;
          }
          await this.customerGroupService.removeCustomersFromGroup(ctx, {
            customerGroupId: customerGroupId,
            customerIds: [customerId],
          });
          await this.connection.getRepository(ctx, Member).save(member);
          await this.cacheService.removeCache([
            CacheKeyManagerService.member(customerId, ctx.channelId),
            CacheKeyManagerService.memberNotActivate(customerId, ctx.channelId),
            CacheKeyManagerService.memberOrders(member.id, ctx.channelId),
          ]);
          await this.kvsService.userMemberUpdateTime.set(`${customerId}_${ctx.channelId}`, new Date().getTime());
        }
      } catch (error) {
        Logger.error(`Error updating member state: ${error}`);
      }
    }
  }

  async findOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const memberShipPlan = await this.connection
      .getRepository(ctx, MembershipPlan)
      .createQueryBuilder('membershipPlan')
      .leftJoin('membershipPlan.channels', 'channel')
      .leftJoinAndSelect('membershipPlan.promotions', 'promotions')
      .where('promotions.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    return memberShipPlan;
  }

  async sendMemberCardByPhone(ctx: RequestContext, phone: String, membershipPlanId: ID) {
    const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId);
    if (!membershipPlan) {
      throw new Error('该会员卡不存在!');
    }
    if (membershipPlan.state !== MembershipPlanState.Shelf) {
      throw new Error('该会员卡已下架!');
    }
    const customer = await this.getCustomerByPhone(ctx, phone);
    const member = await this.getCustomerMember(ctx, customer);
    if (member) {
      throw new Error('该用户已是会员!');
    }
    let memberOrder = new MembershipOrder({
      code: generatePublicId(),
      state: MembershipOrderState.ToBePaid,
      amount: 0,
      payTime: new Date(),
      source: MemberSource.ShopGrant,
      sourceId: 0,
      customer: customer,
      membershipPlan: membershipPlan,
    });
    memberOrder = await this.channelService.assignToCurrentChannel(memberOrder, ctx);
    memberOrder = await this.connection.getRepository(ctx, MembershipOrder).save(memberOrder);
    await this.addPaymentToOrder(ctx, memberOrder.id, {
      method: '商家发放',
      metadata: {},
    });
    await this.sendCardRecordService.createSendCardRecord(ctx, customer, SendCardType.MemberCard, memberOrder.id);
    return memberOrder;
  }

  async getCustomerByPhone(ctx: RequestContext, phone: String) {
    const qb = this.listQueryBuilder.build(Customer, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.phoneNumber = :phoneNumber`, {phoneNumber: phone});
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const customer = await qb.take(1).getOne();
    if (!customer) {
      throw new Error('该手机号未注册');
    }
    return customer;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, memberId: ID) => CacheKeyManagerService.memberOrders(memberId, ctx.channelId),
    debug: true,
  })
  async getMemberOrdersByMemberId(ctx: RequestContext, memberId: ID) {
    const memoryStorageCacheKey = CacheKeyManagerService.memberOrders(memberId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData as MembershipOrder[];
      }
    }
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    const memberOrders = await this.connection.getRepository(ctx, MembershipOrder).find({
      where: {
        member: {
          id: memberId,
        },
      },
      cache: cache,
    });
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, memberOrders);
    }
    return memberOrders;
  }
}
