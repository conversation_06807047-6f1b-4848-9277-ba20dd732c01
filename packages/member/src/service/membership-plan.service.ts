import {Injectable} from '@nestjs/common';
import {CacheKeyManagerService, KvsService, MemoryStorageService, cacheableAccess} from '@scmally/kvs';
import {
  ChannelService,
  customerGroup,
  CustomerGroupService,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UserInputError,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {MembershipPlan} from '../entities';
import {memberDiscountPercentage} from '../promotion-action/discount-action';
import {
  DeletionResult,
  MembershipPlanInput,
  MembershipPlanSwitchingStateInput,
  SwitchingState,
  ValidityPeriodType,
} from '../ui/generated-admin-types';
import {MembershipPlanState, PromotionType} from '../ui/generated-shop-types';
import {CacheService} from './cache.service';

@Injectable()
export class MembershipPlanService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private customerGroupService: CustomerGroupService,
    private promotionService: PromotionService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
  ) {}

  async switchingState(ctx: RequestContext, input: MembershipPlanSwitchingStateInput) {
    const membershipPlan = await this.findOne(ctx, input.id, undefined, ['promotion', 'customerGroup', 'channels']);
    if (!membershipPlan) {
      throw new Error('The membership card does not exist');
    }
    const state = input.state;
    switch (state) {
      case SwitchingState.Disable:
        await this.promotionService.updatePromotion(ctx, {id: membershipPlan.promotion.id, enabled: false});
        await this.connection
          .getRepository(ctx, MembershipPlan)
          .update(membershipPlan.id, {state: MembershipPlanState.Disable});
        break;
      case SwitchingState.Enable:
        await this.promotionService.updatePromotion(ctx, {id: membershipPlan.promotion.id, enabled: true});
        await this.connection
          .getRepository(ctx, MembershipPlan)
          .update(membershipPlan.id, {state: MembershipPlanState.Shelf});
        break;
      case SwitchingState.Shelf:
        await this.connection
          .getRepository(ctx, MembershipPlan)
          .update(membershipPlan.id, {state: MembershipPlanState.Shelf});
        break;
      case SwitchingState.TakeOffTheShelf:
        await this.connection
          .getRepository(ctx, MembershipPlan)
          .update(membershipPlan.id, {state: MembershipPlanState.TakeOffTheShelf});
        break;
      default:
        throw new Error('Unknown state type');
    }
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlanAll(ctx.channelId),
      CacheKeyManagerService.promotion(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.membershipPlan(membershipPlan.id, ctx.channelId),
    ]);
    return this.connection.getRepository(ctx, MembershipPlan).findOneOrFail({where: {id: membershipPlan.id}});
  }
  async updateMembershipPlan(ctx: RequestContext, input: MembershipPlanInput) {
    this.validate(ctx, input);
    const membershipPlanId = input.id;
    if (!membershipPlanId) {
      throw new Error('club card id can not be empty');
    }
    let membershipPlan = await this.findOne(ctx, membershipPlanId, undefined, [
      'promotion',
      'customerGroup',
      'channels',
    ]);
    if (!membershipPlan) {
      throw new Error('The membership card cannot be found');
    }
    membershipPlan = new MembershipPlan({
      ...membershipPlan,
      ...(input as unknown as MembershipPlan),
    });
    membershipPlan = await this.connection.getRepository(ctx, MembershipPlan).save(membershipPlan);
    await this.customerGroupService.update(ctx, {
      id: membershipPlan.customerGroup.id,
      name: membershipPlan.name,
    });
    await this.promotionService.updatePromotion(ctx, {
      id: membershipPlan.promotion.id,
      // name: membershipPlan.name,
      actions: [
        {
          code: memberDiscountPercentage.code,
          arguments: [{name: 'discount', value: String(membershipPlan.rightsDiscount.discountRate)}],
        },
      ],
    });
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlanAll(ctx.channelId),
      CacheKeyManagerService.promotion(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.membershipPlan(membershipPlan.id, ctx.channelId),
      CacheKeyManagerService.membershipPlanCoupon(membershipPlan.id, ctx.channelId),
    ]);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    return membershipPlan;
  }
  async deleteMembershipPlan(ctx: RequestContext, membershipPlanId: string) {
    const membershipPlan = await this.findOne(ctx, membershipPlanId, undefined, [
      'promotion',
      'customerGroup',
      'channels',
    ]);
    if (!membershipPlan) {
      throw new Error('The membership card cannot be found');
    }
    await this.promotionService.softDeletePromotion(ctx, membershipPlan.promotion.id);
    await this.customerGroupService.delete(ctx, membershipPlan.customerGroup.id);
    // return this.connection.getRepository(ctx, MembershipPlan).delete(membershipPlanId);
    await this.connection.getRepository(ctx, MembershipPlan).update(membershipPlanId, {deletedAt: new Date()});
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlanAll(ctx.channelId),
      CacheKeyManagerService.promotion(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(membershipPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.membershipPlan(membershipPlan.id, ctx.channelId),
      CacheKeyManagerService.membershipPlanCoupon(membershipPlan.id, ctx.channelId),
    ]);
    return {
      result: DeletionResult.Deleted,
      message: 'Successfully deleted',
    };
  }
  async createMembershipPlan(ctx: RequestContext, input: MembershipPlanInput) {
    this.validate(ctx, input);
    let membershipPlan = new MembershipPlan({
      ...(input as unknown as MembershipPlan),
      state: MembershipPlanState.Shelf,
    });
    membershipPlan = await this.channelService.assignToCurrentChannel(membershipPlan, ctx);
    const customerGroupEntity = await this.customerGroupService.create(ctx, {name: membershipPlan.name});
    const promotion = await this.createPromotion(ctx, membershipPlan, customerGroupEntity.id);
    membershipPlan.customerGroup = customerGroupEntity;
    membershipPlan.promotion = promotion;
    await this.connection.getRepository(ctx, MembershipPlan).save(membershipPlan);
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlanAll(ctx.channelId),
      CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
    ]);
    return this.findOne(ctx, membershipPlan.id);
  }

  async createPromotion(ctx: RequestContext, membershipPlan: MembershipPlan, customerGroupID: ID): Promise<Promotion> {
    const discount = membershipPlan.rightsDiscount.discountRate;
    if (!discount && discount !== 0) {
      throw new Error('The discount value cannot be empty');
    }
    const promotionInput = {
      name: membershipPlan.name,
      enabled: membershipPlan.state === MembershipPlanState.Shelf,
      couponCode: generatePublicId(),
      conditions: [{code: customerGroup.code, arguments: [{name: 'customerGroupId', value: String(customerGroupID)}]}],
      actions: [
        {
          code: memberDiscountPercentage.code,
          arguments: [{name: 'discount', value: String(discount)}],
        },
      ],
      translations: [{languageCode: LanguageCode.en, name: membershipPlan.name}],
    };
    const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
    await this.connection
      .getRepository(ctx, Promotion)
      .update(promotion.id, {customFields: {type: PromotionType.Member}});
    return promotion;
  }
  validate(ctx: RequestContext, input: MembershipPlanInput) {
    const validityPeriod = input.validityPeriod;
    if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      if (!validityPeriod.startTime || !validityPeriod.endTime) {
        throw new Error('When the validity period is a time range, the start time and end time cannot be empty');
      }
    } else if (validityPeriod.type === ValidityPeriodType.ValidDays) {
      if (!validityPeriod.numberOfDays) {
        throw new Error('When the validity period is days, numberOfDays cannot be empty');
      }
    }
    if (input.rightsDiscount.enable) {
      const discountRate = input.rightsDiscount.discountRate;
      if (!discountRate) {
        throw new UserInputError('The discount cannot be empty');
      }
      if (discountRate < 0 || discountRate >= 100) {
        throw new UserInputError('The discount can only be 1 to 99');
      }
    }
    if (input.rightsPoints.enable) {
      const pointsMultiple = input.rightsPoints.pointsMultiple;
      if (!pointsMultiple) {
        throw new UserInputError('The pointsMultiple cannot be empty');
      }
    }
    if (input.rightsCoupon.enable) {
      const presentedCoupon = input.rightsCoupon.presentedCoupon;
      if (!presentedCoupon || presentedCoupon.length === 0) {
        throw new UserInputError('The presentedCoupon cannot be empty');
      }
      for (const coupon of presentedCoupon) {
        if (!coupon?.couponId) {
          throw new UserInputError('The couponId cannot be empty');
        }
        if (!coupon?.presentedCount || coupon.presentedCount < 0) {
          throw new UserInputError('The quantity cannot be empty');
        }
      }
    }
    if (!input.rightsPoints.enable && !input.rightsCoupon.enable && !input.rightsDiscount.enable) {
      throw new UserInputError('At least one of the rights must be enabled');
    }
  }
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, membershipPlanId: ID) =>
      CacheKeyManagerService.membershipPlan(membershipPlanId, ctx.channelId),
    debug: true,
  })
  async findOne(
    ctx: RequestContext,
    membershipPlanId: ID,
    options?: ListQueryOptions<MembershipPlan>,
    relations?: RelationPaths<MembershipPlan>,
  ) {
    let membershipPlans: MembershipPlan[] = [];
    const memoryStorageCacheKey = CacheKeyManagerService.membershipPlan(membershipPlanId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      membershipPlans = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!membershipPlans || membershipPlans.length <= 0) {
      const qb = this.listQueryBuilder.build(MembershipPlan, options, {
        ctx,
        relations: relations,
        channelId: ctx.channelId,
      });
      qb.leftJoinAndSelect(`${qb.alias}.customerGroup`, 'customerGroup');
      qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
      qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
      qb.andWhere(`${qb.alias}.id=:membershipPlanId`, {membershipPlanId});
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      membershipPlans = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, membershipPlans);
      }
    }
    if (membershipPlans.length > 0) {
      return membershipPlans[0];
    }
    return null;
  }

  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<MembershipPlan>,
    relations: RelationPaths<MembershipPlan>,
  ) {
    const qb = this.listQueryBuilder.build(MembershipPlan, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  /**
   * 显示/隐藏会员计划
   * @param ctx
   * @param membershipPlanId
   * @param isShow
   * @returns
   */
  async showHideMembershipPlan(ctx: RequestContext, membershipPlanId: string, isShow: boolean) {
    const memberPlan = await this.findOne(ctx, membershipPlanId);
    if (!memberPlan) {
      throw new Error('No coupon information for corresponding member can be found');
    }
    await this.connection.getRepository(ctx, MembershipPlan).update(membershipPlanId, {isShow: isShow ? true : false});
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlanAll(ctx.channelId),
      CacheKeyManagerService.promotion(memberPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(memberPlan?.promotion?.id, ctx.channelId),
      CacheKeyManagerService.membershipPlan(membershipPlanId, ctx.channelId),
    ]);
    return this.findOne(ctx, membershipPlanId);
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext) => CacheKeyManagerService.membershipPlanAll(ctx.channelId),
    debug: true,
  })
  async maxDiscountMembershipPlan(ctx: RequestContext) {
    const memoryStorageCacheKey = CacheKeyManagerService.membershipPlanAll(ctx.channelId);
    let membershipPlans: MembershipPlan[] = [];
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        membershipPlans = cacheData as MembershipPlan[];
      }
    }
    if (!membershipPlans || membershipPlans.length === 0) {
      const memberShipPlanQb = this.listQueryBuilder.build(
        MembershipPlan,
        {filter: {isShow: {eq: true}, state: {eq: MembershipPlanState.Shelf}, deletedAt: {isNull: true}}},
        {
          ctx,
          channelId: ctx.channelId,
        },
      );
      if (ctx.apiType === 'shop') {
        memberShipPlanQb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      membershipPlans = await memberShipPlanQb.getMany();
      this.memoryStorageService.set(memoryStorageCacheKey, membershipPlans);
    }
    if (membershipPlans.length === 0) {
      return;
      // throw new Error('No coupon information for corresponding member can be found');
    }
    let maxDiscount = 100;
    let maxDiscountMembershipPlanId = membershipPlans[0].id;
    for (const membershipPlan of membershipPlans) {
      if (membershipPlan.rightsDiscount?.enable) {
        // 如果有折扣权益，且折扣比例小于当前最大折扣比例，则更新最大折扣
        if (membershipPlan.rightsDiscount?.discountRate && membershipPlan.rightsDiscount?.discountRate < maxDiscount) {
          maxDiscount = membershipPlan.rightsDiscount?.discountRate;
          maxDiscountMembershipPlanId = membershipPlan.id;
        }
      }
    }
    return this.findOne(ctx, maxDiscountMembershipPlanId);
  }
}
