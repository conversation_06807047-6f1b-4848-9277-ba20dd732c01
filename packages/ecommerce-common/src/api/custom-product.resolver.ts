import {Args, Info, Parent, Query, ResolveField, Resolver} from '@nestjs/graphql';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ProductListOptions,
  ProductVariantListOptions,
  QueryProductArgs,
  QueryProductsArgs,
} from '@vendure/common/lib/generated-types';
import {
  Allow,
  Api,
  ApiType,
  Asset,
  Collection,
  CollectionService,
  Ctx,
  FacetValue,
  FacetValueService,
  ID,
  ListQueryBuilder,
  PaginatedList,
  Permission,
  Product,
  ProductOptionGroup,
  ProductOptionGroupService,
  ProductService,
  ProductVariant,
  ProductVariantService,
  RelationPaths,
  Relations,
  RequestContext,
  Translated,
  TranslatorService,
  UserInputError,
} from '@vendure/core';
import {IsNull} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PromotionType} from '../generated-shop-types';
import {CustomerProductService, CustomerProductVariantService, PointsProductService} from '../service';
import {LocaleStringHydrator} from '../service/custom-locale-string-hydrator';
@Resolver('Product')
export class CustomProductEntityResolver {
  constructor(
    private productService: ProductService,
    private customerProductVariantService: CustomerProductVariantService,
    private productVariantService: ProductVariantService,
    private customerProductService: CustomerProductService,
    private translator: TranslatorService,
    private collectionService: CollectionService,
    private productOptionGroupService: ProductOptionGroupService,
    private listQueryBuilder: ListQueryBuilder,
    private facetValueService: FacetValueService,
    private localeStringHydrator: LocaleStringHydrator,
    private memoryStorageService: MemoryStorageService,
    private pointsProductService: PointsProductService,
  ) {}

  @ResolveField()
  name(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, product, 'name');
  }

  @ResolveField()
  slug(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, product, 'slug');
  }

  @ResolveField()
  description(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, product, 'description');
  }

  @ResolveField()
  languageCode(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, product, 'languageCode');
  }

  @ResolveField()
  async variants(
    @Ctx() ctx: RequestContext,
    @Parent() product: Product,
    @Relations({entity: ProductVariant}) relations: RelationPaths<ProductVariant>,
  ): Promise<Array<Translated<ProductVariant>>> {
    if (product.variants?.length && product.variants[0].taxCategory) {
      let variants = product.variants;
      variants = variants.filter(v => v.deletedAt === null);
      const items = await this.customerProductVariantService.applyPricesAndTranslateVariants(ctx, variants);
      if (ctx.apiType === 'shop') {
        return items.filter(v => v.enabled);
      }
      return items;
    } else {
      relations = this.customerProductVariantService.addProductVariantRelations(relations);
      const {items: variants} = await this.customerProductVariantService.getVariantsByProductId(
        ctx,
        product.id,
        {},
        relations,
      );
      return variants;
    }
  }

  @Query()
  @Allow(Permission.Public)
  getProductVariants(
    @Ctx() ctx: RequestContext,
    @Args('productId') productId: ID,
    @Args() args: {options: ProductVariantListOptions},
    @Relations({entity: ProductVariant, omit: ['assets']}) relations: RelationPaths<ProductVariant>,
  ) {
    return this.customerProductVariantService.getVariantsByProductId(ctx, productId, args.options, relations);
  }

  @ResolveField()
  async variantList(
    @Ctx() ctx: RequestContext,
    @Parent() product: Product,
    @Args() args: {options: ProductVariantListOptions},
    @Relations({entity: ProductVariant, omit: ['assets']}) relations: RelationPaths<ProductVariant>,
  ): Promise<PaginatedList<ProductVariant>> {
    return this.customerProductVariantService.getVariantsByProductId(ctx, product.id, args.options, relations);
  }

  @Query()
  @Allow(Permission.Public)
  async products(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryProductsArgs,
    @Api() apiType: ApiType,
    @Relations({entity: Product, omit: ['variants']}) relations: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    args.options = args?.options || {};
    if (!args.options?.skip) {
      args.options.skip = Number(process.env.DEFAULT_SKIP ?? 0);
    }
    if (!args.options?.take) {
      args.options.take = Number(process.env.DEFAULT_PAGE_SIZE ?? 50);
    }
    const isShop = apiType === 'shop';
    relations = this.customerProductService.addProductRelations(relations);
    if (isShop) {
      args.options.filter = {
        ...(args.options.filter ?? {}),
        enabled: {eq: true},
      };
    }
    return this.productService.findAll(ctx, args.options || undefined, relations);
  }

  @Query()
  @Allow(Permission.Public)
  async activeProducts(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ProductListOptions,
    @Args('promotionType') promotionType: PromotionType,
    @Args('isExchange') isExchange = false,
    @Api() apiType: ApiType,
    @Relations({entity: Product, omit: ['variants']}) relations: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    options = options || {};
    if (!options?.skip) {
      options.skip = Number(process.env.DEFAULT_SKIP ?? 0);
    }
    if (!options?.take) {
      options.take = Number(process.env.DEFAULT_PAGE_SIZE ?? 50);
    }
    const isShop = apiType === 'shop';
    relations = this.customerProductService.addProductRelations(relations);
    if (isShop) {
      options.filter = {
        ...(options.filter ?? {}),
        enabled: {eq: true},
      };
    }
    return this.customerProductService.activeProducts(ctx, promotionType, isExchange, options || undefined, relations);
  }

  // 和方法products的区别是，这个方法是根据传入的productIds排序
  // 因为直接调用products方法，返回的数据是按照数据库默认排序的 例如：id desc
  @Query()
  @Allow(Permission.Public)
  async productsSortByProductIds(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryProductsArgs,
    @Api() apiType: ApiType,
    @Relations({entity: Product, omit: ['variants']}) relations: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    args.options = args?.options || {};
    if (!args.options?.skip) {
      args.options.skip = Number(process.env.DEFAULT_SKIP ?? 0);
    }
    if (!args.options?.take) {
      args.options.take = Number(process.env.DEFAULT_PAGE_SIZE ?? 50);
    }
    const isShop = apiType === 'shop';
    relations = this.customerProductService.addProductRelations(relations);
    if (isShop) {
      args.options.filter = {
        ...(args.options.filter ?? {}),
        enabled: {eq: true},
      };
    }
    return this.customerProductService.productsSortByProductIds(ctx, args.options || undefined, relations);
  }

  @Query()
  @Allow(Permission.Public)
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, args: QueryProductArgs, relations: RelationPaths<Product>) => {
      if (ctx.apiType === 'shop' && args.id) {
        return CacheKeyManagerService.product(args.id, ctx.channelId);
      }
      return '';
    },
  })
  async product(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryProductArgs,
    @Relations({entity: Product, omit: ['variants']}) relations: RelationPaths<Product>,
  ): Promise<Translated<Product> | undefined> {
    relations = this.customerProductService.addProductRelations(relations);
    if (args.id) {
      let product;
      const cacheKey = CacheKeyManagerService.product(args.id, ctx.channelId);
      if (ctx.apiType === 'shop') {
        const cacheData = this.memoryStorageService.get(cacheKey);
        if (cacheData) {
          product = cacheData as Product;
        }
      }
      if (!product) {
        const qb = this.listQueryBuilder.build(Product, undefined, {
          // TODO 关联的关系必须全部去掉
          relations: [],
          channelId: ctx.channelId,
          where: {deletedAt: IsNull()},
          ctx,
        });
        qb.andWhere(`${qb.alias}.id = :id`, {id: args.id});
        if (ctx.apiType === 'shop') {
          qb.cache(cacheKey, DEFAULT_CACHE_TIMEOUT);
        }
        product = (await qb.take(1).getOne()) as Product;
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(cacheKey, product);
        }
      }
      if (product) {
        return this.translator.translate(product, ctx, ['facetValues', ['facetValues', 'facet']]);
      }
      return;
    } else if (args.slug) {
      return this.productService.findOneBySlug(ctx, args.slug, relations);
    } else {
      throw new UserInputError('error.product-id-or-slug-must-be-provided');
    }
  }
  @ResolveField()
  async collections(
    @Ctx() ctx: RequestContext,
    @Parent() product: Product,
    @Api() apiType: ApiType,
  ): Promise<Array<Translated<Collection>>> {
    const isShop = apiType === 'shop';
    if (product.variants?.length) {
      const variants = product.variants.filter(v => v.deletedAt === null);
      if (!variants) {
        return [];
      }
      let collections = variants.map(v => v.collections ?? []).flat();
      if (isShop) {
        collections = collections.filter(c => c.isPrivate === false);
      }
      return collections.map(collection => this.translator.translate(collection, ctx));
    } else {
      return this.customerProductService.getCollectionsByProductId(ctx, product.id, isShop);
    }
  }

  @ResolveField()
  async optionGroups(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @Info() info: any,
    @Ctx() ctx: RequestContext,
    @Parent() product: Product,
  ): Promise<Array<Translated<ProductOptionGroup>>> {
    if (product.optionGroups?.length) {
      let groups = product.optionGroups;
      // 过滤已经删除的
      groups = groups.filter(g => g.deletedAt === null);
      if (!groups) {
        return [];
      }
      return groups.map(group => this.translator.translate(group, ctx, ['options']));
    } else {
      return this.customerProductService.getOptionGroupsByProductId(ctx, product.id);
    }
  }

  @ResolveField()
  async facetValues(
    @Ctx() ctx: RequestContext,
    @Parent() product: Product,
    @Api() apiType: ApiType,
  ): Promise<Array<Translated<FacetValue>>> {
    if (product.facetValues?.length === 0) {
      return [];
    }
    let facetValues: Array<Translated<FacetValue>>;
    if (product.facetValues?.[0]?.channels) {
      facetValues = product.facetValues as Array<Translated<FacetValue>>;
    } else {
      facetValues = await this.customerProductService.getFacetValuesForProduct(ctx, product.id);
    }
    const filteredFacetValues = await this.facetValueService.findByIds(
      ctx,
      facetValues.map(facetValue => facetValue.id),
    );

    if (apiType === 'shop') {
      return filteredFacetValues.filter(fv => !fv.facet.isPrivate);
    } else {
      return filteredFacetValues;
    }
  }

  @ResolveField()
  async featuredAsset(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<Asset | undefined> {
    if (product.featuredAsset) {
      return product.featuredAsset;
    }
    return this.customerProductService.getFeaturedAsset(ctx, product, 'Product');
  }

  @ResolveField()
  async assets(@Ctx() ctx: RequestContext, @Parent() product: Product): Promise<Asset[] | undefined> {
    return this.customerProductService.getEntityAssets(ctx, product, 'Product');
  }

  @ResolveField()
  productPurchasePermission(@Ctx() ctx: RequestContext, @Parent() product: Product, @Api() apiType: ApiType) {
    const isShowDisable = apiType === 'admin';
    return this.customerProductService.getProductPurchasePermission(ctx, product.id, isShowDisable);
  }

  @ResolveField()
  // 积分兑换信息
  async pointsExchangeInfo(@Ctx() ctx: RequestContext, @Parent() product: Product) {
    return this.pointsProductService.getPointsExchangeInfo(ctx, product.id);
  }
}

@Resolver('Product')
export class CustomShopProductResolver {
  constructor(private customerProductService: CustomerProductService) {}
  @ResolveField()
  @Allow(Permission.Public)
  async productTotalStock(@Ctx() ctx: RequestContext, @Parent() product: Product) {
    return this.customerProductService.getProductTotalStock(ctx, product);
  }
}
