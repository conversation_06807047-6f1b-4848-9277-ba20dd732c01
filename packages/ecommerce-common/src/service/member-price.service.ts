import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {MemberService, MembershipPlan} from '@scmally/member';
import {
  ChannelService,
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  OrderService,
  Product,
  ProductService,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {MemberPrice, MemberPriceProduct, MemberPriceProductVariant} from '../entities';
import {DeletionResult, DiscountType, MemberPriceInput, MemberPriceProductInput} from '../generated-admin-types';
import {CacheService} from './cache.service';
@Injectable()
export class MemberPriceService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private orderService: OrderService,
    private memberService: MemberService,
    private productService: ProductService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
  ) {}
  async getMembershipPlanStr(ctx: RequestContext, memberPriceId: ID) {
    const membershipPlan = await this.connection
      .getRepository(ctx, MembershipPlan)
      .createQueryBuilder('membershipPlan')
      .andWhere('membershipPlan.memberPriceId = :memberPriceId', {memberPriceId})
      .andWhere('membershipPlan.deletedAt is null')
      .getMany();
    return membershipPlan.map(m => m.name).join(',');
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, product: Product) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.productVariantPricesForProduct(product.id, ctx.channelId);
      }
      return '';
    },
  })
  async memberPriceActivityAmount(ctx: RequestContext, product: Product) {
    if (
      !product.variants ||
      product?.variants?.length <= 0 ||
      product?.variants[0]?.productVariantPrices?.length <= 0
    ) {
      const productId = product.id;
      let products: Product[] = [];
      const memoryStorageCacheKey = CacheKeyManagerService.productVariantPricesForProduct(productId, ctx.channelId);
      if (ctx.apiType === 'shop') {
        products = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      if (!products) {
        const qb = this.connection
          .getRepository(ctx, Product)
          .createQueryBuilder('product')
          .leftJoinAndSelect('product.variants', 'variants')
          .leftJoinAndSelect('variants.productVariantPrices', 'productVariantPrices')
          .where('product.id = :productId', {productId});
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
        products = await qb.getMany();
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, products);
        }
      }
      if (products.length <= 0) {
        throw new Error('商品不存在');
      }
      product = products[0];
      if (!product) {
        throw new Error('商品不存在');
      }
      if (product.variants?.length <= 0) {
        return {
          minDiscount: 0,
          maxDiscount: 0,
          minMemberPriceAmount: 0,
          maxMemberPriceAmount: 0,
          memberPriceProductVariant: [],
        };
      }
    }
    const skuIds = product.variants.map(v => v.id);
    let memberPriceId: ID = 0;
    if (ctx.activeUserId) {
      const userMember = await this.memberService.getUserMember(ctx);
      if (userMember) {
        const membershipPlan = userMember.membershipPlan;
        memberPriceId = membershipPlan?.memberPriceId;
        if (!memberPriceId) {
          return {
            minDiscount: 0,
            maxDiscount: 0,
            minMemberPriceAmount: 0,
            maxMemberPriceAmount: 0,
            memberPriceProductVariant: [],
          };
        }
      }
    }
    const memberPriceProductVariant = await this.findMemberPriceBySkuIds(ctx, memberPriceId, skuIds);
    const memberPriceDiscount: number[] = [];
    memberPriceProductVariant.map((item, _index) => {
      const productVariant = product.variants.find(v => v.id === item.productVariantId);
      if (!productVariant) {
        return;
      }
      const productVariantPrice = productVariant.productVariantPrices.find(
        (variantPrice: {channelId: ID}) => variantPrice.channelId === ctx.channelId,
      );
      const price = productVariantPrice?.price;
      let memberPriceAmount = item.memberPriceAmount;
      let discount = price ? price - memberPriceAmount : 0;
      if (item.discountType === DiscountType.FixedPercent) {
        memberPriceAmount = price ? Math.floor(price * (item.memberDiscount / 100)) : memberPriceAmount;
        discount = price ? Math.floor(price - price * (item.memberDiscount / 100)) : 0;
        item.memberPriceAmount = memberPriceAmount;
      }
      memberPriceDiscount.push(discount);
    });
    memberPriceDiscount.sort((a, b) => a - b);

    const minDiscount = memberPriceDiscount.length > 0 ? memberPriceDiscount[0] : 0;
    const maxDiscount = memberPriceDiscount.length > 0 ? memberPriceDiscount[memberPriceDiscount.length - 1] : 0;
    memberPriceProductVariant.sort((a, b) => a.memberPriceAmount - b.memberPriceAmount);
    const minMemberPriceAmount =
      memberPriceProductVariant.length > 0 ? memberPriceProductVariant[0].memberPriceAmount : 0;
    const maxMemberPriceAmount =
      memberPriceProductVariant.length > 0
        ? memberPriceProductVariant[memberPriceProductVariant.length - 1].memberPriceAmount
        : 0;
    return {
      minDiscount: minDiscount ?? 0,
      maxDiscount: maxDiscount ?? 0,
      minMemberPriceAmount: minMemberPriceAmount ?? 0,
      maxMemberPriceAmount: maxMemberPriceAmount ?? 0,
      memberPriceProductVariant: memberPriceProductVariant ?? [],
    };
  }

  async getOrderIsIncludeMemberPrice(ctx: RequestContext, orderId: ID) {
    const userMember = await this.memberService.getUserMember(ctx, true);
    if (!userMember) return false;
    const membershipPlan = userMember.membershipPlan;
    const memberPriceId = membershipPlan?.memberPriceId;
    if (!memberPriceId) {
      return false;
    }
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new Error('订单不存在');
    }
    const linesSkuIds = order.lines.map(l => l.productVariantId);
    const memberPriceProductVariant = await this.findMemberPriceBySkuIds(ctx, memberPriceId, linesSkuIds);
    const memberPriceProductVariantIds = memberPriceProductVariant.map(m => m.productVariantId);
    return memberPriceProductVariantIds.length > 0;
  }

  async memberPriceProductVariants(
    ctx: RequestContext,
    memberPriceProductId: ID,
    options: ListQueryOptions<MemberPriceProductVariant>,
    relations: RelationPaths<MemberPriceProductVariant>,
  ) {
    const qb = this.listQueryBuilder.build(MemberPriceProductVariant, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.memberPriceProduct`, 'memberPriceProduct');
    qb.andWhere(`${qb.alias}.memberPriceProduct.id = :memberPriceProductId`, {memberPriceProductId});
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async upsertMemberPrice(ctx: RequestContext, input: MemberPriceInput) {
    if (!input.name || !input.remark) {
      throw new Error('会员价名称和备注不能为空');
    }
    if (input.id) {
      await this.connection.getRepository(ctx, MemberPrice).update(input.id, {
        name: input.name,
        remark: input.remark,
      });
      await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
      await this.cacheService.removeCache([
        CacheKeyManagerService.memberPrice(input.id, ctx.channelId),
        CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
      ]);
      return this.findOne(ctx, input.id);
    } else {
      let memberPrice = new MemberPrice({
        name: input.name,
        remark: input.remark,
      });
      memberPrice = await this.channelService.assignToCurrentChannel(memberPrice, ctx);
      memberPrice = await this.connection.getRepository(ctx, MemberPrice).save(memberPrice);
      await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
      return memberPrice;
    }
  }

  async memberPrices(
    ctx: RequestContext,
    options: ListQueryOptions<MemberPrice>,
    relations: RelationPaths<MemberPrice>,
  ) {
    const qb = this.listQueryBuilder.build(MemberPrice, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  async findOne(
    ctx: RequestContext,
    memberPriceId: ID,
    options?: ListQueryOptions<MemberPrice>,
    relations?: RelationPaths<MemberPrice>,
  ) {
    const qb = this.listQueryBuilder.build(MemberPrice, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.id = :id`, {id: memberPriceId});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const item = await qb.getMany();
    if (item.length <= 0) {
      throw new Error('未找到会员价活动');
    }
    return item[0];
  }

  async memberPriceProducts(
    ctx: RequestContext,
    memberPriceId: ID,
    collectionId: ID,
    productName: string,
    options: ListQueryOptions<MemberPriceProduct>,
    relations: RelationPaths<MemberPriceProduct>,
  ) {
    const qb = this.listQueryBuilder.build(MemberPriceProduct, options, {
      ctx,
      relations,
    });
    qb.leftJoinAndSelect(`${qb.alias}.product`, 'product');
    qb.leftJoinAndSelect(`${qb.alias}.memberPrice`, 'memberPrice');
    qb.leftJoinAndSelect(`${qb.alias}.memberPriceProductVariant`, 'memberPriceProductVariant');
    qb.leftJoinAndSelect(`memberPriceProductVariant.productVariant`, 'productVariant');
    qb.andWhere(`${qb.alias}.memberPrice.id = :memberPriceId`, {memberPriceId});
    if (collectionId) {
      qb.leftJoinAndSelect(`product.variants`, 'productVariants')
        .leftJoinAndSelect('productVariants.collections', 'collections')
        .andWhere(`productVariants.deletedAt IS NULL`)
        .andWhere(`collections.id = :collectionId`, {collectionId});
    }
    if (productName) {
      qb.leftJoin('product.translations', 'translations').andWhere(`translations.name like :productName`, {
        productName: `%${productName}%`,
      });
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async upsertMemberPriceProduct(ctx: RequestContext, memberPriceId: ID, input: MemberPriceProductInput[]) {
    const memberPrice = await this.findOne(ctx, memberPriceId);
    for (const item of input) {
      const productId = item.productId;
      const memberPriceProductVariants = item.memberPriceProductVariants;
      if (memberPriceProductVariants.length <= 0) {
        throw new Error('会员价商品SKU不能为空');
      }
      let memberPriceProduct = await this.connection.getRepository(ctx, MemberPriceProduct).findOne({
        where: {
          memberPrice: {
            id: memberPrice.id,
          },
          product: {
            id: productId,
          },
        },
      });
      if (!memberPriceProduct) {
        memberPriceProduct = await this.connection.getRepository(ctx, MemberPriceProduct).save(
          new MemberPriceProduct({
            memberPrice: {id: memberPriceId},
            product: {id: productId},
          }),
        );
      }
      for (const memberPriceProductVariant of memberPriceProductVariants) {
        const productVariantId = memberPriceProductVariant.productVariantId;
        const memberPriceAmount = memberPriceProductVariant.memberPriceAmount ?? 0;
        if (memberPriceAmount <= 0) {
          throw new Error('会员价商品价格必须大于0');
        }
        if (memberPriceProductVariant.discountType === DiscountType.FixedAmount) {
          if (
            !memberPriceProductVariant.memberDiscount ||
            memberPriceProductVariant.memberDiscount < 0 ||
            memberPriceProductVariant.memberDiscount > 100
          ) {
            throw new Error('会员折扣必须大于等于0小于等于100');
          }
        }
        const memberPriceProductVariantEntity = await this.connection
          .getRepository(ctx, MemberPriceProductVariant)
          .findOne({
            where: {
              memberPriceProduct: {
                id: memberPriceProduct.id,
              },
              productVariant: {
                id: productVariantId,
              },
            },
          });
        if (memberPriceProductVariantEntity) {
          memberPriceProductVariantEntity.memberPriceAmount = memberPriceAmount;
          memberPriceProductVariantEntity.memberDiscount = memberPriceProductVariant.memberDiscount ?? 0;
          memberPriceProductVariantEntity.discountType =
            memberPriceProductVariant.discountType ?? DiscountType.FixedAmount;
          await this.connection.getRepository(ctx, MemberPriceProductVariant).save(memberPriceProductVariantEntity);
        } else {
          await this.connection.getRepository(ctx, MemberPriceProductVariant).save(
            new MemberPriceProductVariant({
              memberPriceProduct: {id: memberPriceProduct.id},
              productVariant: {id: productVariantId},
              memberPriceAmount,
              memberDiscount: memberPriceProductVariant.memberDiscount ?? 0,
              discountType: memberPriceProductVariant.discountType ?? DiscountType.FixedAmount,
            }),
          );
        }
      }
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.cacheService.removeCache([
      CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
      CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
    ]);
    return this.memberPriceProducts(ctx, memberPriceId, 0, '', {take: 10, skip: 0}, []);
  }
  /**
   * 删除会员价
   * @param ctx
   * @param memberPriceId
   * @returns
   */
  async deleteMemberPrice(ctx: RequestContext, memberPriceId: ID) {
    const memberPrice = await this.connection.getEntityOrThrow(ctx, MemberPrice, memberPriceId);
    await this.connection.getRepository(ctx, MemberPrice).update(memberPrice.id, {
      deletedAt: new Date(),
    });
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.cacheService.removeCache([
      CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
      CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
    ]);
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  /**
   * 删除会员价商品
   * @param ctx
   * @param memberPriceProductIds
   */
  async deleteMemberPriceProduct(ctx: RequestContext, memberPriceProductIds: ID[]) {
    let memberPriceId = '' as ID;
    for (const memberPriceProductId of memberPriceProductIds) {
      const memberPriceProduct = await this.connection.getRepository(ctx, MemberPriceProduct).findOne({
        where: {
          id: memberPriceProductId,
        },
        relations: ['product', 'memberPrice'],
      });
      if (!memberPriceProduct) {
        throw new Error('会员价商品不存在');
      }
      await this.deleteMemberPriceProductVariantByMemberPriceProductId(ctx, memberPriceProduct.id);
      await this.connection.getRepository(ctx, MemberPriceProduct).delete(memberPriceProduct.id);
      if (!memberPriceId) {
        memberPriceId = memberPriceProduct.memberPrice.id;
      }
    }
    if (memberPriceId) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
        CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
      ]);
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  /**
   * 删除会员价商品根据商品id
   * @param ctx
   * @param productId
   */
  async deleteMemberPriceProductByProductId(ctx: RequestContext, productId: ID) {
    await this.connection.getRepository(ctx, MemberPriceProduct).delete({
      product: {
        id: productId,
      },
    });
  }

  /**
   * 删除会员价商品SKU根据商品id
   * @param ctx
   * @param memberPriceProductId
   */
  async deleteMemberPriceProductVariantByMemberPriceProductId(ctx: RequestContext, memberPriceProductId: ID) {
    await this.connection.getRepository(ctx, MemberPriceProductVariant).delete({
      memberPriceProduct: {
        id: memberPriceProductId,
      },
    });
  }

  /**
   * 删除会员价商品SKU
   * @param ctx
   * @param memberPriceProductVariantIds
   */
  async deleteMemberPriceProductVariant(ctx: RequestContext, memberPriceProductVariantIds: ID[]) {
    let memberPriceId = '' as ID;
    for (const memberPriceProductVariantId of memberPriceProductVariantIds) {
      const memberPriceProductVariant = await this.connection
        .getRepository(ctx, MemberPriceProductVariant)
        .createQueryBuilder('memberPriceProductVariant')
        .leftJoinAndSelect('memberPriceProductVariant.memberPriceProduct', 'memberPriceProduct')
        .leftJoinAndSelect('memberPriceProduct.memberPrice', 'memberPrice')
        .leftJoinAndSelect('memberPriceProduct.product', 'product')
        .where('memberPriceProductVariant.id = :memberPriceProductVariantId', {memberPriceProductVariantId})
        .take(1)
        .getOne();
      if (!memberPriceProductVariant) {
        throw new Error('会员价商品SKU不存在');
      }
      const productId = memberPriceProductVariant.memberPriceProduct.product.id;
      await this.connection.getRepository(ctx, MemberPriceProductVariant).delete(memberPriceProductVariantId);
      if (memberPriceId) {
        memberPriceId = memberPriceProductVariant?.memberPriceProduct?.memberPrice?.id;
      }
      const isEmpty = await this.isEmptyMemberPriceProduct(ctx, productId);
      if (isEmpty) {
        await this.deleteMemberPriceProductByProductId(ctx, productId);
      }
    }
    if (memberPriceId) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
        CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
      ]);
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  /**
   * 是否是空的会员价商品
   * @param ctx
   * @param productId 商品id
   * @returns
   */
  async isEmptyMemberPriceProduct(ctx: RequestContext, productId: ID) {
    const memberPriceProduct = await this.connection
      .getRepository(ctx, MemberPriceProduct)
      .createQueryBuilder('memberPriceProduct')
      .leftJoinAndSelect('memberPriceProduct.memberPriceProductVariant', 'memberPriceProductVariant')
      .leftJoinAndSelect('memberPriceProduct.product', 'product')
      .where('product.id = :productId', {productId})
      .take(1)
      .getOne();

    if (!memberPriceProduct?.memberPriceProductVariant) {
      return true;
    }
    return false;
  }

  async setMemberShipPlanMemberPrice(ctx: RequestContext, memberPriceId: ID, memberShipPlanId: ID) {
    const memberPrice = await this.connection.getEntityOrThrow(ctx, MemberPrice, memberPriceId);
    if (!memberPrice) {
      throw new Error('会员价活动不存在');
    }
    await this.connection.getRepository(ctx, MembershipPlan).update(memberShipPlanId, {
      memberPriceId: memberPriceId,
    });
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlan(memberShipPlanId, ctx.channelId),
      CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
      CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId),
    ]);
    return memberPrice;
  }

  // 根据SkuIds查询对应会员价
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, memberPriceId: ID, skuIds: ID[]) => {
      if (ctx.apiType === 'shop') {
        if (memberPriceId) {
          return CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId);
        } else {
          return CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId);
        }
      }
      return '';
    },
  })
  async findMemberPriceBySkuIds(ctx: RequestContext, memberPriceId: ID, skuIds: ID[]) {
    let memberPriceProductVariants: {
      memberPriceAmount: number;
      productVariantId: ID;
      memberDiscount: number;
      discountType: DiscountType;
    }[] = [];
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (memberPriceId) {
        memoryStorageCacheKey = CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.memberPriceNotMemberPriceId(ctx.channelId);
      }
      memberPriceProductVariants = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!memberPriceProductVariants || memberPriceProductVariants.length <= 0) {
      const qb = this.connection
        .getRepository(ctx, MemberPriceProductVariant)
        .createQueryBuilder('memberPriceProductVariant')
        .leftJoinAndSelect('memberPriceProductVariant.productVariant', 'productVariant')
        .leftJoinAndSelect('memberPriceProductVariant.memberPriceProduct', 'memberPriceProduct')
        .leftJoinAndSelect('memberPriceProduct.memberPrice', 'memberPrice')
        .andWhere('memberPrice.deletedAt is null')
        // .andWhere('productVariant.id IN (:...skuIds)', {skuIds})
        .select([
          'memberPriceProductVariant.memberPriceAmount as memberPriceAmount',
          'productVariant.id as productVariantId',
          'memberPriceProductVariant.memberDiscount as memberDiscount',
          'memberPriceProductVariant.discountType as discountType',
        ]);
      if (memberPriceId) {
        qb.andWhere('memberPrice.id = :memberPriceId', {memberPriceId});
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
      } else {
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
      }
      memberPriceProductVariants = await qb.getRawMany<{
        memberPriceAmount: number;
        productVariantId: ID;
        memberDiscount: number;
        discountType: DiscountType;
      }>();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, memberPriceProductVariants);
      }
    }
    // 只需要返回skuIds中存在的数据
    memberPriceProductVariants = memberPriceProductVariants.filter(m => skuIds.includes(m.productVariantId));
    return memberPriceProductVariants;
  }
}
