import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {DiscountActivity, PackageDiscount} from '../entities';
import {ActivityStatus, DiscountActivityInput, ProgramLinkInput} from '../generated-admin-types';
import {ApplicableProduct, ApplicableType, DeletionResult, PromotionType} from '../generated-shop-types';
import {productsDiscountNext} from '../promotion/action/product.discount.next.action';
import {customerGroupList} from '../promotion/conditions';
import {productQuantityContain} from '../promotion/conditions/product.quantity.conditions';
import {ActivityProduct} from '../utils';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {FullDiscountPresentService} from './full-discount-present.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
@Injectable()
export class DiscountActivityService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private commonService: CommonService,
    private promotionService: PromotionService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private fullDiscountPresentService: FullDiscountPresentService,
    private requestContextService: RequestContextService,
    private kvsService: KvsService,
    private promotionResultDetailService: PromotionResultDetailService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private productCustomService: ProductCustomService,
  ) {}

  @cacheableAccess({
    cacheKeyFn: (
      ctx: RequestContext,
      discountActivityId: ID,
      options?: ListQueryOptions<DiscountActivity>,
      relations?: RelationPaths<DiscountActivity>,
    ) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.discountActivity(discountActivityId, ctx.channelId);
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    discountActivityId: ID,
    options?: ListQueryOptions<DiscountActivity>,
    relations?: RelationPaths<DiscountActivity>,
  ) {
    let discountActivities: DiscountActivity[] | undefined;
    const memoryStorageCacheKey = CacheKeyManagerService.discountActivity(discountActivityId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      discountActivities = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!discountActivities) {
      const qb = this.listQueryBuilder.build(DiscountActivity, options, {
        relations: [], // (relations ?? []).concat(['promotion']),
        ctx,
        channelId: ctx.channelId,
      });
      qb.andWhere(`${qb.alias}.id = :discountActivityId`, {discountActivityId});
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      discountActivities = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, discountActivities);
      }
    }
    if (discountActivities.length > 0) {
      const discountActivity = discountActivities[0];
      const promotionId = discountActivity.promotionId;
      if (promotionId) {
        const promotionMemoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        const cache =
          ctx.apiType === 'shop'
            ? {
                id: promotionMemoryStorageCacheKey,
                milliseconds: DEFAULT_CACHE_TIMEOUT,
              }
            : false;
        let promotion: Promotion | undefined | null;
        if (ctx.apiType === 'shop') {
          promotion = this.memoryStorageService.get(promotionMemoryStorageCacheKey);
        }
        if (!promotion) {
          promotion = await this.connection.getRepository(ctx, Promotion).findOne({
            where: {id: promotionId},
            cache: cache,
          });
          if (ctx.apiType === 'shop') {
            this.memoryStorageService.set(promotionMemoryStorageCacheKey, promotion);
          }
        }
        discountActivity.promotion = promotion as Promotion;
      }
      return discountActivity;
    }
    return null;
  }
  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<DiscountActivity>,
    relations: RelationPaths<DiscountActivity>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(DiscountActivity, options, {
      relations: relations ?? ['promotion'],
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          PromotionType.DiscountActivity,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }

  async upsertDiscountActivity(ctx: RequestContext, input: DiscountActivityInput) {
    await this.validate(ctx, input);
    let status = ActivityStatus.Normal;
    if (new Date() < new Date(input.startTime)) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date() > new Date(input.endTime)) {
      status = ActivityStatus.HaveEnded;
    }
    // 修改前的可用商品
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const discountActivity = await this.findOne(ctx, input.id);
      if (!discountActivity) {
        throw new EntityNotFoundError('活动不存在', input.id);
      }
      oldApplicableProduct = {
        applicableType: ApplicableType.AvailableGoods,
        productIds: discountActivity.productIds as string[],
      };
    }
    let discountActivity = new DiscountActivity({
      ...(input as DiscountActivity),
      status,
    });
    discountActivity = await this.channelService.assignToCurrentChannel(discountActivity, ctx);
    discountActivity = await this.connection.getRepository(ctx, DiscountActivity).save(discountActivity);
    const promotion = await this.upsertPromotion(ctx, discountActivity);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      {
        applicableType: ApplicableType.AvailableGoods,
        productIds: discountActivity.productIds as string[],
      },
      oldApplicableProduct,
    );
    discountActivity.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, DiscountActivity).save(discountActivity);
    if (discountActivity.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.discountActivity(discountActivity.id, ctx.channelId),
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return this.findOne(ctx, discountActivity.id);
  }
  async upsertPromotion(ctx: RequestContext, discountActivity: DiscountActivity) {
    const promotionInput = {
      couponCode: discountActivity.promotion ? discountActivity.promotion.couponCode : generatePublicId(),
      name: discountActivity.displayName,
      startsAt: discountActivity.startTime,
      endsAt: discountActivity.endTime,
      enabled:
        discountActivity.status === ActivityStatus.Normal || discountActivity.status === ActivityStatus.NotStarted,
      conditions: [],
      actions: [],
      customFields: {
        type: PromotionType.DiscountActivity,
        isAutomatic: true,
        activityName: discountActivity.displayName,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: discountActivity.displayName,
        },
      ],
    };
    promotionInput.conditions.push({
      code: productQuantityContain.code,
      arguments: [
        {name: 'productIds', value: discountActivity.productIds},
        {name: 'quantity', value: discountActivity.minimum},
        {name: 'type', value: ApplicableType.AvailableGoods},
      ],
    } as never);
    if (discountActivity.whetherRestrictUsers) {
      //是否限制参与用户 创建满减送的参与用户的条件
      promotionInput.conditions.push({
        code: customerGroupList.code,
        arguments: [
          {name: 'isOpen', value: discountActivity.whetherRestrictUsers},
          {name: 'groupType', value: discountActivity.groupType},
          {name: 'customerGroupIds', value: discountActivity.memberPlanIds},
        ],
      } as never);
    }
    promotionInput.actions.push({
      code: productsDiscountNext.code,
      arguments: [
        {name: 'discount', value: discountActivity.discount},
        {name: 'productIds', value: discountActivity.productIds},
      ],
    } as never);
    discountActivity = (await this.findOne(ctx, discountActivity.id, {}, ['promotion'])) as DiscountActivity;
    if (discountActivity?.promotion) {
      const updatePromotion = {
        ...promotionInput,
        id: discountActivity.promotion.id,
        customFields: {
          type: PromotionType.DiscountActivity,
          isAutomatic: true,
          activityName: discountActivity.displayName,
          stackingDiscountSwitch: discountActivity.stackingDiscountSwitch,
          stackingPromotionTypes: discountActivity.stackingPromotionTypes,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: PromotionType.DiscountActivity,
          isAutomatic: true,
          activityName: discountActivity.displayName,
          stackingDiscountSwitch: discountActivity.stackingDiscountSwitch,
          stackingPromotionTypes: discountActivity.stackingPromotionTypes,
        },
      });
      return promotion;
    }
  }

  async failureDiscountActivity(ctx: RequestContext, discountActivityId: ID) {
    const discountActivity = await this.findOne(ctx, discountActivityId);
    if (!discountActivity) {
      throw new Error(`DiscountActivity not found with id ${discountActivityId}`);
    }
    discountActivity.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(discountActivity.promotion.id, {enabled: false});
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, DiscountActivity).save(discountActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.discountActivity(discountActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(discountActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(discountActivity.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, {
      applicableType: ApplicableType.AvailableGoods,
      productIds: discountActivity.productIds as string[],
    });
    return this.findOne(ctx, discountActivityId);
  }

  async validate(ctx: RequestContext, input: DiscountActivityInput) {
    if (input.startTime > input.endTime) {
      throw new Error('活动开始时间不能大于活动结束时间');
    }
    if (input.minimum < 0) {
      throw new Error('使用门槛不能小于0');
    }
    if (input.discount < 0 || input.discount >= 100) {
      throw new Error('折扣范围为0-9.9');
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      productIds: input.productIds,
      applicableType: ApplicableType.AvailableGoods,
    };
    // 验证活动商品和满减送是否冲突
    await this.fullDiscountPresentService.verifyProductIdsAndFullDiscountConflict(ctx, inputProduct);
    // 验证活动商品和打包一口价活动是否冲突
    await this.fullDiscountPresentService.verifyProductIdsAndPackageDiscountConflict(ctx, inputProduct);
    // 验证活动商品和第X件X折活动是否冲突
    await this.fullDiscountPresentService.verifyProductIdsAndDiscountActivityConflict(
      ctx,
      inputProduct,
      input.id as ID,
    );
  }

  async getDiscountActivityLink(ctx: RequestContext, input: ProgramLinkInput) {
    const discountActivityId = input.id;
    const discountActivity = await this.findOne(ctx, discountActivityId);
    if (!discountActivity) {
      throw new EntityNotFoundError('DiscountActivity', discountActivityId);
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(discountActivity.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }

  async getDiscountActivityQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const discountActivityId = input.id;
    const discountActivity = await this.findOne(ctx, discountActivityId);
    if (!discountActivity) {
      throw new EntityNotFoundError('DiscountActivity', discountActivityId);
    }
    if (discountActivity.smallProgramQRCodeLink) {
      return discountActivity.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(discountActivity.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    discountActivity.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, DiscountActivity).save(discountActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.discountActivity(discountActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(discountActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(discountActivity.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }
  async deleteDiscountActivity(ctx: RequestContext, discountActivityId: ID) {
    const discountActivity = await this.findOne(ctx, discountActivityId);
    if (!discountActivity) {
      throw new Error(`DiscountActivity with id ${discountActivityId} not found`);
    }
    discountActivity.deletedAt = new Date();
    discountActivity.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(discountActivity.promotion.id, {enabled: false});
    await this.promotionService.softDeletePromotion(ctx, discountActivity.promotion.id);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, DiscountActivity).save(discountActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.discountActivity(discountActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(discountActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(discountActivity.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, {
      applicableType: ApplicableType.AvailableGoods,
      productIds: discountActivity.productIds as string[],
    });
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  async findOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const discountActivity = await this.connection
      .getRepository(ctx, DiscountActivity)
      .createQueryBuilder('discountActivity')
      .leftJoin('discountActivity.channels', 'channel')
      .leftJoinAndSelect('discountActivity.promotion', 'promotion')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    return discountActivity;
  }

  activitySynopsis(ctx: RequestContext, discountActivity: DiscountActivity) {
    let synopsisStr = ``;
    if (discountActivity.discount === 0) {
      //每满X件，第X件0元
      synopsisStr = `每满${discountActivity.minimum}件，第${discountActivity.minimum}件0元`;
    } else if (discountActivity.discount === 50) {
      synopsisStr = `每满${discountActivity.minimum}件，第${discountActivity.minimum}件半价`;
    } else {
      synopsisStr = `每满${discountActivity.minimum}件，第${discountActivity.minimum}件${
        discountActivity.discount / 10
      }折`;
    }
    return synopsisStr;
  }

  activityContent(ctx: RequestContext, discountActivity: DiscountActivity) {
    let contentStr = ``;
    if (discountActivity.discount === 0) {
      contentStr = `活动商品范围内，购买${discountActivity.minimum}件,价格最低的那件享0元优惠`;
    } else if (discountActivity.discount === 50) {
      contentStr = `活动商品范围内，购买${discountActivity.minimum}件,价格最低的那件享半价优惠`;
    } else {
      contentStr = `活动商品范围内，购买${discountActivity.minimum}件,价格最低的那件享${
        discountActivity.discount / 10
      }折优惠`;
    }
    return [contentStr, `此优惠可无限循环，买得越多优惠越多`];
  }

  activitySuperposition(stackingDiscountSwitch: boolean, stackingPromotionTypes: PromotionType[]) {
    if (stackingDiscountSwitch) {
      //活动可与XXX、XXXX同时使用
      const stackingPromotionTypesStr: string[] = [];
      stackingPromotionTypes.map((item, index) => {
        if (item === PromotionType.FullDiscountPresent) {
          stackingPromotionTypesStr[index] = '满减送';
        } else if (item === PromotionType.DiscountActivity) {
          stackingPromotionTypesStr[index] = '第X件X折';
        } else if (item === PromotionType.Member) {
          stackingPromotionTypesStr[index] = '会员折扣';
        } else if (item === PromotionType.Coupon) {
          stackingPromotionTypesStr[index] = '优惠券';
        } else if (item === PromotionType.PurchaseAtAPremium) {
          stackingPromotionTypesStr[index] = '加价购';
        } else if (item === PromotionType.PackageDiscount) {
          stackingPromotionTypesStr[index] = '打包一口价';
        } else if (item === PromotionType.ActuallyPaid) {
          stackingPromotionTypesStr[index] = '实付满赠';
        } else if (item === PromotionType.SelectiveGift) {
          stackingPromotionTypesStr[index] = '任选满赠';
        } else if (item === PromotionType.PaymentReward) {
          stackingPromotionTypesStr[index] = '支付有礼';
        } else if (item === PromotionType.ShoppingCreditsClaim) {
          stackingPromotionTypesStr[index] = '购物金获取';
        } else if (item === PromotionType.ShoppingCreditsDeduction) {
          stackingPromotionTypesStr[index] = '购物金抵扣';
        }
      });
      return `活动可与${stackingPromotionTypesStr.join('、')}同时使用`;
    } else {
      return `不可与其它优惠活动同时使用`;
    }
  }

  async packageOnePriceAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.packageOnePrice(ctx);
    }
  }

  async packageOnePrice(ctx: RequestContext) {
    const packageOnePrice = await this.connection
      .getRepository(ctx, PackageDiscount)
      .createQueryBuilder('packageDiscount')
      .leftJoinAndSelect('packageDiscount.channels', 'channel')
      .leftJoinAndSelect('packageDiscount.promotion', 'promotion')
      .andWhere('packageDiscount.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('packageDiscount.startTime <= :startTime', {startTime: new Date()})
      .andWhere('packageDiscount.endTime > :endTime', {endTime: new Date()})
      .getMany();
    const packageOnePriceIds = packageOnePrice.map(item => item.id);
    const packageOnePricePromotionIds = packageOnePrice.map(item => item.promotion.id);
    if (packageOnePriceIds && packageOnePriceIds.length > 0) {
      for (const item of packageOnePrice) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, PackageDiscount)
        .update({id: In(packageOnePriceIds)}, {status: ActivityStatus.Normal});
      const packageOnePriceKeys = packageOnePriceIds.map(id =>
        CacheKeyManagerService.packageDiscount(id, ctx.channelId),
      );
      await this.cacheService.removeCache(packageOnePriceKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      packageOnePrice.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, {
          applicableType: ApplicableType.AvailableGoods,
          productIds: item.productIds as string[],
        });
      });
    }
    if (packageOnePricePromotionIds && packageOnePricePromotionIds.length > 0) {
      await this.connection
        .getRepository(ctx, Promotion)
        .update({id: In(packageOnePricePromotionIds)}, {enabled: true});
      const packageOnePricePromotionKeys = [
        ...packageOnePricePromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...packageOnePricePromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(packageOnePricePromotionKeys);
    }
    const haveEnded = await this.connection
      .getRepository(ctx, PackageDiscount)
      .createQueryBuilder('packageDiscount')
      .leftJoinAndSelect('packageDiscount.channels', 'channel')
      .leftJoinAndSelect('packageDiscount.promotion', 'promotion')
      .andWhere('packageDiscount.status = :status', {status: ActivityStatus.Normal})
      .andWhere('packageDiscount.endTime <= :endTime', {endTime: new Date()})
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    const haveEndedPromotionIds = haveEnded.map(item => item.promotion.id);
    if (haveEndedIds && haveEndedIds.length > 0) {
      for (const item of haveEnded) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, PackageDiscount)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
      const haveEndedKeys = haveEndedIds.map(id => CacheKeyManagerService.packageDiscount(id, ctx.channelId));
      await this.cacheService.removeCache(haveEndedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      haveEnded.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, {
          applicableType: ApplicableType.AvailableGoods,
          productIds: item.productIds as string[],
        });
      });
    }
    if (haveEndedPromotionIds && haveEndedPromotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(haveEndedPromotionIds)}, {enabled: false});
      const haveEndedPromotionKeys = [
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(haveEndedPromotionKeys);
    }
  }

  async xPiecesYFoldAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.xPiecesYFold(ctx);
    }
  }

  async xPiecesYFold(ctx: RequestContext) {
    const notYetStarted = await this.connection
      .getRepository(ctx, DiscountActivity)
      .createQueryBuilder('discountActivity')
      .leftJoinAndSelect('discountActivity.channels', 'channel')
      .leftJoinAndSelect('discountActivity.promotion', 'promotion')
      .andWhere('discountActivity.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('discountActivity.startTime <= :startTime', {startTime: new Date()})
      .andWhere('discountActivity.endTime > :endTime', {endTime: new Date()})
      .getMany();
    const notYetStartedIds = notYetStarted.map(item => item.id);
    const notYetStartedPromotionIds = notYetStarted.map(item => item.promotion.id);
    if (notYetStartedIds && notYetStartedIds.length > 0) {
      for (const item of notYetStarted) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, DiscountActivity)
        .update({id: In(notYetStartedIds)}, {status: ActivityStatus.Normal});
      const notYetStartedKeys = notYetStarted.map(item =>
        CacheKeyManagerService.discountActivity(item.id, ctx.channelId),
      );
      await this.cacheService.removeCache(notYetStartedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      notYetStarted.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, {
          applicableType: ApplicableType.AvailableGoods,
          productIds: item.productIds as string[],
        });
      });
    }
    if (notYetStartedPromotionIds && notYetStartedPromotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(notYetStartedPromotionIds)}, {enabled: true});
      const notYetStartedPromotionKeys = [
        ...notYetStartedPromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...notYetStartedPromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(notYetStartedPromotionKeys);
    }
    const haveEnded = await this.connection
      .getRepository(ctx, DiscountActivity)
      .createQueryBuilder('discountActivity')
      .leftJoinAndSelect('discountActivity.channels', 'channel')
      .leftJoinAndSelect('discountActivity.promotion', 'promotion')
      .andWhere('discountActivity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('discountActivity.endTime <= :endTime', {endTime: new Date()})
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    const haveEndedPromotionIds = haveEnded.map(item => item.promotion.id);
    if (haveEndedIds && haveEndedIds.length > 0) {
      for (const item of haveEnded) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, DiscountActivity)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
      const haveEndedKeys = haveEnded.map(item => CacheKeyManagerService.discountActivity(item.id, ctx.channelId));
      await this.cacheService.removeCache(haveEndedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      haveEnded.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, {
          applicableType: ApplicableType.AvailableGoods,
          productIds: item.productIds as string[],
        });
      });
    }
    if (haveEndedPromotionIds && haveEndedPromotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(haveEndedPromotionIds)}, {enabled: false});
      const haveEndedPromotionKeys = [
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(haveEndedPromotionKeys);
    }
  }
}
