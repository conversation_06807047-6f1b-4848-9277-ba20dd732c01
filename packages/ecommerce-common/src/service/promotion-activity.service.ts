import {Injectable} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {MembershipPlan} from '@scmally/member';
import {
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Product,
  ProductService,
  Promotion,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {IsNull} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {
  Coupon,
  DiscountActivity,
  FullDiscountPresent,
  PackageDiscount,
  PaymentRewardActivity,
  PurchasePremium,
  SelectiveGiftActivity,
  ShoppingCreditsClaimActivity,
  ShoppingCreditsDeductionActivity,
} from '../entities';
import {
  ActivityStatus,
  ApplicableProduct,
  ApplicableType,
  CouponState,
  FullDiscountPresentType,
  PromotionCustomFields,
  PromotionType,
} from '../generated-shop-types';
import {CustomerProductService} from './custom-product.service';
import {PointsProductService} from './points-product.service';
@Injectable()
export class PromotionActivityService {
  constructor(
    private connection: TransactionalConnection,
    private productService: ProductService,
    private customerProductService: CustomerProductService,
    private listQueryBuilder: ListQueryBuilder,
    private memoryStorageService: MemoryStorageService,
    private pointsProductService: PointsProductService,
  ) {}

  async areAllEligibleProductsInactiveOrSoldOut(ctx: RequestContext, promotionId: ID, promotionType: PromotionType) {
    const applicableProduct: ApplicableProduct[] = [];
    switch (promotionType) {
      case PromotionType.Coupon:
        {
          const coupon = await this.connection.getRepository(ctx, Coupon).findOne({where: {id: promotionId}});
          if (!coupon) throw new Error('活动不存在');
          applicableProduct.push(coupon.applicableProduct);
        }
        break;
      case PromotionType.PurchaseAtAPremium:
        {
          const purchasePremium = await this.connection.getRepository(ctx, PurchasePremium).findOne({
            where: {id: promotionId},
            relations: ['purchasePremiumProducts', 'purchasePremiumProducts.product'],
          });
          if (!purchasePremium) throw new Error('活动不存在');
          if (!purchasePremium.purchasePremiumProducts || purchasePremium.purchasePremiumProducts.length <= 0) {
            return true;
          } else {
            const purchasePremiumProducts = purchasePremium.purchasePremiumProducts.filter(item => item.enabled);
            if (purchasePremiumProducts.length <= 0) {
              return true;
            }
            const purchasePremiumApplicableProduct = {
              applicableType: ApplicableType.AvailableGoods,
              productIds: purchasePremiumProducts.map(item => item.product.id),
            } as ApplicableProduct;
            applicableProduct.push(purchasePremiumApplicableProduct);
          }
          applicableProduct.push(purchasePremium.applicableProduct);
        }
        break;
      case PromotionType.DiscountActivity:
        {
          const discountActivity = await this.connection.getRepository(ctx, DiscountActivity).findOne({
            where: {id: promotionId},
          });
          if (!discountActivity) throw new Error('活动不存在');
          applicableProduct.push({
            applicableType: ApplicableType.AvailableGoods,
            productIds: discountActivity.productIds,
          } as ApplicableProduct);
        }
        break;
      case PromotionType.FullDiscountPresent:
      case PromotionType.ActuallyPaid:
        {
          const fullDiscountPresent = await this.connection.getRepository(ctx, FullDiscountPresent).findOne({
            where: {id: promotionId},
          });
          if (!fullDiscountPresent) throw new Error('活动不存在');
          if (fullDiscountPresent.ruleValues) {
            let productIds: ID[] = [];
            for (const ruleValue of fullDiscountPresent.ruleValues) {
              const productId = (ruleValue?.freeGiftValues?.map(item => item?.freeGiftProductId) ?? []) as ID[];
              productIds = productIds.concat(productId);
            }
            if (productIds.length > 0) {
              applicableProduct.push({
                applicableType: ApplicableType.AvailableGoods,
                productIds: productIds,
                isGift: true,
              } as ApplicableProduct);
            }
          }
          applicableProduct.push(fullDiscountPresent.applicableProduct);
        }
        break;
      case PromotionType.PackageDiscount:
        {
          const packageDiscount = await this.connection.getRepository(ctx, PackageDiscount).findOne({
            where: {id: promotionId},
          });
          if (!packageDiscount) throw new Error('活动不存在');
          applicableProduct.push({
            applicableType: ApplicableType.AvailableGoods,
            productIds: packageDiscount.productIds,
          } as ApplicableProduct);
        }
        break;
      case PromotionType.SelectiveGift:
        {
          const selectiveGift = await this.connection.getRepository(ctx, SelectiveGiftActivity).findOne({
            where: {id: promotionId},
          });
          if (!selectiveGift) throw new Error('活动不存在');
          const ruleValues = selectiveGift.ruleValues;
          if (ruleValues) {
            let productIds: ID[] = [];
            for (const ruleValue of ruleValues) {
              const productId = (ruleValue?.freeGiftValues?.map(item => item?.freeGiftProductId) ?? []) as ID[];
              productIds = productIds.concat(productId);
            }
            applicableProduct.push({
              applicableType: ApplicableType.AvailableGoods,
              productIds: productIds,
              isGift: true,
            } as ApplicableProduct);
          }
          applicableProduct.push(selectiveGift.applicableProduct);
        }
        break;
      default:
        return;
    }
    for (const item of applicableProduct) {
      const productAvailabilityCount = await this.productAvailabilityCount(ctx, item);
      if (productAvailabilityCount <= 0) {
        return true;
      }
    }
    return false;
  }
  async productAvailabilityCount(ctx: RequestContext, applicableProduct: ApplicableProduct) {
    const qb = this.listQueryBuilder.build(
      Product,
      {},
      {
        relations: [],
        channelId: ctx.channelId,
        where: {deletedAt: IsNull()},
        ctx,
      },
    );
    if (applicableProduct.applicableType === ApplicableType.AvailableGoods) {
      if (!applicableProduct.productIds || applicableProduct.productIds.length === 0) return 0;
      qb.andWhere(`${qb.alias}.id IN (:...productIds)`, {productIds: applicableProduct.productIds});
    } else if (applicableProduct.applicableType === ApplicableType.UnusableGoods) {
      if (applicableProduct.productIds && applicableProduct.productIds.length > 0) {
        qb.andWhere(`${qb.alias}.id NOT IN (:...productIds)`, {productIds: applicableProduct.productIds});
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((applicableProduct as any).isGift) {
      qb.andWhere(`${qb.alias}.customFieldsFreegift = true`);
    } else {
      qb.andWhere(`${qb.alias}.customFieldsFreegift != true`);
    }
    qb.leftJoin(`${qb.alias}.variants`, 'productVariants');
    qb.andWhere(`productVariants.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.enabled = true`);
    qb.leftJoin(`productVariants.stockLevels`, 'stockLevels');
    qb.andWhere(`productVariants.deletedAt IS NULL`);
    qb.andWhere(`stockLevels.stockOnHand > stockLevels.stockAllocated`);
    const count = await qb.getCount();
    return count;
  }

  /**
   * 获取活动详情
   * @param ctx
   * @param id
   * @returns
   */
  async productActivitiesFindOne(ctx: RequestContext, id: ID, isIncludeFailure = false) {
    const promotion = await this.connection.getRepository(ctx, Promotion).findOne({where: {id: id}});
    if (!promotion) throw new Error('活动不存在');
    const type = (promotion.customFields as PromotionCustomFields).type;
    if (!type) throw new Error('活动类型不存在');
    if (type === PromotionType.Coupon) {
      const coupon = await this.couponFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!coupon) return;
      return coupon;
    } else if (type === PromotionType.Member) {
      const member = await this.membershipPlanFindOneByPromotionId(ctx, id);
      if (!member) return;
      return member;
    } else if (type === PromotionType.PurchaseAtAPremium) {
      const purchaseAtAPremium = await this.purchasePremiumFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!purchaseAtAPremium) return;
      return purchaseAtAPremium;
    } else if (type === PromotionType.DiscountActivity) {
      const discountActivity = await this.discountActivityFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!discountActivity) return;
      return discountActivity;
    } else if (type === PromotionType.FullDiscountPresent || type === PromotionType.ActuallyPaid) {
      const fullDiscountPresent = await this.fullDiscountPresentFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!fullDiscountPresent) return;
      return fullDiscountPresent;
    } else if (type === PromotionType.PackageDiscount) {
      const packageDiscount = await this.packageDiscountFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!packageDiscount) return;
      return packageDiscount;
    } else if (type === PromotionType.SelectiveGift) {
      const selectiveGift = await this.selectiveGiftFindOneByPromotionId(ctx, id, isIncludeFailure);
      if (!selectiveGift) return;
      return selectiveGift;
    } else if (type === PromotionType.ShoppingCreditsClaim) {
      const shoppingCreditsClaim = await this.shoppingCreditsClaimActivityFindOneByPromotionId(
        ctx,
        id,
        isIncludeFailure,
      );
      if (!shoppingCreditsClaim) return;
      return shoppingCreditsClaim;
    } else if (type === PromotionType.ShoppingCreditsDeduction) {
      const shoppingCreditsDeduction = await this.shoppingCreditsDeductionFindOneByPromotionId(
        ctx,
        id,
        isIncludeFailure,
      );
      if (!shoppingCreditsDeduction) return;
      return shoppingCreditsDeduction;
    } else {
      Logger.error('活动类型不存在');
      return;
    }
  }

  async productActivitiesFindAll(ctx: RequestContext, productId: ID, promotions: Promotion[]) {
    const result = [];
    for (const promotion of promotions) {
      const activities = await this.productActivitiesFindOne(ctx, promotion.id);
      if (activities) {
        result.push(activities);
      }
    }
    return result;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async couponFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, Coupon)
      .createQueryBuilder('coupon')
      .leftJoin('coupon.channels', 'channel')
      .leftJoinAndSelect('coupon.promotion', 'promotion')
      .where('promotion.id=:promotionId', {promotionId})
      .andWhere('channel.id=:channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('coupon.state != :state', {state: CouponState.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }

    const coupon = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, coupon);
    }
    return coupon;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async discountActivityFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, DiscountActivity)
      .createQueryBuilder('discountActivity')
      .leftJoinAndSelect('discountActivity.promotion', 'promotion')
      .leftJoin('discountActivity.channels', 'channel')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('discountActivity.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const discountActivity = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, discountActivity);
    }
    return discountActivity;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async packageDiscountFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, PackageDiscount)
      .createQueryBuilder('packageDiscount')
      .leftJoinAndSelect('packageDiscount.promotion', 'promotion')
      .leftJoin('packageDiscount.channels', 'channel')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('packageDiscount.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const packageDiscount = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, packageDiscount);
    }
    return packageDiscount;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async shoppingCreditsDeductionFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .createQueryBuilder('shoppingCreditsDeduction')
      .leftJoinAndSelect('shoppingCreditsDeduction.promotion', 'promotion')
      .leftJoin('shoppingCreditsDeduction.channels', 'channel')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('shoppingCreditsDeduction.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const shoppingCreditsDeduction = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, shoppingCreditsDeduction);
    }
    return shoppingCreditsDeduction;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, id: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(id, ctx.channelId);
        }
      }
      return '';
    },
  })
  async shoppingCreditsClaimActivityFindOneByPromotionId(ctx: RequestContext, id: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(id, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .createQueryBuilder('shoppingCreditsClaim');
    query.leftJoinAndSelect('shoppingCreditsClaim.promotion', 'promotion');
    query.leftJoin('shoppingCreditsClaim.channels', 'channel');
    query.where('promotion.id = :promotionId', {promotionId: id});
    query.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('shoppingCreditsClaim.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const shoppingCreditsClaim = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, shoppingCreditsClaim);
    }
    return shoppingCreditsClaim;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async selectiveGiftFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, SelectiveGiftActivity)
      .createQueryBuilder('selectiveGiftActivity')
      .leftJoinAndSelect('selectiveGiftActivity.promotion', 'promotion')
      .leftJoin('selectiveGiftActivity.channels', 'channel')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('selectiveGiftActivity.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const selectiveGift = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, selectiveGift);
    }
    return selectiveGift;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async fullDiscountPresentFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, FullDiscountPresent)
      .createQueryBuilder('fullDiscountPresent')
      .leftJoinAndSelect('fullDiscountPresent.promotion', 'promotion')
      .leftJoin('fullDiscountPresent.channels', 'channel')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (!isIncludeFailure) {
      query.andWhere('fullDiscountPresent.status != :status', {status: ActivityStatus.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const fullDiscountPresent = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, fullDiscountPresent);
    }
    return fullDiscountPresent;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID, isIncludeFailure = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeFailure) {
          return CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
        } else {
          return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async purchasePremiumFindOneByPromotionId(ctx: RequestContext, promotionId: ID, isIncludeFailure = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        memoryStorageCacheKey = CacheKeyManagerService.promotionIncludeFailure(promotionId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const query = this.connection
      .getRepository(ctx, PurchasePremium)
      .createQueryBuilder('purchasePremium')
      .leftJoinAndSelect('purchasePremium.promotion', 'promotion')
      .leftJoinAndSelect('purchasePremium.purchasePremiumProducts', 'purchasePremiumProducts')
      .leftJoinAndSelect('purchasePremiumProducts.product', 'product')
      .leftJoinAndSelect('product.variants', 'variants')
      .leftJoinAndSelect('product.featuredAsset', 'featuredAsset')
      .leftJoin('purchasePremium.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.id=:promotionId', {promotionId});
    if (!isIncludeFailure) {
      query.andWhere('purchasePremium.state != :state', {state: CouponState.Failure});
    }
    if (ctx.apiType === 'shop') {
      if (isIncludeFailure) {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        query.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const purchasePremium = await query.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, purchasePremium);
    }
    return purchasePremium;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
      }
      return '';
    },
  })
  async membershipPlanFindOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const memoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const qb = this.connection
      .getRepository(ctx, MembershipPlan)
      .createQueryBuilder('membershipPlan')
      .leftJoinAndSelect('membershipPlan.promotion', 'promotion')
      .leftJoin('membershipPlan.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.id = :promotionId', {promotionId});
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const memberShipPlan = await qb.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, memberShipPlan);
    }
    return memberShipPlan;
  }

  async activityUsableProducts(
    ctx: RequestContext,
    promotionType: PromotionType,
    startTime: Date,
    endTime: Date,
    isUsable: boolean,
    collectionId: ID,
    options: ListQueryOptions<Product>,
    relations: RelationPaths<Product>,
  ) {
    if (promotionType === PromotionType.PurchaseAtAPremium) {
      //获取互斥的加价购活动
      const purchasePremium = await this.getConflictingPurchasePremium(ctx, startTime, endTime);
      const applicableProducts = purchasePremium.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (
      promotionType === PromotionType.DiscountActivity ||
      promotionType === PromotionType.FullDiscountPresent ||
      promotionType === PromotionType.PackageDiscount
    ) {
      //获取互斥的第几件几折活动
      const discountActivity = await this.getConflictingDiscountActivity(ctx, startTime, endTime);
      //把第几件几折活动的商品数据拼成和满赠活动一样的格式
      const discountApplicableProducts = discountActivity.map(
        item =>
          ({
            applicableType: ApplicableType.AvailableGoods,
            productIds: item.productIds,
          } as ApplicableProduct),
      );
      let applicableProducts = discountApplicableProducts;
      // 如果不是打包一口价活动，才获取满减送活动的商品
      if (promotionType !== PromotionType.PackageDiscount) {
        //获取互斥的满减送活动
        const fullDiscountPresent = await this.getConflictingFullDiscountPresent(ctx, startTime, endTime);
        const fullDiscountPresentApplicableProducts = fullDiscountPresent.map(item => item.applicableProduct);
        //合并满减活动和满赠的商品数据
        applicableProducts = applicableProducts.concat(fullDiscountPresentApplicableProducts);
      }

      //不是满减送活动，才获取打包一口价活动商品
      if (promotionType !== PromotionType.FullDiscountPresent) {
        // 获取打包一口价的活动
        const packageDiscount = await this.getPackageDiscount(ctx, startTime, endTime);
        const packageDiscountApplicableProducts = packageDiscount.map(
          item =>
            ({
              applicableType: ApplicableType.AvailableGoods,
              productIds: item.productIds,
            } as ApplicableProduct),
        );
        //合并打包一口价的商品数据
        applicableProducts = applicableProducts.concat(packageDiscountApplicableProducts);
      }
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.ActuallyPaid) {
      //获取互斥的满减送活动
      const fullDiscountPresent = await this.getConflictingFullDiscountPresent(ctx, startTime, endTime, true);
      const applicableProducts = fullDiscountPresent.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.MemberPrice) {
      const applicableProducts = [
        {
          applicableType: ApplicableType.All,
          productIds: [],
        },
      ];
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.SelectiveGift) {
      //获取互斥的任选赠活动
      const selectiveGifts = await this.getConflictingSelectiveGifts(ctx, startTime, endTime);
      const applicableProducts = selectiveGifts.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.PaymentReward) {
      const paymentReward = await this.paymentRewardActivityConflict(ctx, startTime, endTime);
      const applicableProducts = paymentReward.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.PointsExchange) {
      const productIds = await this.pointsProductService.getPointsExchangeProductIds(ctx, startTime, endTime);
      const applicableProducts = [
        {
          applicableType: ApplicableType.AvailableGoods,
          productIds,
        } as ApplicableProduct,
      ];
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.BlindBox) {
      const applicableProducts = [
        {
          applicableType: ApplicableType.All,
          productIds: [],
        },
      ];
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.ShoppingCreditsClaim) {
      const shoppingCreditsClaim = await this.getShoppingCreditsClaim(ctx, startTime, endTime);
      const applicableProducts = shoppingCreditsClaim.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else if (promotionType === PromotionType.ShoppingCreditsDeduction) {
      const shoppingCreditsDeduction = await this.getShoppingCreditsDeduction(ctx, startTime, endTime);
      const applicableProducts = shoppingCreditsDeduction.map(item => item.applicableProduct);
      //获取可用的商品
      const products = await this.getUsableProducts(
        ctx,
        collectionId,
        applicableProducts,
        options,
        relations,
        isUsable,
      );
      return products;
    } else {
      return [];
    }
  }

  async getUsableProducts(
    ctx: RequestContext,
    collectionId: ID,
    applicableProducts: ApplicableProduct[],
    options: ListQueryOptions<Product>,
    relations: RelationPaths<Product>,
    isUsable = true,
  ) {
    options = {
      ...options,
      filter: {
        ...options.filter,
        freeGift: {
          eq: false,
        },
      },
    } as ListQueryOptions<Product>;
    let collectionIdProducts: ID[] = [];
    if (collectionId) {
      const products = await this.connection
        .getRepository(ctx, Product)
        .createQueryBuilder('product')
        .leftJoin('product.variants', 'variants')
        .andWhere('variants.deletedAt IS NULL')
        .leftJoin('variants.collections', 'collections')
        .select('product.id', 'productId')
        .andWhere(`collections.id = :collectionId`, {collectionId})
        .getRawMany<{productId: ID}>();

      collectionIdProducts = products.reduce((value, item) => [...value, item.productId], <Array<ID>>[]);
      if (collectionIdProducts.length <= 0) {
        return {
          items: [],
          totalItems: 0,
        };
      }
    }
    const productAvailability = this.determineProductAvailability(ctx, applicableProducts);
    let {availableGoods, unusableGoods} = productAvailability;
    if (availableGoods.length <= 0 && unusableGoods.length <= 0) {
      if (collectionIdProducts.length > 0) {
        options = {
          ...options,
          filter: {
            ...options.filter,
            id: {
              in: collectionIdProducts as string[],
            },
          },
        };
      }
      const products = await this.customerProductService.findAll(
        ctx,
        {
          ...options,
        },
        relations,
      );
      return products;
    }
    if (!isUsable) {
      const products = availableGoods;
      availableGoods = unusableGoods;
      unusableGoods = products;
    }
    if (availableGoods.length > 0) {
      // 如果有collectionId，只查询collectionId下的商品 且商品在可用商品中
      if (collectionIdProducts.length > 0) {
        availableGoods = availableGoods.filter(item => collectionIdProducts.includes(item));
      }
      if (availableGoods.length <= 0) {
        return {
          items: [],
          totalItems: 0,
        };
      }
      const products = await this.customerProductService.findAll(
        ctx,
        {
          ...options,
          filter: {
            ...options.filter,
            id: {
              in: availableGoods as string[],
            },
          },
        },
        relations,
      );
      return products;
    } else {
      if (collectionIdProducts.length <= 0) {
        options = {
          ...options,
          filter: {
            ...options.filter,
            id: {
              notIn: unusableGoods as string[],
            },
          },
        };
      } else {
        options = {
          ...options,
          filter: {
            ...options.filter,
            id: {
              notIn: unusableGoods as string[],
              in: collectionIdProducts as string[],
            },
          },
        };
      }
      const products = await this.customerProductService.findAll(ctx, options, relations);
      return products;
    }
  }

  /**
   * 确定商品的可用性
   * @param ctx
   * @param applicableProducts
   * @returns
   */
  determineProductAvailability(ctx: RequestContext, applicableProducts: ApplicableProduct[]) {
    const availableGoods: ID[] = [];
    const unusableGoods: ID[] = [];
    for (const applicableProduct of applicableProducts) {
      const productIds = applicableProduct.productIds as ID[];
      if (applicableProduct.applicableType === 'all') {
        //如果是全部商品可用，直接返回空数组
        return {availableGoods: [], unusableGoods: []};
      } else if (applicableProduct.applicableType === 'unusableGoods') {
        if (!productIds || productIds.length === 0) continue;
        unusableGoods.push(...productIds);
      } else {
        if (!productIds || productIds.length === 0) continue;
        availableGoods.push(...productIds);
      }
    }
    if (unusableGoods.length > 0) {
      const unProductIds = unusableGoods.filter(item => !availableGoods.includes(item));
      //如果不可用的商品都不在可用商品中，直接返回不可用商品
      return {availableGoods: unProductIds, unusableGoods: []};
    }
    return {availableGoods: [], unusableGoods: availableGoods};
  }

  //获取互斥的加价购活动
  async getConflictingPurchasePremium(ctx: RequestContext, startTime: Date, endTime: Date) {
    return this.connection
      .getRepository(ctx, PurchasePremium)
      .createQueryBuilder('purchasePremium')
      .leftJoinAndSelect('purchasePremium.promotion', 'promotion')
      .leftJoin('promotion.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.startsAt <= :endTime', {endTime})
      .andWhere('promotion.endsAt >= :startTime', {startTime})
      .andWhere('purchasePremium.state != :state', {state: CouponState.Failure})
      .getMany();
  }
  //获取互斥的第二件半价活动
  async getConflictingDiscountActivity(ctx: RequestContext, startTime: Date, endTime: Date) {
    return this.connection
      .getRepository(ctx, DiscountActivity)
      .createQueryBuilder('discountActivity')
      .leftJoin('discountActivity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('discountActivity.startTime <= :endTime', {endTime})
      .andWhere('discountActivity.endTime >= :startTime', {startTime})
      .andWhere('discountActivity.status != :status', {status: ActivityStatus.Failure})
      .getMany();
  }

  // 获取互斥的购物金发放活动
  async getShoppingCreditsClaim(
    ctx: RequestContext,
    startTime: Date,
    endTime: Date,
  ): Promise<ShoppingCreditsClaimActivity[]> {
    const query = this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .createQueryBuilder('shoppingCreditsClaimActivity')
      .leftJoin('shoppingCreditsClaimActivity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('shoppingCreditsClaimActivity.startTime <= :endTime', {endTime})
      .andWhere('shoppingCreditsClaimActivity.endTime >= :startTime', {startTime});
    query.andWhere('shoppingCreditsClaimActivity.status != :status', {status: ActivityStatus.Failure});
    return query.getMany();
  }

  // 获取互斥的购物金抵扣活动
  async getShoppingCreditsDeduction(
    ctx: RequestContext,
    startTime: Date,
    endTime: Date,
  ): Promise<ShoppingCreditsDeductionActivity[]> {
    const query = this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .createQueryBuilder('shoppingCreditsDeductionActivity')
      .leftJoin('shoppingCreditsDeductionActivity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('shoppingCreditsDeductionActivity.startTime <= :endTime', {endTime})
      .andWhere('shoppingCreditsDeductionActivity.endTime >= :startTime', {startTime});
    query.andWhere('shoppingCreditsDeductionActivity.status != :status', {status: ActivityStatus.Failure});
    return query.getMany();
  }

  // 获取互斥的任选赠活动
  async getConflictingSelectiveGifts(ctx: RequestContext, startTime: Date, endTime: Date) {
    return this.connection
      .getRepository(ctx, SelectiveGiftActivity)
      .createQueryBuilder('selectiveGiftActivity')
      .leftJoin('selectiveGiftActivity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('selectiveGiftActivity.startTime <= :endTime', {endTime})
      .andWhere('selectiveGiftActivity.endTime >= :startTime', {startTime})
      .andWhere('selectiveGiftActivity.status != :status', {status: ActivityStatus.Failure})
      .getMany();
  }

  // 获取互斥的支付有礼活动
  paymentRewardActivityConflict(ctx: RequestContext, startTime: Date, endTime: Date) {
    return this.connection
      .getRepository(ctx, PaymentRewardActivity)
      .createQueryBuilder('paymentRewardActivity')
      .leftJoin('paymentRewardActivity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('paymentRewardActivity.startTime <= :endTime', {endTime})
      .andWhere('paymentRewardActivity.endTime >= :startTime', {startTime})
      .andWhere('paymentRewardActivity.status != :status', {status: ActivityStatus.Failure})
      .getMany();
  }

  //获取互斥的满赠活动
  async getConflictingFullDiscountPresent(
    ctx: RequestContext,
    startTime: Date,
    endTime: Date,
    isAmountFullPresent = false,
  ) {
    const query = this.connection
      .getRepository(ctx, FullDiscountPresent)
      .createQueryBuilder('fullDiscountPresent')
      .leftJoin('fullDiscountPresent.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('fullDiscountPresent.startTime <= :endTime', {endTime})
      .andWhere('fullDiscountPresent.endTime >= :startTime', {startTime})
      .andWhere('fullDiscountPresent.status != :status', {status: ActivityStatus.Failure});
    if (isAmountFullPresent) {
      query.andWhere('fullDiscountPresent.type = :type', {type: FullDiscountPresentType.AmountFullPresent});
    } else {
      query.andWhere('fullDiscountPresent.type != :type', {type: FullDiscountPresentType.AmountFullPresent});
    }
    return query.getMany();
  }

  // 获取打包一口价活动
  async getPackageDiscount(ctx: RequestContext, startTime: Date, endTime: Date) {
    return this.connection
      .getRepository(ctx, PackageDiscount)
      .createQueryBuilder('packageDiscount')
      .leftJoin('packageDiscount.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('packageDiscount.startTime <= :endTime', {endTime})
      .andWhere('packageDiscount.endTime >= :startTime', {startTime})
      .andWhere('packageDiscount.status != :status', {status: ActivityStatus.Failure})
      .getMany();
  }
}
