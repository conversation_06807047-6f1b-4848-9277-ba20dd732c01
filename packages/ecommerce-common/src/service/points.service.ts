import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {MemberService, MembershipOrder} from '@scmally/member';
import {RedLockService} from '@scmally/red-lock';
import {WeChatPaymentService} from '@scmally/wechat';
import {AbstractPoints} from '@scmally/wechat/dist/service/abstract-points';
import {
  CustomerService,
  EventBus,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderService,
  RelationPaths,
  RequestContext,
  Transaction,
  TransactionalConnection,
} from '@vendure/core';
import {IsNull} from 'typeorm';
import {MemberSource, MemberState} from '../../../member/dist/ui/generated-admin-types';
import {OrderPromotionResult, PointsHistory, PointsProductSku} from '../entities';
import {PointsNotEnoughError, PointsOnlyOnePerOrderError, PointsPromotionExpiredError} from '../error.type';
import {PointsChangeEvent} from '../event';
import {PointsGrantTiming, PointsSourceType, SymbolType} from '../generated-admin-types';
import {ChannelCustomFields, OrderBuyType, OrderCustomFields} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {CacheKeyManagerService} from '@scmally/kvs';
import {CustomerPointsService} from './customer-points.service';
import {InterfaceCommonCustomer} from './interface-customer';
import {OrderPromotionResultService} from './order-promotion-result.service';
import {PointsConfigService} from './points-config.service';
import {PointsProductService} from './points-product.service';
@Injectable()
export class PointsService extends AbstractPoints {
  async updateCustomerPoints(ctx: RequestContext, customerId: ID, points: number, symbolType: SymbolType) {
    const lock = await this.redLockService.lockResource(
      `CustomerPoints:updateCustomerPoints:${customerId}:${ctx.channelId}`,
    );
    try {
      const customer = await this.customerService.findOne(ctx, customerId);
      if (!customer) {
        throw new Error('Customer not found');
      }
      if (symbolType === SymbolType.Out) {
        const customerPoints = await this.customerPointsService.findCustomerPoints(ctx, customerId);
        if (customerPoints < Math.abs(points)) {
          throw new PointsNotEnoughError(`扣除的积分不能大于当前积分`);
        }
      }
      await this.customerPointsService.updateCustomerPoints(ctx, customerId, points, symbolType);
      const str = symbolType === SymbolType.In ? '平台获得积分' : '平台扣除积分';
      const pointsHistory = new PointsHistory({
        customer,
        // sourceId: '',
        sourceType: PointsSourceType.AdminManualOperation,
        points,
        symbolType: symbolType,
        description: str,
        channelId: ctx.channelId,
      });
      await this.connection.getRepository(ctx, PointsHistory).save(pointsHistory);
      // 管理后台操作积分无需同步到有赞 故不发布积分变动通知
      return await this.customerService.findOne(ctx, customerId);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }
  async memberOrderCancel(ctx: RequestContext, memberOrderCancelInfo: {memberOrderId: ID; price: number}) {
    const memberOrderId = memberOrderCancelInfo.memberOrderId;
    const memberOrder = await this.connection
      .getRepository(ctx, MembershipOrder)
      .createQueryBuilder('membershipOrder')
      .andWhere('membershipOrder.id = :id', {id: memberOrderId})
      .getOne();
    if (memberOrder?.source === MemberSource.OrderBuy) {
      const orderId = memberOrder.sourceId;
      const order = await this.orderService.findOne(ctx, orderId);
      if (order) {
        await this.merchantVoluntaryRefund(ctx, order, memberOrderCancelInfo.price);
      }
    }
  }
  constructor(
    private customerService: CustomerService,
    private connection: TransactionalConnection,
    private memberService: MemberService,
    private pointsConfigService: PointsConfigService,
    @Inject(forwardRef(() => OrderPromotionResultService))
    private orderPromotionResultService: OrderPromotionResultService,
    @Inject(forwardRef(() => OrderService))
    private orderService: OrderService,
    public weChatPaymentService: WeChatPaymentService,
    private listQueryBuilder: ListQueryBuilder,
    private pointsProductService: PointsProductService,
    private redLockService: RedLockService,
    private cacheService: CacheService,
    private eventBus: EventBus,
    private customerPointsService: CustomerPointsService,
  ) {
    super(weChatPaymentService);
  }

  public interfaceCustomer: InterfaceCommonCustomer;

  registerCustomer(interfaceCustomer: InterfaceCommonCustomer) {
    this.interfaceCustomer = interfaceCustomer;
  }

  async merchantVoluntaryRefund(ctx: RequestContext, order: Order, refundableAmount: number) {
    const orderPromResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
    const orderTotal = orderPromResult?.promResult?.orderTotalPrice ?? order.subTotal;
    const price = Number(refundableAmount ?? 0);
    const pointsHistory = await this.findPointByType(ctx, order.id, PointsSourceType.Order, SymbolType.In);
    if (!pointsHistory) {
      Logger.error(`pointsHistory not exist`);
      return;
    }
    const customer = pointsHistory.customer;
    if (!customer) {
      Logger.error(`customer not exist`);
      return;
    }
    const pointsTotal = pointsHistory.points;
    //退款需要扣除的积分 = 订单总积分 * （主动退款金额 / 订单总金额）
    let points = Math.floor(pointsTotal * (price / orderTotal));
    const orderLineIds = order?.lines?.map(line => line.id);
    if (orderLineIds && orderLineIds.length > 0) {
      const getRefundableAmount = await this.orderPromotionResultService.getRefundableAmount(
        ctx,
        order.id,
        orderLineIds,
      );
      if (getRefundableAmount <= 0) {
        // 已经退回的总积分
        const qb = this.connection.getRepository(ctx, PointsHistory).createQueryBuilder('pointsHistory');
        qb.leftJoin('pointsHistory.customer', 'customer');
        qb.andWhere('pointsHistory.deletedAt is null');
        qb.andWhere('customer.id = :customerId', {customerId: customer.id});
        qb.andWhere('pointsHistory.sourceId = :sourceId', {sourceId: order.id});
        qb.andWhere('pointsHistory.symbolType = :symbolType', {symbolType: SymbolType.Out});
        qb.andWhere('pointsHistory.channelId = :channelId', {channelId: ctx.channelId});
        qb.andWhere('pointsHistory.sourceType  in (:...sourceType)', {
          sourceType: [PointsSourceType.AfterSale, PointsSourceType.VoluntaryRefund],
        });
        qb.select('SUM(pointsHistory.points)', 'totalPoints');
        const pointsHistoryTotal = await qb.getRawOne<{totalPoints: number}>();
        // 已经退回的总积分
        const totalPoints = pointsHistoryTotal?.totalPoints ?? 0;
        // 剩余积分
        const surplusPoints = pointsTotal - totalPoints;
        if (points !== surplusPoints) {
          points = surplusPoints;
        }
      }
    }
    if (points <= 0) {
      Logger.error(`points is less than 0`);
      return;
    }

    await this.customerPointsService.updateCustomerPoints(ctx, customer.id, points, SymbolType.Out);
    Logger.debug(`主动退款更新用户积分成功`);
    const newPointsHistory = new PointsHistory({
      customer,
      sourceId: order.id,
      sourceType: PointsSourceType.VoluntaryRefund,
      points: points,
      symbolType: SymbolType.Out,
      description: `订单退款成功,扣除积分`,
      channelId: ctx.channelId,
    });
    await this.connection.getRepository(ctx, PointsHistory).save(newPointsHistory);
    this.eventBus.publish(
      new PointsChangeEvent(ctx, customer, points, SymbolType.Out, newPointsHistory.description, newPointsHistory),
    );
    Logger.debug(`保存积分历史成功`);
  }

  async pointsHistory(
    ctx: RequestContext,
    options: ListQueryOptions<PointsHistory>,
    relations: RelationPaths<PointsHistory>,
  ) {
    const customer = await this.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new Error('用户未登录');
    }
    const qb = this.listQueryBuilder.build(PointsHistory, options, {
      relations,
      ctx,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.leftJoin(`${qb.alias}.customer`, 'customer');
    // 积分不等于0
    qb.andWhere(`${qb.alias}.points != 0`);
    qb.andWhere('customer.id = :customerId', {customerId: customer.id});
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId}); // 只查询当前渠道的积分
    const [items, totalItems] = await qb.getManyAndCount();
    items.map(item => {
      if (!item.description) {
        if (item.sourceType === PointsSourceType.Order) {
          item.description = `积分收入:订单交易获得积分`;
        } else if (item.sourceType === PointsSourceType.AfterSale) {
          item.description = `订单退款成功，扣除积分`;
        } else if (item.sourceType === PointsSourceType.YouZanSync) {
          item.description = `有赞积分同步`;
        } else if (item.sourceType === PointsSourceType.PointsExchange) {
          item.description = `积分兑换`;
        } else if (item.sourceType === PointsSourceType.PointsRefund) {
          item.description = `积分兑换订单退款`;
        } else if (item.sourceType === PointsSourceType.VoluntaryRefund) {
          item.description = `订单退款成功，扣除积分`;
        }
      }
    });
    return {
      items,
      totalItems,
    };
  }

  @Transaction()
  async cancelOrderPointsExchangeCount(ctx: RequestContext, order: Order) {
    try {
      const orderCustomerField = order.customFields as OrderCustomFields;
      if (orderCustomerField.buyType !== OrderBuyType.PointsExchange) {
        return false;
      }
      const orderLines = order.lines;
      if (!orderLines || orderLines.length !== 1) {
        return false;
      }
      const orderPlacedAt = order.orderPlacedAt ?? new Date();
      for (const orderLine of orderLines) {
        const productId = orderLine.productVariant.productId;
        const productVariantId = orderLine.productVariant.id;
        const lock = await this.redLockService.lockResource(
          `PointsProduct:SalesUpdate:${productId}:${productVariantId}`,
        );
        try {
          const pointsProduct = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
          if (!pointsProduct || new Date(pointsProduct.createdAt) > new Date(orderPlacedAt)) {
            continue;
          }
          const pointsProductSku = pointsProduct.pointsProductSkus.find(item =>
            idsAreEqual(item.productVariantId, productVariantId),
          );
          if (pointsProductSku) {
            pointsProductSku.exchangeCount -= orderLine.quantity || orderLine.orderPlacedQuantity;
            if (pointsProductSku.exchangeCount < 0) {
              pointsProductSku.exchangeCount = 0;
            }
            await this.connection.getRepository(ctx, PointsProductSku).save(pointsProductSku);
            await this.cacheService.removeCache(CacheKeyManagerService.pointsProduct(productId, ctx.channelId));
          }
        } catch (e) {
          Logger.error(`更新积分兑换商品销量失败: ${e.message}`);
          throw new Error(`更新积分兑换商品销量失败: ${e.message}`);
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
      return true;
    } catch (error) {
      Logger.error(`更新积分兑换商品销量失败: ${error.message}`);
      return false;
    }
  }

  @Transaction()
  async addPointsExchangeProductSales(ctx: RequestContext, order: Order, isAgain: boolean): Promise<boolean> {
    try {
      if (isAgain) {
        return true;
      }
      const orderCustomerField = order.customFields as OrderCustomFields;
      if (orderCustomerField.buyType !== OrderBuyType.PointsExchange) {
        return false;
      }
      const orderLines = order.lines;
      if (!orderLines || orderLines.length !== 1) {
        return false;
      }
      for (const orderLine of orderLines) {
        const productId = orderLine.productVariant.productId;
        const productVariantId = orderLine.productVariant.id;
        const lock = await this.redLockService.lockResource(
          `PointsProduct:SalesUpdate:${productId}:${productVariantId}`,
        );
        try {
          const pointsProduct = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
          if (!pointsProduct) {
            continue;
          }
          const pointsProductSku = pointsProduct.pointsProductSkus.find(item =>
            idsAreEqual(item.productVariantId, productVariantId),
          );
          if (pointsProductSku) {
            pointsProductSku.exchangeCount += orderLine.quantity;
            await this.connection.getRepository(ctx, PointsProductSku).save(pointsProductSku);
            await this.cacheService.removeCache(CacheKeyManagerService.pointsProduct(productId, ctx.channelId));
          }
        } catch (e) {
          Logger.error(`更新积分兑换商品销量失败: ${e.message}`);
          throw new Error(`更新积分兑换商品销量失败: ${e.message}`);
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
      return true;
    } catch (error) {
      Logger.error(`更新积分兑换商品销量失败: ${error.message}`);
      return false;
    }
  }

  async checkPoints(ctx: RequestContext, order: Order, points: number, isAgain: boolean): Promise<boolean> {
    const customer = order.customer;
    if (!customer) {
      throw new Error('订单没有关联用户');
    }
    if (isAgain) {
      return true;
    }
    const orderCustomerField = order.customFields as OrderCustomFields;
    if (orderCustomerField.buyType === OrderBuyType.PointsExchange) {
      const customerPoints = await this.customerPointsService.findCustomerPoints(ctx, customer.id);
      if (customerPoints < points) {
        throw new PointsNotEnoughError(`积分不足,请重新选择规格或者商品下单,当前积分:${customerPoints}`);
      }
      const orderLines = order.lines;
      if (!orderLines || orderLines.length !== 1) {
        throw new PointsOnlyOnePerOrderError('积分兑换订单每次只能兑换一个商品');
      }
      const orderLine = orderLines[0];
      const productId = orderLine.productVariant.productId;
      const productVariantId = orderLine.productVariant.id;
      const pointsProduct = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
      if (!pointsProduct) {
        throw new PointsPromotionExpiredError('积分兑换活动已失效,请重新下单');
      }
      const pointsProductSku = pointsProduct.pointsProductSkus.find(item =>
        idsAreEqual(item.productVariantId, productVariantId),
      );
      if (!pointsProductSku?.enabled) {
        throw new PointsPromotionExpiredError('积分兑换活动已失效,请重新下单');
      }
    } else {
      const orderLines = order.lines;
      for (const orderLine of orderLines) {
        const productId = orderLine.productVariant.productId;
        const productVariantId = orderLine.productVariant.id;
        const pointsProduct = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
        if (pointsProduct && !pointsProduct.allowPurchaseAtOriginalPrice) {
          const pointsProductSku = pointsProduct.pointsProductSkus.find(item =>
            idsAreEqual(item.productVariantId, productVariantId),
          );
          if (pointsProductSku?.enabled) {
            throw new PointsPromotionExpiredError('该商品只能支持积分兑换,请重新选择');
          }
        }
      }
    }
    return true;
  }

  async freezePoints(ctx: RequestContext, order: Order, points: number, isAgain: boolean): Promise<boolean> {
    const customer = order.customer;
    if (!customer) {
      throw new Error('订单没有关联用户');
    }
    if (isAgain) {
      return true;
    }
    const customerPoints = await this.customerPointsService.findCustomerPoints(ctx, customer.id);
    if (customerPoints < points) {
      throw new PointsNotEnoughError(`积分不足,请重新选择规格或者商品下单,当前积分:${customerPoints}`);
    }
    await this.customerPointsService.updateCustomerPoints(ctx, customer.id, points, SymbolType.Frz);
    const pointsHistory = new PointsHistory({
      customer,
      sourceId: order.id,
      sourceType: PointsSourceType.PointsExchange,
      points,
      symbolType: SymbolType.Frz,
      description: '冻结积分（积分兑换待支付)',
      channelId: ctx.channelId,
    });
    await this.connection.getRepository(ctx, PointsHistory).save(pointsHistory);
    this.eventBus.publish(
      new PointsChangeEvent(ctx, customer, points, SymbolType.Frz, pointsHistory.description, pointsHistory),
    );
    return true;
  }

  @Transaction()
  async cancelOrderPointsExchange(ctx: RequestContext, order: Order) {
    const orderCustomerField = order.customFields as OrderCustomFields;
    if (orderCustomerField.buyType !== OrderBuyType.PointsExchange || !orderCustomerField.isReturnPoints) {
      return;
    }
    const pointsHistory = await this.findPointByType(ctx, order.id, PointsSourceType.PointsExchange, SymbolType.Out);
    if (pointsHistory) {
      const points = pointsHistory.points;
      const customer = pointsHistory.customer;
      await this.customerPointsService.updateCustomerPoints(ctx, customer.id, points, SymbolType.In);
      const newPointsHistory = new PointsHistory({
        customer,
        sourceId: order.id,
        sourceType: PointsSourceType.PointsRefund,
        points,
        symbolType: SymbolType.In,
        description: '积分兑换订单退款',
        channelId: ctx.channelId,
      });
      await this.connection.getRepository(ctx, PointsHistory).save(newPointsHistory);
      this.eventBus.publish(
        new PointsChangeEvent(ctx, customer, points, SymbolType.In, newPointsHistory.description, newPointsHistory),
      );
    }
  }

  @Transaction()
  async cancelOrderPoints(ctx: RequestContext, order: Order) {
    const pointsHistory = await this.findPointByType(ctx, order.id, PointsSourceType.PointsExchange, SymbolType.Frz);
    if (pointsHistory) {
      const points = pointsHistory.points;
      const customer = pointsHistory.customer;
      await this.customerPointsService.updateCustomerPoints(ctx, customer.id, points, SymbolType.In);
      await this.connection.getRepository(ctx, PointsHistory).update(
        {
          id: pointsHistory.id,
        },
        {
          deletedAt: new Date(),
        },
      );
      this.eventBus.publish(
        new PointsChangeEvent(ctx, customer, points, SymbolType.In, '兑换订单取消积分解冻', pointsHistory),
      );
    }
  }

  // 订单赠送积分
  async orderBonusPoints(ctx: RequestContext, order: Order, orderPromResult?: OrderPromotionResult) {
    const orderPoints = await this.connection.getRepository(ctx, PointsHistory).findOne({
      where: {
        sourceId: order.id,
        sourceType: PointsSourceType.Order,
        symbolType: SymbolType.In,
        channelId: ctx.channelId,
        deletedAt: IsNull(),
      },
    });
    if (orderPoints) {
      return;
    }
    if (!orderPromResult) {
      orderPromResult = (await this.orderPromotionResultService.getResultByOrderId(
        ctx,
        order.id,
      )) as OrderPromotionResult;
    }
    const totalPrice = orderPromResult?.promResult?.orderTotalPrice || order.subTotal;
    const channel = ctx.channel;
    const points = (channel.customFields as ChannelCustomFields).points ?? 0;
    const amount = (channel.customFields as ChannelCustomFields).amount ?? 0;
    let pointsDescription = `每消费一个${amount / 100}元获得${points}积分`;
    Logger.debug(`订单完成赠送积分渠道设置:points: ${points}, amount: ${amount}`);
    if (points && amount) {
      let bonusPoints = Math.floor(totalPrice / amount) * points;
      const customer = order.customer;
      if (customer) {
        const userMember = await this.memberService.getUserMember(ctx, true, customer.id);
        Logger.debug(`获取用户会员信息:${userMember}`);
        if (userMember && userMember.state === MemberState.Normal) {
          Logger.debug(`用户是会员并且会员状态是正常使用中`);
          const membershipPlan = userMember.membershipPlan;
          if (membershipPlan.rightsPoints.enable) {
            Logger.debug(`会员积分权益是开启状态，积分倍数是${membershipPlan.rightsPoints.pointsMultiple}`);
            const pointsMultiple = membershipPlan.rightsPoints.pointsMultiple || 1;
            bonusPoints = Math.floor(bonusPoints * pointsMultiple);
            pointsDescription = pointsDescription + `,享${membershipPlan.name}${pointsMultiple}倍积分`;
            Logger.debug(
              `会员积分权益是开启状态，积分倍数是${membershipPlan.rightsPoints.pointsMultiple}，积分倍数后的积分是${bonusPoints}`,
            );
          }
        }
        if (bonusPoints <= 0) {
          Logger.debug(`订单完成赠送积分: ${bonusPoints}积分小于等于0`);
          return;
        }
        await this.customerPointsService.updateCustomerPoints(ctx, customer.id, bonusPoints, SymbolType.In);
        const pointsHistory = new PointsHistory({
          customer,
          sourceId: order.id,
          sourceType: PointsSourceType.Order,
          points: bonusPoints,
          symbolType: SymbolType.In,
          description: pointsDescription,
          channelId: ctx.channelId,
        });
        await this.connection.getRepository(ctx, PointsHistory).save(pointsHistory);
        this.eventBus.publish(
          new PointsChangeEvent(ctx, customer, bonusPoints, SymbolType.In, pointsHistory.description, pointsHistory),
        );
        Logger.debug(`保存积分历史成功`);
      }
    }
  }

  // 确认收货赠送积分
  async confirmReceiptOfGoodsBonusPoints(ctx: RequestContext, order: Order) {
    const pointsConfig = await this.pointsConfigService.findOne(ctx);
    if (pointsConfig?.pointsGrantTiming || pointsConfig.pointsGrantTiming === PointsGrantTiming.ConfirmReceipt) {
      const isBonusPoints = await this.isBonusPoints(ctx, order);
      // 判断是否赠送积分
      if (isBonusPoints) {
        await this.orderBonusPoints(ctx, order);
      }
    }
  }

  @Transaction()
  async paymentSettledBonusPoints(ctx: RequestContext, order: Order) {
    const orderPromResult = (await this.orderPromotionResultService.getResultByOrderId(
      ctx,
      order.id,
    )) as OrderPromotionResult;
    const pointsConfig = await this.pointsConfigService.findOne(ctx);
    if (!pointsConfig?.pointsGrantTiming || pointsConfig.pointsGrantTiming === PointsGrantTiming.CompletePayment) {
      const isBonusPoints = await this.isBonusPoints(ctx, order);
      // 判断是否赠送积分
      if (isBonusPoints) {
        await this.orderBonusPoints(ctx, order, orderPromResult);
      }
    }
    if ((order.customFields as OrderCustomFields).buyType === OrderBuyType.PointsExchange) {
      const pointsHistory = await this.findPointByType(ctx, order.id, PointsSourceType.PointsExchange, SymbolType.Frz);
      if (pointsHistory) {
        pointsHistory.symbolType = SymbolType.Out;
        pointsHistory.description = '积分兑换';
        await this.connection.getRepository(ctx, PointsHistory).save(pointsHistory);
      }
      const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
      try {
        await this.orderService.updateCustomFields(ctx, order.id, {
          isReturnPoints: pointsConfig?.pointsRefundGrant ?? false,
        });
      } catch (error) {
        Logger.error(error);
        throw new Error('update order custom field error');
      } finally {
        await this.redLockService.unlockResource(lock);
      }
    }
  }
  async isBonusPoints(ctx: RequestContext, order: Order) {
    const orderBuyType = (order.customFields as OrderCustomFields)?.buyType;
    if (orderBuyType === OrderBuyType.PointsExchange) {
      const pointsConfig = await this.pointsConfigService.findOne(ctx);
      if (pointsConfig?.pointsExchangeGrant) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  async findPointByType(ctx: RequestContext, sourceId: ID, sourceType: PointsSourceType, symbolType: SymbolType) {
    return this.connection.getRepository(ctx, PointsHistory).findOne({
      where: {sourceId, sourceType, symbolType: symbolType, channelId: ctx.channelId, deletedAt: IsNull()},
      relations: ['customer'],
    });
  }
}
