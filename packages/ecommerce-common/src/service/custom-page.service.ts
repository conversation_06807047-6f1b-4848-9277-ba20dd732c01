import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Product,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {DateTime} from 'luxon';
import {LessThanOrEqual} from 'typeorm';
import {CUSTOM_PAGE_CACHE_TIMEOUT, DEFAULT_CACHE_TIMEOUT} from '../consts';
import {Component, CustomPage, Popup} from '../entities';
import {CustomPageInput, LayoutType, ProgramLinkInput, ShareType} from '../generated-admin-types';
import {ComponentType, Direction, JumpInput, PageType} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
export type Point = {
  x: number;
  y: number;
};
@Injectable()
export class CustomPageService {
  constructor(
    private connection: TransactionalConnection,
    private commonService: CommonService,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private requestContextService: RequestContextService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private kvsService: KvsService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private productCustomService: ProductCustomService,
  ) {}

  async getPopup(ctx: RequestContext, popupId: ID) {
    return this.connection.getRepository(ctx, Popup).findOne({
      where: {id: popupId},
    });
  }
  /**
   * 新增或者修改自定义页面
   * @param ctx
   * @param customPageInput
   * @returns
   */
  async upsertCustomPage(ctx: RequestContext, customPageInput: CustomPageInput, isTimedPublishing = false) {
    this.validate(customPageInput);
    // 如果是定时发布并且当前页面已经存在
    if (customPageInput.timingAt && customPageInput.id && customPageInput.timingAt > new Date() && !isTimedPublishing) {
      const customPage = await this.findOne(ctx, customPageInput.id);
      if (!customPage) {
        throw new Error('customPage not exist');
      }
      await this.connection.getRepository(ctx, CustomPage).update(
        {id: customPageInput.id},
        {
          modifiedContent: customPageInput,
          timingAt: customPageInput.timingAt,
        },
      );
    } else {
      const popup = await this.upsertPopup(ctx, customPageInput);
      let customPage = new CustomPage({
        ...customPageInput,
        popup: popup,
      });
      if (customPageInput.id) {
        const oldCustomPage = await this.findOne(ctx, customPageInput.id);
        if (oldCustomPage?.type !== customPage.type) {
          customPage.smallProgramQRCodeLink = null;
          customPage.h5Link = null;
          customPage.h5QRCode = null;
        }
      }
      if (isTimedPublishing) {
        customPage.enable = true;
        customPage.modifiedContent = null;
        customPage.timingAt = null;
      }
      if (customPage.type && customPage.type !== PageType.ActivePage && customPage.enable) {
        const homePageSql = this.listQueryBuilder.build(
          CustomPage,
          {filter: {type: {eq: customPage.type}, enable: {eq: true}}},
          {
            ctx,
            channelId: ctx.channelId,
          },
        );
        if (customPage.id) {
          homePageSql.andWhere(`${homePageSql.alias}.id != :id`, {id: customPage.id});
        }
        const homePage = await homePageSql.take(1).getOne();
        if (homePage) {
          // 2025-02-10 运营需求 新页面发布原页面不能失效
          // homePage.enable = false;
          // 故修改页面类型为活动页面
          homePage.type = PageType.ActivePage;
          homePage.smallProgramQRCodeLink = null;
          homePage.h5Link = null;
          homePage.h5QRCode = null;
          await this.cacheService.removeCache(CacheKeyManagerService.customPage(homePage.id, ctx.channelId));
          await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(homePage.id, ctx.channelId));
          await this.connection.getRepository(ctx, CustomPage).save(homePage);
        }
      }
      const updatedAt = new Date();
      customPage = await this.channelService.assignToCurrentChannel(customPage, ctx);
      customPage = await this.connection.getRepository(ctx, CustomPage).save(customPage);
      for (const componentInput of customPageInput.components) {
        const componentId = componentInput.id;
        let sort = componentInput.sort;
        if (!componentId) {
          const maxComponent = await this.connection.getRepository(ctx, Component).findOne({
            where: {customPage: {id: customPage.id}},
            order: {sort: 'DESC'},
          });
          sort = sort ? sort : maxComponent ? Number(maxComponent.sort ?? 0) + 1 : 1;
        }
        const component = new Component({...componentInput, customPage, sort: sort});
        await this.connection.getRepository(ctx, Component).save(component);
      }
      if (customPage.id) {
        await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPage.id, ctx.channelId));
        await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPage.id, ctx.channelId));
      }
      if (customPage.type && customPage.type !== PageType.ActivePage) {
        await this.cacheService.removeCache(CacheKeyManagerService.customPageByType(customPage.type, ctx.channelId));
        await this.cacheService.removeCache(
          CacheKeyManagerService.customPageComponentByType(customPage.type, ctx.channelId),
        );
      }
      await this.kvsService.customPageUpdateTime.set(`CustomPage_ID-${customPage.id}`, updatedAt);
      return this.connection.getRepository(ctx, CustomPage).findOne({where: {id: customPage.id}});
    }
  }
  async upsertPopup(ctx: RequestContext, customPageInput: CustomPageInput) {
    const showPopup = customPageInput.showPopup;
    if (showPopup) {
      const popupInput = customPageInput.popup;
      if (!popupInput) {
        throw new Error('开启弹窗时内容不能为空');
      }
      // 需要验证开始时间和结束时间 "HH:mm:ss"
      if (!popupInput.deliveryStartTime || !popupInput.deliveryEndTime) {
        throw new Error('弹窗开始时间和结束时间不能为空');
      }
      // 结束时间不能小于开始时间
      if (this.isStartTimeGreaterThanEndTime(popupInput.deliveryStartTime, popupInput.deliveryEndTime)) {
        throw new Error('结束时间不能小于等于开始时间');
      }
      let popup = new Popup({
        id: popupInput.id,
        title: popupInput.title,
        pictureResource: popupInput.pictureResource,
        deliveryStartTime: popupInput.deliveryStartTime,
        deliveryEndTime: popupInput.deliveryEndTime,
        pushFrequency: popupInput.pushFrequency,
      });
      popup = await this.channelService.assignToCurrentChannel(popup, ctx);
      popup = await this.connection.getRepository(ctx, Popup).save(popup);
      return popup;
    }
    return;
  }

  isStartTimeGreaterThanEndTime(startTimeStr: string, endTimeStr: string) {
    const startTime = DateTime.fromFormat(startTimeStr, 'HH:mm:ss');
    const endTime = DateTime.fromFormat(endTimeStr, 'HH:mm:ss');
    return startTime >= endTime;
  }

  async updateSortCustomPage(ctx: RequestContext, customPageId: ID, componentId: ID, direction: Direction) {
    const components = await this.connection
      .getRepository(ctx, Component)
      .find({where: {customPage: {id: customPageId}}});
    components.sort((a, b) => Number(a.sort ?? 0) - Number(b.sort ?? 0));
    const componentIndex = components.findIndex(c => c.id === componentId);
    if (componentIndex === -1) {
      throw new Error('component not exist');
    }
    let targetIndex = -1;
    if (direction === Direction.Up) {
      if (componentIndex === 0) {
        throw new Error('component is first');
      }
      targetIndex = componentIndex - 1;
    } else if (direction === Direction.Down) {
      if (componentIndex === components.length - 1) {
        throw new Error('component is last');
      }
      targetIndex = componentIndex + 1;
    }
    const component = components[componentIndex];
    const targetComponent = components[targetIndex];
    const tempSort = component.sort;
    component.sort = targetComponent.sort;
    targetComponent.sort = tempSort;
    await this.connection.getRepository(ctx, Component).save(component);
    await this.connection.getRepository(ctx, Component).save(targetComponent);
    await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPageId, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId));
    const customPage = await this.findOne(ctx, customPageId);
    if (customPage?.type && customPage.type !== PageType.ActivePage) {
      await this.cacheService.removeCache(CacheKeyManagerService.customPageByType(customPage.type, ctx.channelId));
      await this.cacheService.removeCache(
        CacheKeyManagerService.customPageComponentByType(customPage.type, ctx.channelId),
      );
    }
    const updatedAt = new Date();
    await this.connection.getRepository(ctx, CustomPage).update({id: customPageId}, {updatedAt: updatedAt});
    await this.kvsService.customPageUpdateTime.set(`CustomPage_ID-${customPageId}`, updatedAt);
    return this.connection.getRepository(ctx, CustomPage).findOne({where: {id: customPageId}});
  }

  /**
   * 获取自定义页面二维码链接
   * @param ctx
   * @param input
   * @returns
   */
  async getCustomPageQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const customPageId = input.id;
    const customPage = await this.findOne(ctx, customPageId);
    if (!customPage) {
      throw new EntityNotFoundError('CustomPage', customPageId);
    }
    if (input.isPreview && customPage.smallProgramPreviewQRCode) {
      return customPage.smallProgramPreviewQRCode;
    }
    if (!input.isPreview && customPage.smallProgramQRCodeLink) {
      return customPage.smallProgramQRCodeLink;
    }
    if (customPage.type === PageType.HomePage) {
      input.type = ShareType.HomePage;
    } else if (customPage.type === PageType.CommodityGroupPage) {
      input.type = ShareType.CommodityGroupPage;
    } else if (customPage.type === PageType.MembershipPlanPage) {
      input.type = ShareType.MemberCenter;
    } else if (customPage.type === PageType.CheckinPage) {
      input.type = ShareType.CheckinPage;
    }
    if (input.isPreview) {
      input.type = ShareType.CustomPage;
      input.path = '';
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    if (input.isPreview) {
      customPage.smallProgramPreviewQRCode = qrCodeLink;
    } else {
      customPage.smallProgramQRCodeLink = qrCodeLink;
    }
    await this.connection.getRepository(ctx, CustomPage).save(customPage);
    await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPageId, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId));
    if (customPage?.type && customPage.type !== PageType.ActivePage) {
      await this.cacheService.removeCache(CacheKeyManagerService.customPageByType(customPage.type, ctx.channelId));
      await this.cacheService.removeCache(
        CacheKeyManagerService.customPageComponentByType(customPage.type, ctx.channelId),
      );
    }
    return qrCodeLink;
  }

  /**
   * 根据分组id获取商品
   * @param ctx
   * @param collectionId  分组id
   * @param options 查询参数
   * @param relations 关联参数
   * @returns
   */
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, collectionId: ID, options: ListQueryOptions<Product>) => {
      const productName = options?.filter?.name?.contains;
      const skip = options?.skip;
      if (productName || skip) {
        return ''; // 如果不需要缓存，返回空字符串
      }
      return CacheKeyManagerService.productCollections(collectionId, ctx.channelId);
    },
  })
  async seriesProducts(
    ctx: RequestContext,
    collectionId: ID,
    options: ListQueryOptions<Product>,
    relations: RelationPaths<Product>,
  ) {
    const productName = options?.filter?.name?.contains;
    const skip = options?.skip;
    let isCache = true;
    if (productName || skip) {
      isCache = false;
    }
    const memoryStorageCacheKey = CacheKeyManagerService.productCollections(collectionId, ctx.channelId);
    if (ctx.apiType === 'shop' && isCache) {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const qb = this.connection
      .getRepository(ctx, Product)
      .createQueryBuilder('product')
      .distinct(true)
      .leftJoin('product.variants', 'productVariant')
      .andWhere('productVariant.deletedAt IS NULL')
      .leftJoin('productVariant.collections', 'collection')
      .andWhere('collection.id = :collectionId', {collectionId: collectionId})
      .andWhere('product.deletedAt IS NULL')
      .andWhere('product.customFieldsFreegift = false')
      .leftJoin('product.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (ctx.apiType === 'shop') {
      if (isCache) {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      qb.andWhere('product.customFieldsHidden = false').andWhere('product.enabled = true');
    }
    if (productName) {
      qb.leftJoin('product.translations', 'translations');
      qb.andWhere(`translations.name like :productName`, {productName: `%${productName}%`});
    }
    const [items, totalItems] = await qb
      .limit(options.take ?? 50)
      .offset(options.skip ?? 0)
      .orderBy('product.createdAt', 'DESC')
      .getManyAndCount();
    if (ctx.apiType === 'shop' && isCache) {
      this.memoryStorageService.set(memoryStorageCacheKey, {items, totalItems});
    }
    return {
      items,
      totalItems,
    };
  }

  /**
   * 停用或者启用自定义页面
   * @param ctx
   * @param customPageId  自定义页面id
   * @param enable  是否启用
   * @returns
   */
  async transitionEnable(ctx: RequestContext, customPageId: ID, enable: boolean) {
    const customPage = await this.findOne(ctx, customPageId);
    if (!customPage) {
      throw new Error('customPage not exist');
    }
    if (enable) {
      if (customPage.type && customPage.type !== PageType.ActivePage) {
        const homePage = await this.listQueryBuilder
          .build(
            CustomPage,
            {filter: {type: {eq: customPage.type}, enable: {eq: true}}},
            {
              ctx,
              channelId: ctx.channelId,
            },
          )
          .take(1)
          .getOne();
        if (homePage) {
          homePage.enable = false;
          await this.cacheService.removeCache(CacheKeyManagerService.customPage(homePage.id, ctx.channelId));
          await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(homePage.id, ctx.channelId));
          await this.connection.getRepository(ctx, CustomPage).save(homePage);
        }
      }
    }
    customPage.enable = enable;
    await this.kvsService.customPageUpdateTime.set(`CustomPage_ID-${customPageId}`, new Date());
    await this.connection.getRepository(ctx, CustomPage).save(customPage);
    await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPageId, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId));
    if (customPage?.type && customPage.type !== PageType.ActivePage) {
      await this.cacheService.removeCache(CacheKeyManagerService.customPageByType(customPage.type, ctx.channelId));
      await this.cacheService.removeCache(
        CacheKeyManagerService.customPageComponentByType(customPage.type, ctx.channelId),
      );
    }
    return this.findOne(ctx, customPageId);
  }

  /**
   * 删除自定义页面
   * @param ctx
   * @param customPageId
   * @returns
   */
  async deleteCustomPage(ctx: RequestContext, customPageId: ID) {
    const customPage = await this.findOne(ctx, customPageId);
    if (!customPage) {
      throw new Error('customPage not exist');
    }
    const components = await this.connection
      .getRepository(ctx, Component)
      .createQueryBuilder()
      .andWhere('customPageId = :customPageId', {customPageId: customPage.id})
      .getMany();
    for (const component of components) {
      await this.connection.getRepository(ctx, Component).delete(component.id);
    }
    await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPageId, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId));
    await this.kvsService.customPageUpdateTime.set(`CustomPage_ID-${customPageId}`, new Date());
    return this.connection.getRepository(ctx, CustomPage).delete(customPageId);
  }
  /**
   * 查询自定义页面列表
   * @param ctx
   * @param options
   * @param relations
   * @returns
   */
  async findAll(ctx: RequestContext, options: ListQueryOptions<CustomPage>, relations: RelationPaths<CustomPage>) {
    const [items, totalItems] = await this.listQueryBuilder
      .build(CustomPage, options, {
        ctx,
        relations: relations ?? ['channels', 'components'],
        channelId: ctx.channelId,
      })
      .getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  /**
   * 查询自定义页面详情
   * @param ctx
   * @param customPageId 自定义页面id
   * @param options
   * @param relations
   * @returns
   */
  @cacheableAccess({
    cacheKeyFn: (
      ctx: RequestContext,
      customPageId: ID,
      options?: ListQueryOptions<CustomPage>,
      relations?: RelationPaths<CustomPage>,
      isPreview?: boolean,
    ) => {
      if (isPreview) {
        return ''; // 如果是预览，不使用缓存
      }
      const customPageType = options?.filter?.type?.eq;
      if (ctx.apiType === 'shop') {
        if (customPageId) {
          return CacheKeyManagerService.customPage(customPageId, ctx.channelId);
        } else {
          if (customPageType && customPageType !== PageType.ActivePage) {
            return CacheKeyManagerService.customPageByType(customPageType, ctx.channelId);
          }
        }
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    customPageId: ID,
    options?: ListQueryOptions<CustomPage>,
    relations?: RelationPaths<CustomPage>,
    isPreview?: boolean,
  ) {
    if (!isPreview) {
      const customPageType = options?.filter?.type?.eq;
      let customPages: CustomPage[] = [];
      let memoryStorageCacheKey = ``;
      if (ctx.apiType === 'shop') {
        if (customPageId) {
          memoryStorageCacheKey = CacheKeyManagerService.customPage(customPageId, ctx.channelId);
        } else {
          if (customPageType && customPageType !== PageType.ActivePage) {
            memoryStorageCacheKey = CacheKeyManagerService.customPageByType(customPageType, ctx.channelId);
          }
        }
        customPages = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      if (!customPages || customPages.length <= 0) {
        const qb = this.listQueryBuilder.build(CustomPage, options, {
          ctx,
          relations: [], // relations ?? ['channels', 'components'],
          channelId: ctx.channelId,
        });
        if (customPageId) {
          qb.andWhere(`${qb.alias}.id=:customPageId`, {customPageId: customPageId});
        }
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, CUSTOM_PAGE_CACHE_TIMEOUT);
        }
        customPages = await qb.getMany();
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, customPages);
        }
      }
      if (customPages.length > 0) {
        const customPage = customPages[0];
        const cache =
          ctx.apiType === 'shop'
            ? {
                id: ``,
                milliseconds: 0,
              }
            : false;
        if (ctx.apiType === 'shop' && cache) {
          if (customPageId) {
            cache.id = CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId);
          } else {
            cache.id = CacheKeyManagerService.customPageComponentByType(customPageType as string, ctx.channelId);
          }
          cache.milliseconds = DEFAULT_CACHE_TIMEOUT;
        }
        let components: Component[] = [];
        let componentsMemoryStorageCacheKey = ``;
        if (ctx.apiType === 'shop') {
          if (customPageId) {
            componentsMemoryStorageCacheKey = CacheKeyManagerService.customPageComponent(customPageId, ctx.channelId);
          } else {
            componentsMemoryStorageCacheKey = CacheKeyManagerService.customPageComponentByType(
              customPageType as string,
              ctx.channelId,
            );
          }
          components = this.memoryStorageService.get(componentsMemoryStorageCacheKey);
          Logger.info(`componentsMemoryStorageCacheKey:${componentsMemoryStorageCacheKey}`);
        }
        if (!components || components.length <= 0) {
          customPageId = customPage.id;
          components = await this.connection.getRepository(ctx, Component).find({
            where: {customPage: {id: customPageId}},
            cache: cache,
          });
          if (ctx.apiType === 'shop') {
            this.memoryStorageService.set(componentsMemoryStorageCacheKey, components);
          }
        }
        customPage.components = components;
        if (customPage.components && customPage.components.length > 0) {
          customPage.components.sort((a, b) => Number(a.sort ?? 0) - Number(b.sort ?? 0));
        }
        return customPage;
      }
      return null;
    } else {
      const customPages = await this.connection.getRepository(ctx, CustomPage).find({
        where: {id: customPageId},
        relations: ['channels', 'components'],
      });
      if (customPages.length > 0) {
        const customPage = customPages[0];
        const modifiedContent = customPage?.modifiedContent;
        if (modifiedContent) {
          // 变量modifiedContent中的components 如果不存在id字段则给id赋值为0
          modifiedContent.components.forEach(component => {
            if (!component.id) {
              component.id = '0';
            }
          });
          return modifiedContent as CustomPage;
        }
        return customPage;
      }
      return null;
    }
  }

  /**
   * 验证自定义页面数据
   * @param customPageInput
   */
  validate(customPageInput: CustomPageInput) {
    const components = customPageInput.components;
    if (components.length < 0) {
      throw new Error('The component list of the custom page cannot be empty');
    }
    for (const component of components) {
      if (
        component.type === ComponentType.Banner ||
        component.type === ComponentType.Picture ||
        component.type === ComponentType.Video
      ) {
        const pictures = component.data.pictures;
        if (!pictures || pictures.length === 0) {
          throw new Error('pictures cannot be empty when the component type is advertising image or banner');
        }
        if (component.type === ComponentType.Picture || component.type === ComponentType.Video) {
          const layoutType = component.data.layoutType ?? LayoutType.OneColumn;
          // 根据不同的布局类型检查图片数量
          const pictureLength = pictures.length;
          const layoutPictureCount = {
            [LayoutType.OneColumn]: 1,
            [LayoutType.TwoColumn]: 2,
            [LayoutType.ThreeColumn]: 3,
          };
          if (layoutPictureCount[layoutType] !== pictureLength) {
            throw new Error('The number of pictures does not match the layout type');
          }
        }
        for (const picture of pictures) {
          if (picture) {
            if (component.type === ComponentType.Video) {
              if (!picture.videoUrl) {
                throw new Error('The video path cannot be empty');
              }
            }
            const imgHeight = picture.imgHeight;
            const imgWidth = picture.imgWidth;
            //检查图片宽高
            if (imgHeight < 0 || imgWidth < 0) {
              throw new Error('The width and height of the picture cannot be less than zero');
            }
            const jumps = picture.jump;
            for (let i = 0; i < jumps.length; i++) {
              const jump = jumps[i];
              //检查跳转坐标是否在图片范围内
              if (
                jump.startAxisX > imgWidth ||
                jump.startAxisY > imgHeight ||
                jump.endPointAxisX > imgWidth ||
                jump.endPointAxisY > imgHeight
              ) {
                throw new Error('The coordinates cannot be larger than the width and height of the picture');
              }
              if (jump.startAxisX < 0 || jump.startAxisY < 0 || jump.endPointAxisX < 0 || jump.endPointAxisY < 0) {
                throw new Error('The coordinate cannot be less than zero');
              }
              //检查跳转坐标是否有交集
              for (let l = i + 1; l < jumps.length; l++) {
                const jump2 = jumps[l];
                if (this.intersectionOrNot(jump, jump2)) {
                  throw new Error('Jump click coordinates have intersection');
                }
              }
            }
          }
        }
      } else if (component.type === ComponentType.Product) {
        if (!component.data.productGroup) {
          throw new Error('The product group id cannot be empty when the component type is commodity group');
        }
      }
    }
  }
  /**
   * 判断两个矩形是否有交集
   * @param jump
   * @param jump2
   * @returns
   */
  intersectionOrNot(jump: JumpInput, jump2: JumpInput) {
    const startAxis = {x: jump.startAxisX, y: jump.startAxisY};
    const endPointAxis = {x: jump.endPointAxisX, y: jump.endPointAxisY};
    const startAxis2 = {x: jump2.startAxisX, y: jump2.startAxisY};
    const endPointAxis2 = {x: jump2.endPointAxisX, y: jump2.endPointAxisY};
    // 找到两个矩形的左上角和右下角坐标
    const x1 = Math.min(startAxis.x, endPointAxis.x);
    const y1 = Math.min(startAxis.y, endPointAxis.y);
    const x2 = Math.max(startAxis.x, endPointAxis.x);
    const y2 = Math.max(startAxis.y, endPointAxis.y);

    const x3 = Math.min(startAxis2.x, endPointAxis2.x);
    const y3 = Math.min(startAxis2.y, endPointAxis2.y);
    const x4 = Math.max(startAxis2.x, endPointAxis2.x);
    const y4 = Math.max(startAxis2.y, endPointAxis2.y);

    // 判断两个矩形是否重叠
    if (x1 <= x4 && x2 >= x3 && y1 <= y4 && y2 >= y3) {
      return true;
    } else {
      return false;
    }
  }

  // 定时执行定时发布自定义页面
  async timedPublishingPageAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.timedPublishingPageByCtx(ctx);
    }
  }

  // 定时发布
  async timedPublishingPageByCtx(ctx: RequestContext) {
    const customPages = await this.connection.getRepository(ctx, CustomPage).find({
      where: {
        timingAt: LessThanOrEqual(new Date()),
      },
    });
    for (const customPage of customPages) {
      const customPageInput = customPage.modifiedContent;
      if (!customPageInput) {
        customPage.enable = true;
        customPage.timingAt = null;
        if (customPage.type && customPage.type !== PageType.ActivePage) {
          const homePage = await this.listQueryBuilder
            .build(
              CustomPage,
              {filter: {type: {eq: customPage.type}, enable: {eq: true}}},
              {
                ctx,
                channelId: ctx.channelId,
              },
            )
            .take(1)
            .getOne();
          if (homePage) {
            homePage.type = PageType.ActivePage;
            homePage.smallProgramQRCodeLink = null;
            homePage.h5Link = null;
            homePage.h5QRCode = null;
            await this.cacheService.removeCache(CacheKeyManagerService.customPage(homePage.id, ctx.channelId));
            await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(homePage.id, ctx.channelId));
            await this.connection.getRepository(ctx, CustomPage).save(homePage);
          }
        }
        await this.connection.getRepository(ctx, CustomPage).save(customPage);
        await this.cacheService.removeCache(CacheKeyManagerService.customPage(customPage.id, ctx.channelId));
        await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(customPage.id, ctx.channelId));
        if (customPage?.type && customPage.type !== PageType.ActivePage) {
          await this.cacheService.removeCache(CacheKeyManagerService.customPageByType(customPage.type, ctx.channelId));
          await this.cacheService.removeCache(
            CacheKeyManagerService.customPageComponentByType(customPage.type, ctx.channelId),
          );
        }
      } else {
        await this.upsertCustomPage(ctx, customPageInput, true);
      }
    }
  }
}
