/* eslint-disable @typescript-eslint/no-explicit-any */
import {Injectable} from '@nestjs/common';
import {MemoryStorageService, CacheKeyManagerService} from '@scmally/kvs';
import {LanguageCode} from '@vendure/common/lib/generated-types';
import {
  RequestContext,
  RequestContextCacheService,
  TransactionalConnection,
  Translatable,
  TranslatableKeys,
  Translated,
  TranslatorService,
  VendureEntity,
} from '@vendure/core';
import {FindOneOptions} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';

/**
 * This helper class is to be used in GraphQL entity resolvers, to resolve fields which depend on being
 * translated (i.e. the corresponding entity field is of type `LocaleString`).
 */
@Injectable()
export class LocaleStringHydrator {
  constructor(
    private connection: TransactionalConnection,
    private requestCache: RequestContextCacheService,
    private translator: TranslatorService,
    private memoryStorageService: MemoryStorageService,
  ) {}

  async hydrateLocaleStringField<T extends VendureEntity & Translatable & {languageCode?: LanguageCode}>(
    ctx: RequestContext,
    entity: T,
    fieldName: TranslatableKeys<T> | 'languageCode',
  ): Promise<string> {
    if (entity[fieldName]) {
      // Already hydrated, so return the value
      return entity[fieldName] as any;
    }
    await this.hydrateLocaleStrings(ctx, entity);
    return entity[fieldName] as any;
  }

  /**
   * Takes a translatable entity and populates all the LocaleString fields
   * by fetching the translations from the database (they will be eagerly loaded).
   *
   * This method includes a caching optimization to prevent multiple DB calls when many
   * translatable fields are needed on the same entity in a resolver.
   */
  private async hydrateLocaleStrings<T extends VendureEntity & Translatable>(
    ctx: RequestContext,
    entity: T,
    keyName?: string,
  ): Promise<Translated<T>> {
    const entityType = entity.constructor.name;
    if (!entity.translations?.length) {
      const cacheKey = `hydrate-${entityType}-${entity.id}:${ctx.channelId}`;
      let dbCallPromise = this.requestCache.get<Promise<T | null>>(ctx, cacheKey);
      const memoryStorageCacheKey = CacheKeyManagerService.entityHydrate(entityType, entity.id, ctx.channelId);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      if (ctx.apiType === 'shop' && !dbCallPromise) {
        dbCallPromise = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      if (!dbCallPromise) {
        const cache =
          ctx.apiType === 'shop'
            ? {
                id: memoryStorageCacheKey,
                milliseconds: DEFAULT_CACHE_TIMEOUT,
              }
            : false;
        dbCallPromise = this.connection
          .getRepository<T>(ctx, entityType)
          .findOne({where: {id: entity.id}, cache: cache} as FindOneOptions<T>);
        this.requestCache.set(ctx, cacheKey, dbCallPromise);
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, dbCallPromise);
        }
      }

      await dbCallPromise.then(withTranslations => {
        entity.translations = withTranslations!.translations;
      });
    }
    if (entity.translations.length) {
      const translated = this.translator.translate(entity, ctx);
      for (const localeStringProp of Object.keys(entity.translations[0])) {
        if (
          localeStringProp === 'base' ||
          localeStringProp === 'id' ||
          localeStringProp === 'createdAt' ||
          localeStringProp === 'updatedAt'
        ) {
          continue;
        }
        if (localeStringProp === 'customFields') {
          (entity as any)[localeStringProp] = Object.assign(
            (entity as any)[localeStringProp] ?? {},
            (translated as any)[localeStringProp] ?? {},
          );
        } else {
          (entity as any)[localeStringProp] = (translated as any)[localeStringProp];
        }
      }
    }
    return entity as Translated<T>;
  }
}
