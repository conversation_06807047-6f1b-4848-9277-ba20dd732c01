import {Injectable} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {ID, Product, Promotion, RequestContext, TransactionalConnection} from '@vendure/core';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {ProductPromotionActive} from '../entities';
import {ApplicableProduct, ApplicableType, ProductCustomFields} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {CustomerProductService} from './custom-product.service';
@Injectable()
export class ProductPromotionActiveService {
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private customerProductService: CustomerProductService,
  ) {}

  //添加活动时创建商品对应活动
  async createProductPromotionActive(
    ctx: RequestContext,
    promotion: Promotion,
    applicableProduct: ApplicableProduct,
    oldApplicableProduct?: ApplicableProduct,
  ) {
    //删除原有的
    await this.connection.getRepository(ctx, ProductPromotionActive).delete({
      promotion: {id: promotion.id},
    });
    //创建新的商品参与活动
    const applicableType = applicableProduct.applicableType;
    if (applicableType === ApplicableType.All) {
      const productPromotionActive = new ProductPromotionActive({
        promotion,
        applicableType,
      });
      await this.connection.getRepository(ctx, ProductPromotionActive).save(productPromotionActive);
    } else {
      const productIds = applicableProduct.productIds;
      if (!productIds || productIds.length === 0) return;
      const productPromotionActives = [];
      for (const productId of productIds) {
        const productPromotionActive = new ProductPromotionActive({
          promotion,
          applicableType,
          productId: productId,
        });
        productPromotionActives.push(productPromotionActive);
      }
      await this.connection.getRepository(ctx, ProductPromotionActive).save(productPromotionActives);
    }
    await this.activeProductCacheClear(ctx, applicableProduct, oldApplicableProduct);
  }
  async activeProductCacheClear(
    ctx: RequestContext,
    applicableProduct: ApplicableProduct,
    oldApplicableProduct?: ApplicableProduct,
  ) {
    //创建新的商品参与活动
    const applicableType = applicableProduct.applicableType;
    if (applicableType === ApplicableType.All) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.productPromotionActiveAll(ctx.channelId),
        CacheKeyManagerService.productPromotionActiveAll(ctx.channelId, true),
      ]);
    } else {
      const productIds = applicableProduct.productIds;
      if (!productIds || productIds.length === 0) return;
      if (applicableType === ApplicableType.UnusableGoods) {
        const key = [
          ...productIds.map(productId =>
            CacheKeyManagerService.productPromotionActiveUnusableGoods(productId as ID, ctx.channelId),
          ),
          ...productIds.map(productId =>
            CacheKeyManagerService.productPromotionActiveUnusableGoods(productId as ID, ctx.channelId, true),
          ),
        ];
        await this.cacheService.removeCache(key);
      } else {
        const key = [
          ...productIds.map(productId =>
            CacheKeyManagerService.productPromotionActiveAvailableGoods(productId as ID, ctx.channelId),
          ),
          ...productIds.map(productId =>
            CacheKeyManagerService.productPromotionActiveAvailableGoods(productId as ID, ctx.channelId, true),
          ),
        ];
        await this.cacheService.removeCache(key);
      }
    }
    if (oldApplicableProduct) {
      if (oldApplicableProduct.applicableType === ApplicableType.All) {
        await this.cacheService.removeCache([
          CacheKeyManagerService.productPromotionActiveAll(ctx.channelId),
          CacheKeyManagerService.productPromotionActiveAll(ctx.channelId, true),
        ]);
      } else {
        const productIds = oldApplicableProduct.productIds;
        if (!productIds || productIds.length === 0) return;
        if (applicableType === ApplicableType.UnusableGoods) {
          const key = [
            ...productIds.map(productId =>
              CacheKeyManagerService.productPromotionActiveUnusableGoods(productId as ID, ctx.channelId),
            ),
            ...productIds.map(productId =>
              CacheKeyManagerService.productPromotionActiveUnusableGoods(productId as ID, ctx.channelId, true),
            ),
          ];
          await this.cacheService.removeCache(key);
        } else {
          const key = [
            ...productIds.map(productId =>
              CacheKeyManagerService.productPromotionActiveAvailableGoods(productId as ID, ctx.channelId),
            ),
            ...productIds.map(productId =>
              CacheKeyManagerService.productPromotionActiveAvailableGoods(productId as ID, ctx.channelId, true),
            ),
          ];
          await this.cacheService.removeCache(key);
        }
      }
    }
  }

  // 获取商品参与的活动类型为all的活动
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, isIncludeNotStarted = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeNotStarted) {
          return CacheKeyManagerService.productPromotionActiveAll(ctx.channelId, true);
        } else {
          return CacheKeyManagerService.productPromotionActiveAll(ctx.channelId);
        }
      }
      return '';
    },
  })
  async getParticipatingActivitiesAll(ctx: RequestContext, isIncludeNotStarted = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeNotStarted) {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveAll(ctx.channelId, true);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveAll(ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData.productPromotionActives as ProductPromotionActive[];
      }
    }
    const queryAll = this.connection
      .getRepository(ctx, ProductPromotionActive)
      .createQueryBuilder('productPromotionActive')
      .leftJoinAndSelect('productPromotionActive.promotion', 'promotion')
      .leftJoin('promotion.channels', 'channel')
      .where('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.id = productPromotionActive.promotionId')
      .andWhere('productPromotionActive.applicableType = :applicableType', {
        applicableType: ApplicableType.All,
      });
    // TODO 小程序显示暂时屏蔽购物金活动
    // if (ctx.apiType === 'shop') {
    //   queryAll.andWhere('promotion.customFieldsType not in (:...customFieldsType)', {
    //     customFieldsType: [PromotionType.ShoppingCreditsClaim, PromotionType.ShoppingCreditsDeduction],
    //   });
    // }

    if (!isIncludeNotStarted) {
      queryAll
        .andWhere('promotion.enabled = :enabled', {enabled: true})
        .andWhere('promotion.startsAt <= :now', {now: new Date()})
        .andWhere('promotion.endsAt >= :now', {now: new Date()});
    } else {
      queryAll.andWhere('promotion.enabled = :enabled', {enabled: true});
      queryAll.andWhere('promotion.endsAt > :now', {now: new Date()});
    }

    queryAll.andWhere('promotion.deletedAt is null');
    if (ctx.apiType === 'shop') {
      if (isIncludeNotStarted) {
        queryAll.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        queryAll.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const resultAll = await queryAll.getMany();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {productPromotionActives: resultAll});
    }
    return resultAll;
  }
  // 获取商品参与的活动类型为unusableGoods的活动
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID, isIncludeNotStarted = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeNotStarted) {
          return CacheKeyManagerService.productPromotionActiveUnusableGoods(productId, ctx.channelId, true);
        } else {
          return CacheKeyManagerService.productPromotionActiveUnusableGoods(productId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async getParticipatingActivitiesUnusableGoods(ctx: RequestContext, productId: ID, isIncludeNotStarted = false) {
    let resultUnusableGoods: ProductPromotionActive[] = [];
    let memoryStorageCacheKey = ``;
    let isCacheProductPromotionActive = false;
    if (ctx.apiType === 'shop') {
      if (isIncludeNotStarted) {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveUnusableGoods(
          productId,
          ctx.channelId,
          true,
        );
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveUnusableGoods(productId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        resultUnusableGoods = cacheData.productPromotionActives;
        isCacheProductPromotionActive = true;
      }
    }
    if (!isCacheProductPromotionActive) {
      const queryUnusableGoods = this.connection
        .getRepository(ctx, ProductPromotionActive)
        .createQueryBuilder('productPromotionActive')
        .leftJoinAndSelect('productPromotionActive.promotion', 'promotion')
        .leftJoin('promotion.channels', 'channel')
        .where('channel.id = :channelId', {channelId: ctx.channelId})
        .andWhere('promotion.id = productPromotionActive.promotionId')
        .andWhere('productPromotionActive.applicableType = :unusableGoodsType', {
          unusableGoodsType: ApplicableType.UnusableGoods,
        })
        .andWhere('productPromotionActive.productId != :productId', {productId})
        .andWhere(
          'productPromotionActive.promotionId not in (select productPromotionActive.promotionId from product_promotion_active productPromotionActive where productPromotionActive.productId = :productId)',
          {productId},
        );

      if (!isIncludeNotStarted) {
        queryUnusableGoods
          .andWhere('promotion.enabled = :enabled', {enabled: true})
          .andWhere('promotion.startsAt <= :now', {now: new Date()})
          .andWhere('promotion.endsAt >= :now', {now: new Date()});
      } else {
        queryUnusableGoods.andWhere('promotion.enabled = :enabled', {enabled: true});
        queryUnusableGoods.andWhere('promotion.endsAt > :now', {now: new Date()});
      }

      // TODO 小程序显示暂时屏蔽购物金活动
      // if (ctx.apiType === 'shop') {
      //   queryUnusableGoods.andWhere('promotion.customFieldsType not in (:...customFieldsType)', {
      //     customFieldsType: [PromotionType.ShoppingCreditsClaim, PromotionType.ShoppingCreditsDeduction],
      //   });
      // }

      queryUnusableGoods.andWhere('promotion.deletedAt is null');
      if (ctx.apiType === 'shop') {
        if (isIncludeNotStarted) {
          queryUnusableGoods.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        } else {
          queryUnusableGoods.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
      }
      resultUnusableGoods = await queryUnusableGoods.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, {productPromotionActives: resultUnusableGoods});
      }
    }
    resultUnusableGoods = resultUnusableGoods.filter(productPromotionActive => {
      // 找出所有相同 promotion.id 的 productPromotionActive
      const samePromotionProductPromotionActives = resultUnusableGoods.filter(
        sameProductPromotionActive => sameProductPromotionActive.promotion.id === productPromotionActive.promotion.id,
      );
      // 如果存在多个相同 promotion 的 productPromotionActive
      if (samePromotionProductPromotionActives.length > 1) {
        // 如果当前 productPromotionActive 不是第一个，过滤掉
        if (samePromotionProductPromotionActives[0].id !== productPromotionActive.id) {
          return false;
        }
      }
      // 其他情况直接保留
      return true;
    });
    return resultUnusableGoods;
  }

  // 获取商品参与的活动类型为availableGoods的活动
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID, isIncludeNotStarted = false) => {
      if (ctx.apiType === 'shop') {
        if (isIncludeNotStarted) {
          return CacheKeyManagerService.productPromotionActiveAvailableGoods(productId, ctx.channelId, true);
        } else {
          return CacheKeyManagerService.productPromotionActiveAvailableGoods(productId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async getParticipatingActivitiesAvailableGoods(ctx: RequestContext, productId: ID, isIncludeNotStarted = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isIncludeNotStarted) {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveAvailableGoods(
          productId,
          ctx.channelId,
          true,
        );
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.productPromotionActiveAvailableGoods(productId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData.productPromotionActives as ProductPromotionActive[];
      }
    }
    const queryAvailableGoods = this.connection
      .getRepository(ctx, ProductPromotionActive)
      .createQueryBuilder('productPromotionActive')
      .leftJoinAndSelect('productPromotionActive.promotion', 'promotion')
      .leftJoin('promotion.channels', 'channel')
      .where('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.id = productPromotionActive.promotionId')
      .andWhere('productPromotionActive.applicableType = :availableGoodsType', {
        availableGoodsType: ApplicableType.AvailableGoods,
      })
      .andWhere('productPromotionActive.productId = :productId', {productId});
    // TODO 小程序显示暂时屏蔽购物金活动
    // if (ctx.apiType === 'shop') {
    //   queryAvailableGoods.andWhere('promotion.customFieldsType not in (:...customFieldsType)', {
    //     customFieldsType: [PromotionType.ShoppingCreditsClaim, PromotionType.ShoppingCreditsDeduction],
    //   });
    // }
    if (!isIncludeNotStarted) {
      queryAvailableGoods
        .andWhere('promotion.enabled = :enabled', {enabled: true})
        .andWhere('promotion.startsAt <= :now', {now: new Date()})
        .andWhere('promotion.endsAt >= :now', {now: new Date()});
    } else {
      queryAvailableGoods.andWhere('promotion.enabled = :enabled', {enabled: true});
      queryAvailableGoods.andWhere('promotion.endsAt > :now', {now: new Date()});
    }

    queryAvailableGoods.andWhere('promotion.deletedAt is null');
    if (ctx.apiType === 'shop') {
      if (isIncludeNotStarted) {
        queryAvailableGoods.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      } else {
        queryAvailableGoods.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const resultAvailableGoods = await queryAvailableGoods.getMany();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {productPromotionActives: resultAvailableGoods});
    }
    return resultAvailableGoods;
  }

  // 如果是管理原后台查看的话则需要查询未开始的活动
  async getParticipatingActivities(ctx: RequestContext, productId: ID, isIncludeNotStarted = false, product?: Product) {
    if (!product) {
      product = await this.customerProductService.findOne(ctx, productId);
      if (!product) {
        return [];
      }
    }
    // 如果是虚拟商品则不参与活动
    if ((product.customFields as ProductCustomFields).virtualTargetType) {
      return [];
    }
    // 获取活动类型为all的活动
    const allActivities = await this.getParticipatingActivitiesAll(ctx, isIncludeNotStarted);
    // 获取活动类型为unusableGoods的活动
    const unusableGoodsActivities = await this.getParticipatingActivitiesUnusableGoods(
      ctx,
      productId,
      isIncludeNotStarted,
    );
    // 获取活动类型为availableGoods的活动
    const availableGoodsActivities = await this.getParticipatingActivitiesAvailableGoods(
      ctx,
      productId,
      isIncludeNotStarted,
    );
    // 合并所有活动
    const productPromotionActives = [...allActivities, ...unusableGoodsActivities, ...availableGoodsActivities];
    const activities = productPromotionActives.map(productPromotionActive => {
      return productPromotionActive.promotion;
    });
    return activities;
  }
}
