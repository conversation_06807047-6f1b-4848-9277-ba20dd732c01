import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  idsAreEqual,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  ProductService,
  ProductVariant,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {SelectiveGiftActivity} from '../entities';
import {
  ActivityStatus,
  DiscountType,
  ProgramLinkInput,
  PromotionConditionType,
  PromotionType,
  RuleType,
  SelectiveGiftActivityInput,
} from '../generated-admin-types';
import {ApplicableProduct, DeletionResult, FreeGiftValue, RuleValue} from '../generated-shop-types';
import {circulationDiscount, ladderDiscount} from '../promotion/action';
import {customerGroupList, productQuantityContain} from '../promotion/conditions';
import {ActivityProduct} from '../utils/activity-utils';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {FullDiscountPresentService} from './full-discount-present.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
@Injectable()
export class SelectiveGiftActivityService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private promotionService: PromotionService,
    private promotionResultDetailService: PromotionResultDetailService,
    private kvsService: KvsService,
    private fullDiscountPresentService: FullDiscountPresentService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private productService: ProductService,
    private commonService: CommonService,
    private requestContextService: RequestContextService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private productCustomService: ProductCustomService,
  ) {}

  async selectiveGiftActivityAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.selectiveGiftActivity(ctx);
    }
  }

  async selectiveGiftActivity(ctx: RequestContext) {
    const notYetStarted = await this.connection
      .getRepository(ctx, SelectiveGiftActivity)
      .createQueryBuilder('selectiveGiftActivity')
      .leftJoinAndSelect('selectiveGiftActivity.promotion', 'promotion')
      .leftJoinAndSelect('selectiveGiftActivity.channels', 'channels')
      .andWhere('selectiveGiftActivity.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('selectiveGiftActivity.startTime <= :startTime', {startTime: new Date()})
      .andWhere('selectiveGiftActivity.endTime > :endTime', {endTime: new Date()})
      .getMany();
    const notYetStartedIds = notYetStarted.map(item => item.id);
    const promotionIds = notYetStarted.map(item => item.promotion.id);
    if (notYetStartedIds && notYetStartedIds.length > 0) {
      for (const item of notYetStarted) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, SelectiveGiftActivity)
        .update({id: In(notYetStartedIds)}, {status: ActivityStatus.Normal});
      const notYetStartedKeys = notYetStarted.map(item =>
        CacheKeyManagerService.selectiveGiftActivity(item.id, ctx.channelId),
      );
      await this.cacheService.removeCache(notYetStartedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      notYetStarted.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
      });
    }
    if (promotionIds && promotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(promotionIds)}, {enabled: true});
      const notYetStartedPromotionKeys = [
        ...promotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...promotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(notYetStartedPromotionKeys);
    }
    const haveEnded = await this.connection
      .getRepository(ctx, SelectiveGiftActivity)
      .createQueryBuilder('selectiveGiftActivity')
      .leftJoinAndSelect('selectiveGiftActivity.promotion', 'promotion')
      .leftJoinAndSelect('selectiveGiftActivity.channels', 'channels')
      .andWhere('selectiveGiftActivity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('selectiveGiftActivity.endTime <= :endTime', {endTime: new Date()})
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    const haveEndedPromotionIds = haveEnded.map(item => item.promotion.id);
    if (haveEndedIds && haveEndedIds.length > 0) {
      for (const item of haveEnded) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, SelectiveGiftActivity)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
      const haveEndedKeys = haveEnded.map(item => CacheKeyManagerService.selectiveGiftActivity(item.id, ctx.channelId));
      await this.cacheService.removeCache(haveEndedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      haveEnded.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
      });
    }
    if (haveEndedPromotionIds && haveEndedPromotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(haveEndedPromotionIds)}, {enabled: false});
      const haveEndedPromotionKeys = [
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(haveEndedPromotionKeys);
    }
  }

  async getSelectiveGiftActivityLink(ctx: RequestContext, input: ProgramLinkInput) {
    const selectiveGiftActivityId = input.id;
    const selectiveGiftActivity = await this.findOne(ctx, selectiveGiftActivityId);
    if (!selectiveGiftActivity) {
      throw new EntityNotFoundError('SelectiveGiftActivity', selectiveGiftActivityId);
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(selectiveGiftActivity.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }
  async getSelectiveGiftActivityQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const selectiveGiftActivityId = input.id;
    const selectiveGiftActivity = await this.findOne(ctx, selectiveGiftActivityId);
    if (!selectiveGiftActivity) {
      throw new EntityNotFoundError('SelectiveGiftActivity', selectiveGiftActivityId);
    }
    if (selectiveGiftActivity.smallProgramQRCodeLink) {
      return selectiveGiftActivity.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(selectiveGiftActivity.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    selectiveGiftActivity.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, SelectiveGiftActivity).save(selectiveGiftActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.selectiveGiftActivity(selectiveGiftActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(selectiveGiftActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(selectiveGiftActivity.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }
  async activityContent(ctx: RequestContext, selectiveGiftActivity: SelectiveGiftActivity) {
    const type = selectiveGiftActivity.type;
    const ruleValues = selectiveGiftActivity.ruleValues;
    const ruleType = selectiveGiftActivity.ruleType;
    if (ruleType === RuleType.Ladder) {
      return this.ladderRuleValues(ctx, ruleValues, type);
    } else if (ruleType === RuleType.Cycle) {
      return this.cycleRuleValues(ctx, ruleValues, type);
    }
  }
  async cycleRuleValues(ctx: RequestContext, ruleValues: RuleValue[], type: PromotionConditionType) {
    let fullPresent = '';
    if (type === PromotionConditionType.Quantity) {
      fullPresent = `满${ruleValues[0].minimum}件,送${ruleValues[0]?.maximumOffer}种赠品`;
    } else if (type === PromotionConditionType.Amount) {
      fullPresent = `满${ruleValues[0].minimum / 100}元,送${ruleValues[0]?.maximumOffer}种赠品`;
    }
    const freeGiftValues = ruleValues[0].freeGiftValues as FreeGiftValue[];
    const giveaway = await this.fullDiscountPresentService.getGiftsStr(ctx, freeGiftValues);
    if (giveaway?.length > 0) {
      return [`${fullPresent}【${giveaway.join('、')}】`];
    } else {
      return [`${fullPresent} 暂无赠品`];
    }
  }
  async ladderRuleValues(ctx: RequestContext, ruleValues: RuleValue[], type: PromotionConditionType) {
    const fullPresent = [];
    for (const ruleValue of ruleValues) {
      let commonStr = '';
      if (type === PromotionConditionType.Quantity) {
        commonStr = `满${ruleValue.minimum}件`;
      } else if (type === PromotionConditionType.Amount) {
        commonStr = `满${ruleValue.minimum / 100}元`;
      }
      if (ruleValue.freeGiftValues && ruleValue.freeGiftValues.length > 0) {
        const fullPresentStr = `${commonStr},送${ruleValue.maximumOffer}种赠品`;
        const freeGiftValues = ruleValue.freeGiftValues as FreeGiftValue[];
        const giveaway = await this.fullDiscountPresentService.getGiftsStr(ctx, freeGiftValues);
        if (giveaway?.length > 0) {
          fullPresent.push(`${fullPresentStr}【${giveaway.join('、')}】`);
        } else {
          fullPresent.push(`${fullPresentStr} 暂无赠品`);
        }
      }
    }
    return fullPresent;
  }
  async activitySynopsis(ctx: RequestContext, selectiveGiftActivity: SelectiveGiftActivity) {
    let synopsisStr = ``;
    const ruleValues = selectiveGiftActivity.ruleValues;
    const ruleType = selectiveGiftActivity.ruleType;
    const type = selectiveGiftActivity.type;
    if (ruleType === RuleType.Ladder) {
      const maxDiscount = ruleValues[ruleValues.length - 1];
      if (maxDiscount.freeGiftValues && maxDiscount.freeGiftValues.length > 0) {
        synopsisStr += `最高送${maxDiscount.maximumOffer}种赠品`;
      }
    } else if (ruleType === RuleType.Cycle) {
      const discount = ruleValues[0];
      let commonStr = '';
      if (type === PromotionConditionType.Quantity) {
        commonStr = `满${discount.minimum}件`;
      } else if (type === PromotionConditionType.Amount) {
        commonStr = `满${discount.minimum / 100}元`;
      }
      if (discount?.freeGiftValues && discount.freeGiftValues.length > 0) {
        synopsisStr += `${commonStr} 送${discount.maximumOffer}种赠品`;
      } else {
        synopsisStr += `满减送活动`;
      }
    }
    return synopsisStr;
  }
  async activityGifts(ctx: RequestContext, selectiveGiftActivity: SelectiveGiftActivity) {
    const ruleValues = selectiveGiftActivity.ruleValues;
    const freeGiftItems: FreeGiftValue[] = [];
    for (const ruleValue of ruleValues) {
      const freeGiftValues = ruleValue.freeGiftValues;
      if (freeGiftValues) {
        for (const freeGiftValue of freeGiftValues) {
          if (freeGiftValue) {
            freeGiftItems.push(freeGiftValue);
          }
        }
      }
    }
    const productIds = freeGiftItems.map(item => item.freeGiftProductId) as ID[];
    if (productIds.length === 0) {
      return [];
    }
    let products = await this.productService.findByIds(ctx, productIds);
    products = products.sort((a, b) => {
      const itemA = freeGiftItems.find(item => idsAreEqual(item.freeGiftProductId as ID, a.id));
      const itemB = freeGiftItems.find(item => idsAreEqual(item.freeGiftProductId as ID, b.id));
      const priorityA = itemA?.priority ?? 0;
      const priorityB = itemB?.priority ?? 0;
      return priorityA - priorityB;
    });
    return products;
  }

  async upsertSelectiveGiftActivity(ctx: RequestContext, input: SelectiveGiftActivityInput) {
    await this.validate(ctx, input);
    // 修改前的可用商品
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const selectiveGiftActivity = await this.findOne(ctx, input.id);
      if (!selectiveGiftActivity) {
        throw new EntityNotFoundError('SelectiveGiftActivity', input.id);
      }
      oldApplicableProduct = selectiveGiftActivity.applicableProduct;
    }
    let status = ActivityStatus.Normal;
    if (new Date(input.startTime) > new Date()) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date(input.endTime) < new Date()) {
      status = ActivityStatus.HaveEnded;
    }
    let selectiveGiftActivity = new SelectiveGiftActivity({
      ...(input as unknown as SelectiveGiftActivity),
      status,
    });
    for (const ruleValue of selectiveGiftActivity.ruleValues) {
      const freeGiftValues = ruleValue.freeGiftValues;
      if (!freeGiftValues) {
        continue;
      }
      for (const freeGiftValue of freeGiftValues) {
        if (!freeGiftValue) {
          continue;
        }
        const freeGiftProductId = freeGiftValue?.freeGiftProductId;
        if (freeGiftProductId) {
          const productVariant = await this.connection
            .getRepository(ctx, ProductVariant)
            .createQueryBuilder('productVariant')
            .leftJoinAndSelect('productVariant.productVariantPrices', 'productVariantPrices')
            .andWhere('productVariant.productId = :productId', {productId: freeGiftProductId})
            .orderBy('productVariantPrices.price', 'ASC')
            .take(1)
            .getOne();
          if (!productVariant) {
            return freeGiftValue;
          }
          freeGiftValue.skuId = String(productVariant.id);
        }
      }
    }
    selectiveGiftActivity = await this.channelService.assignToCurrentChannel(selectiveGiftActivity, ctx);
    selectiveGiftActivity = await this.connection.getRepository(ctx, SelectiveGiftActivity).save(selectiveGiftActivity);
    const promotion = await this.upsertPromotion(ctx, selectiveGiftActivity);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      input.applicableProduct,
      oldApplicableProduct,
    );
    selectiveGiftActivity.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, SelectiveGiftActivity).save(selectiveGiftActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.selectiveGiftActivity(selectiveGiftActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
    ]);
    return this.findOne(ctx, selectiveGiftActivity.id as ID);
  }
  async upsertPromotion(ctx: RequestContext, selectiveGiftActivity: SelectiveGiftActivity): Promise<Promotion> {
    const type = PromotionType.SelectiveGift;
    const promotionInput = {
      couponCode: selectiveGiftActivity.promotion ? selectiveGiftActivity.promotion.couponCode : generatePublicId(),
      name: selectiveGiftActivity.displayName,
      startsAt: selectiveGiftActivity.startTime,
      endsAt: selectiveGiftActivity.endTime,
      enabled:
        selectiveGiftActivity.status === ActivityStatus.Normal ||
        selectiveGiftActivity.status === ActivityStatus.NotStarted,
      conditions: [],
      actions: [],
      customFields: {
        type: type,
        isAutomatic: true,
        activityName: selectiveGiftActivity.displayName,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: selectiveGiftActivity.displayName,
        },
      ],
    };
    const minimum = selectiveGiftActivity.ruleValues[0].minimum;
    promotionInput.conditions.push({
      code: productQuantityContain.code,
      arguments: [
        {name: 'productIds', value: selectiveGiftActivity.applicableProduct.productIds},
        {name: 'quantity', value: minimum},
        {name: 'type', value: selectiveGiftActivity.applicableProduct.applicableType},
        {name: 'fullDiscountPresentType', value: selectiveGiftActivity.type},
      ],
    } as never);
    if (selectiveGiftActivity.whetherRestrictUsers) {
      //是否限制参与用户
      promotionInput.conditions.push({
        code: customerGroupList.code,
        arguments: [
          {name: 'isOpen', value: selectiveGiftActivity.whetherRestrictUsers},
          {name: 'groupType', value: selectiveGiftActivity.groupType},
          {name: 'customerGroupIds', value: selectiveGiftActivity.memberPlanIds},
        ],
      } as never);
    }
    // 创建优惠类型为循环的优惠动作
    if (selectiveGiftActivity.ruleType === RuleType.Cycle) {
      promotionInput.actions.push({
        code: circulationDiscount.code,
        arguments: [
          {name: 'productIds', value: selectiveGiftActivity.applicableProduct.productIds},
          {name: 'type', value: selectiveGiftActivity.applicableProduct.applicableType},
          {name: 'rules', value: selectiveGiftActivity.ruleValues},
          {name: 'fullDiscountPresentType', value: selectiveGiftActivity.type},
        ],
      } as never);
      // 创建优惠类型为阶梯的优惠动作
    } else if (selectiveGiftActivity.ruleType === RuleType.Ladder) {
      // 创建优惠类型为阶梯的优惠动作
      promotionInput.actions.push({
        code: ladderDiscount.code,
        arguments: [
          {name: 'productIds', value: selectiveGiftActivity.applicableProduct.productIds},
          {name: 'type', value: selectiveGiftActivity.applicableProduct.applicableType},
          {name: 'rules', value: selectiveGiftActivity.ruleValues},
          {name: 'fullDiscountPresentType', value: selectiveGiftActivity.type},
        ],
      } as never);
    }
    selectiveGiftActivity = (await this.findOne(ctx, selectiveGiftActivity.id as ID)) as SelectiveGiftActivity;
    if (selectiveGiftActivity?.promotion) {
      const updatePromotion = {
        ...promotionInput,
        id: selectiveGiftActivity.promotion.id,
        customFields: {
          type: type,
          isAutomatic: true,
          stackingDiscountSwitch: selectiveGiftActivity.stackingDiscountSwitch,
          stackingPromotionTypes: selectiveGiftActivity.stackingPromotionTypes,
          activityName: selectiveGiftActivity.displayName,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: type,
          isAutomatic: true,
          stackingDiscountSwitch: selectiveGiftActivity.stackingDiscountSwitch,
          stackingPromotionTypes: selectiveGiftActivity.stackingPromotionTypes,
          activityName: selectiveGiftActivity.displayName,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: SelectiveGiftActivityInput) {
    if (!input.startTime || !input.endTime) {
      throw new Error('请选择活动时间');
    }
    if (input.startTime >= input.endTime) {
      throw new Error('开始时间不能大于结束时间');
    }
    const ruleValues = input.ruleValues;
    const ruleType = input.ruleType;
    if (ruleValues.length <= 0) {
      throw new Error('活动规则不能为空');
    }
    if (ruleType === RuleType.Cycle) {
      if (ruleValues.length !== 1) {
        throw new Error('周期活动规则只能存在一个');
      }
    }
    for (const rule of ruleValues) {
      const discountValue = rule.discountValue;
      if (discountValue) {
        if (discountValue.discountType !== DiscountType.NoDiscount) {
          throw new Error('任选增送活动不支持优惠');
        }
      }
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      productIds: input.applicableProduct.productIds as ID[],
      applicableType: input.applicableProduct.applicableType,
    };
    await this.fullDiscountPresentService.verifyProductIdsAndSelectiveGiftActivityConflict(
      ctx,
      inputProduct,
      input.id as ID,
    );
  }
  async softDeleteSelectiveGiftActivity(ctx: RequestContext, id: ID) {
    const selectiveGiftActivity = await this.findOne(ctx, id);
    if (!selectiveGiftActivity) {
      throw new Error(`SelectiveGiftActivity with id ${id} not found`);
    }
    if (selectiveGiftActivity.deletedAt) {
      throw new Error(`SelectiveGiftActivity with id ${id} is already deleted`);
    }
    selectiveGiftActivity.deletedAt = new Date();
    selectiveGiftActivity.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(selectiveGiftActivity.promotion.id, {
      enabled: false,
    });
    await this.promotionService.softDeletePromotion(ctx, selectiveGiftActivity.promotion.id);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, SelectiveGiftActivity).save(selectiveGiftActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.selectiveGiftActivity(selectiveGiftActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(selectiveGiftActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(selectiveGiftActivity.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, selectiveGiftActivity.applicableProduct);
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }
  async failureSelectiveGiftActivity(ctx: RequestContext, id: ID) {
    const selectiveGiftActivity = await this.findOne(ctx, id);
    if (!selectiveGiftActivity) {
      throw new Error(`SelectiveGiftActivity with id ${id} not found`);
    }
    if (selectiveGiftActivity.status === 'failure') {
      throw new Error(`SelectiveGiftActivity with id ${id} is already failure`);
    }
    selectiveGiftActivity.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(selectiveGiftActivity.promotion.id, {
      enabled: false,
    });
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, SelectiveGiftActivity).save(selectiveGiftActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.selectiveGiftActivity(selectiveGiftActivity.id, ctx.channelId),
      CacheKeyManagerService.promotion(selectiveGiftActivity.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(selectiveGiftActivity.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, selectiveGiftActivity.applicableProduct);
    return this.findOne(ctx, selectiveGiftActivity.id as ID);
  }

  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<SelectiveGiftActivity>,
    relations?: RelationPaths<SelectiveGiftActivity>,
  ) {
    let items: SelectiveGiftActivity[] = [];
    const memoryStorageCacheKey = CacheKeyManagerService.selectiveGiftActivity(id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      items = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!items || items.length === 0) {
      const qb = this.listQueryBuilder.build(SelectiveGiftActivity, options, {
        relations: [], // (relations ?? []).concat(['promotion']),
        channelId: ctx.channelId,
        ctx,
      });
      qb.andWhere(`${qb.alias}.id = :id`, {id});
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      items = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, items);
      }
    }
    if (items?.length > 0) {
      const selectiveGiftActivity = items[0];
      const promotionId = selectiveGiftActivity.promotionId;
      if (promotionId) {
        const promotionMemoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        const cache =
          ctx.apiType === 'shop'
            ? {
                id: promotionMemoryStorageCacheKey,
                milliseconds: DEFAULT_CACHE_TIMEOUT,
              }
            : false;
        let promotion: Promotion | undefined | null;
        if (ctx.apiType === 'shop') {
          promotion = this.memoryStorageService.get(promotionMemoryStorageCacheKey);
        }
        if (!promotion) {
          promotion = await this.connection.getRepository(ctx, Promotion).findOne({
            where: {id: promotionId},
            cache: cache,
          });
          if (ctx.apiType === 'shop') {
            this.memoryStorageService.set(promotionMemoryStorageCacheKey, promotion);
          }
        }
        selectiveGiftActivity.promotion = promotion as Promotion;
      }
      return selectiveGiftActivity;
    }
    return null;
  }
  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<SelectiveGiftActivity>,
    relations: RelationPaths<SelectiveGiftActivity>,
    isAdmin?: boolean,
  ) {
    const qb = this.listQueryBuilder.build(SelectiveGiftActivity, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length) {
      for (const item of items) {
        const type = PromotionType.SelectiveGift;
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          type,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {items, totalItems};
  }
}
