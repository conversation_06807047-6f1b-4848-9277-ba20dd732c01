import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  ProductVariant,
  RelationPaths,
  RequestContext,
  TaxCategory,
  TransactionalConnection,
} from '@vendure/core';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PointsProduct, PointsProductSku} from '../entities';
import {DeletionResult, ExchangeConditionType, PointsProductInput} from '../generated-admin-types';
import {ActivityStatus, ProductCustomFields, PromotionType, PutOnSaleType} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
import {CustomerProductService} from './custom-product.service';
import {CustomerPointsService} from './customer-points.service';
import {InterfaceCommonCustomer} from './interface-customer';
import {PointsConfigService} from './points-config.service';
import {ProductCustomService} from './product-custom.service';
@Injectable()
export class PointsProductService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private pointsConfigService: PointsConfigService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
    private customProductVariantService: CustomerProductVariantService,
    private cacheService: CacheService,
    private productCustomService: ProductCustomService,
    private customerProductService: CustomerProductService,
    private customerPointsService: CustomerPointsService,
  ) {}

  public interfaceCustomer: InterfaceCommonCustomer;

  registerCustomer(interfaceCustomer: InterfaceCommonCustomer) {
    this.interfaceCustomer = interfaceCustomer;
  }

  async getSkuPointsExchange(ctx: RequestContext, productVariant: ProductVariant) {
    const productId = productVariant.productId;
    const skuId = productVariant.id;
    const pointsProduct = await this.getPointsExchangeInfo(ctx, productId);
    if (!pointsProduct) {
      return;
    }
    const pointsProductSkus = await this.getPointsProductSkus(ctx, pointsProduct);
    if (!pointsProductSkus) {
      return;
    }
    const pointsProductSku = pointsProductSkus.find(sku => idsAreEqual(sku.productVariantId, skuId));
    if (!pointsProductSku?.enabled) {
      return;
    }
    return {
      points: pointsProductSku.points,
      cash: pointsProductSku.cash,
      exchangeTotal: pointsProductSku.exchangeableCount,
      exchangeCount: pointsProductSku.exchangeCount,
    };
  }

  async getPointsProductSkus(ctx: RequestContext, pointsProduct: PointsProduct): Promise<PointsProductSku[]> {
    if (
      pointsProduct.pointsProductSkus &&
      pointsProduct.exchangeConditionType === ExchangeConditionType.CustomExchangeRatio
    ) {
      return pointsProduct.pointsProductSkus;
    }
    const pointsProductId = pointsProduct.id;
    const qb = this.connection.getRepository(ctx, PointsProductSku).createQueryBuilder('pointsProductSku');
    qb.leftJoinAndSelect('pointsProductSku.productVariant', 'productVariant');
    qb.leftJoinAndSelect('productVariant.productVariantPrices', 'productVariantPrices');
    qb.andWhere('pointsProductSku.pointsProductId = :pointsProductId', {pointsProductId});
    const items = await qb.getMany();
    if (pointsProduct.exchangeConditionType === ExchangeConditionType.CustomExchangeRatio) {
      return items;
    }
    const pointsConfigUpdateTime = await this.kvsService.pointsConfigUpdateTime.get(String(ctx.channelId));
    if (!pointsConfigUpdateTime || new Date(pointsConfigUpdateTime) < pointsProduct.lastModifyTime) {
      return items;
    }
    const pointsConfig = await this.pointsConfigService.findOne(ctx);
    const variants = await this.getPointsProductVariants(ctx, items);
    const pointsExchangeRate = pointsConfig?.pointsExchangeRate ?? 0;
    items.forEach(sku => {
      const productVariantId = sku.productVariantId;
      const price = variants.find(variant => idsAreEqual(variant.id, productVariantId))?.price ?? 0;
      sku.points = Math.ceil((price * pointsExchangeRate) / 100);
      sku.cash = 0;
    });
    return items;
  }
  async getPointsProductVariants(ctx: RequestContext, items: PointsProductSku[]) {
    let productVariants = items.map(sku => sku.productVariant);
    const taxCategory = await this.customProductVariantService.getChannelTaxCategory(ctx);
    for (const variant of productVariants) {
      variant.taxCategory = taxCategory as TaxCategory;
    }
    productVariants = await this.customProductVariantService.applyPricesAndTranslateVariants(ctx, productVariants);
    return productVariants;
  }

  async getPointExchange(ctx: RequestContext, pointsProduct: PointsProduct) {
    const pointsProductSkus = await this.getPointsProductSkus(ctx, pointsProduct);
    const enabledProductSku = pointsProductSkus.filter(sku => sku.enabled);
    // 按照积分排序 如果积分相同按照金额排序
    enabledProductSku.sort((a, b) => {
      if (a.points === b.points) {
        return a.cash - b.cash;
      }
      return a.points - b.points;
    });
    if (enabledProductSku.length === 0) {
      return;
    }
    // 可兑换总数
    const exchangeTotal = enabledProductSku.reduce((prev, current) => {
      return prev + current.exchangeableCount;
    }, 0);
    // 已经兑换总数
    const exchangeCount = enabledProductSku.reduce((prev, current) => {
      return prev + current.exchangeCount;
    }, 0);

    return {
      points: enabledProductSku[0].points,
      cash: enabledProductSku[0].cash,
      // 可兑换总数
      exchangeTotal: exchangeTotal,
      // 已经兑换总数
      exchangeCount: exchangeCount,
    };
  }
  async getPointsExchangeProductIds(ctx: RequestContext, startTime: Date, endTime: Date) {
    const qb = this.connection.getRepository(ctx, PointsProduct).createQueryBuilder('pointsProduct');
    qb.leftJoin('pointsProduct.channels', 'channel');
    qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    qb.andWhere('pointsProduct.deletedAt IS NULL');
    qb.select('pointsProduct.productId', 'productId');
    const products = await qb.getRawMany<{productId: ID}>();
    if (!products || products.length === 0) {
      return [];
    }
    return products.map(product => product.productId);
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID) => {
      return CacheKeyManagerService.pointsProduct(productId, ctx.channelId);
    },
  })
  async getPointsExchangeInfo(ctx: RequestContext, productId: ID): Promise<PointsProduct> {
    const memoryKey = CacheKeyManagerService.pointsProduct(productId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(memoryKey);
      if (memoryData) {
        return memoryData.pointsProduct;
      }
    }
    const qb = this.connection.getRepository(ctx, PointsProduct).createQueryBuilder('pointsProduct');
    qb.leftJoinAndSelect('pointsProduct.pointsProductSkus', 'pointsProductSkus');
    qb.andWhere('pointsProduct.productId = :productId', {productId});
    qb.leftJoinAndSelect('pointsProduct.channels', 'channel');
    if (ctx.apiType === 'shop') {
      qb.andWhere('pointsProduct.status = :status', {status: ActivityStatus.Normal});
    } else {
      qb.andWhere('pointsProduct.status in (:...status)', {
        status: [ActivityStatus.Normal, ActivityStatus.NotStarted],
      });
    }
    qb.andWhere('pointsProduct.deletedAt IS NULL');
    qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (ctx.apiType === 'shop') {
      qb.cache(memoryKey, DEFAULT_CACHE_TIMEOUT);
    }
    const pointsProduct = (await qb.getOne()) as PointsProduct;
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryKey, {pointsProduct: pointsProduct});
    }
    return pointsProduct;
  }

  async upsertPointsProduct(ctx: RequestContext, input: PointsProductInput) {
    await this.validate(ctx, input);
    let status = ActivityStatus.Normal;
    if (new Date(input.startTime) > new Date()) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date(input.endTime) < new Date()) {
      status = ActivityStatus.HaveEnded;
    }
    let pointsProduct = new PointsProduct({
      id: input.id,
      product: {id: input.productId},
      productType: input.productType,
      startTime: input.startTime,
      endTime: input.endTime,
      allowPurchaseAtOriginalPrice: input.allowPurchaseAtOriginalPrice,
      exchangeConditionType: input.exchangeConditionType,
      exchangeLimit: input.exchangeLimit,
      stackingDiscountSwitch: input.stackingDiscountSwitch,
      stackingPromotionTypes: (input.stackingPromotionTypes ?? []) as PromotionType[],
      lastModifyTime: new Date(),
      status,
    });
    pointsProduct = await this.channelService.assignToCurrentChannel(pointsProduct, ctx);
    pointsProduct = await this.connection.getRepository(ctx, PointsProduct).save(pointsProduct);
    const pointsProductSkus = input.pointsProductSkus.map(sku => {
      return {
        id: sku?.id as ID,
        productVariant: {id: sku?.productVariantId},
        enabled: sku?.enabled,
        points: sku?.points,
        cash: sku?.cash,
        exchangeTotal: sku?.exchangeTotal,
        pointsProduct: pointsProduct,
      };
    });
    await this.connection.getRepository(ctx, PointsProductSku).save(pointsProductSkus as PointsProductSku[]);
    await this.cacheService.removeCache(CacheKeyManagerService.pointsProduct(input.productId, ctx.channelId));
    return pointsProduct;
  }

  async validate(ctx: RequestContext, input: PointsProductInput) {
    //验证活动时间
    if (input.startTime >= input.endTime) {
      throw new Error('活动开始时间不能大于结束时间');
    }
    if (!input.productId) {
      throw new Error('请选择商品');
    }
    const productId = input.productId;
    const product = await this.customerProductService.findOne(ctx, productId);
    const productCustomFields = product?.customFields as ProductCustomFields;
    if (productCustomFields.putOnSaleType === PutOnSaleType.Scheduled) {
      // if (new Date(productCustomFields.putOnSaleTime) > new Date(input.startTime)) {
      //   throw new Error('活动开始时间不能小于商品上架时间');
      // }
    }
    if (productCustomFields.timedTakedown) {
      if (new Date(productCustomFields.takedownTime) < new Date(input.endTime)) {
        throw new Error('活动结束时间不能大于商品下架时间');
      }
    }
    if (!input.id) {
      const pointsProduct = await this.connection
        .getRepository(ctx, PointsProduct)
        .createQueryBuilder('pointsProduct')
        .leftJoin('pointsProduct.channels', 'channel')
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
        .andWhere('pointsProduct.productId = :productId', {productId: input.productId})
        .andWhere('pointsProduct.deletedAt IS NULL')
        .getCount();
      if (pointsProduct > 0) {
        throw new Error('该商品已存在积分活动');
      }
    }

    if (!input.productType) {
      throw new Error('请选择商品类型');
    }
    if (!input.exchangeConditionType) {
      throw new Error('请选择兑换条件类型');
    }
    const pointsConfig = await this.pointsConfigService.findOne(ctx);
    if (input.exchangeConditionType === ExchangeConditionType.UnifiedExchangeRatio) {
      if (!pointsConfig?.pointsExchangeRate) {
        throw new Error('请先设置积分兑换比例');
      }
    }
    if (!input.exchangeLimit || input.exchangeLimit < 0) {
      throw new Error('请输入兑换限制/人');
    }
    const pointsProducts = input.pointsProductSkus;
    if (!pointsProducts || pointsProducts.length <= 0) {
      throw new Error('请添加规格');
    }
    for (const pointsProduct of pointsProducts) {
      if (!pointsProduct?.enabled) {
        continue;
      }
      if (!pointsProduct.productVariantId) {
        throw new Error('请选择规格');
      }
      if ((pointsProduct.exchangeTotal ?? -1) < 0) {
        throw new Error('可兑换总数不能小于0');
      }
      const productVariantId = pointsProduct.productVariantId;
      const productVariant = await this.customProductVariantService.getVariantWithPriceAndTax(ctx, productVariantId);
      if (!productVariant) {
        throw new Error('商品规格已禁用或者已删除');
      }
      if (input.exchangeConditionType === ExchangeConditionType.CustomExchangeRatio) {
        // 自定义兑换比例 积分和金额必填并且要大于0
        if (!pointsProduct.points || pointsProduct.points <= 0) {
          throw new Error('请输入积分');
        }
        if ((pointsProduct.cash ?? -1) < 0) {
          throw new Error('兑换金额不能小于0');
        }
        if ((pointsProduct.cash ?? 0) >= productVariant.price) {
          throw new Error('金额不能大于商品价格');
        }
      } else if (input.exchangeConditionType === ExchangeConditionType.UnifiedExchangeRatio) {
        // 统一兑换比例 金额只能为0
        pointsProduct.cash = 0;
        const points = Math.ceil((productVariant.price * pointsConfig.pointsExchangeRate) / 100);
        pointsProduct.points = points;
      }
      const stock = await this.customProductVariantService.getSaleableStockLevel(ctx, productVariant);
      // 剩余可兑换总数
      let residueExchangeTotal = pointsProduct.exchangeTotal ?? 0; // 可兑换总数
      if (pointsProduct.id) {
        const old = await this.connection.getRepository(ctx, PointsProductSku).findOne({where: {id: pointsProduct.id}});
        if (old) {
          residueExchangeTotal = residueExchangeTotal - old.exchangeCount; // 减去原来的可兑换总数
          if (residueExchangeTotal < 0) {
            throw new Error('可兑换总数不能小于已兑换数量');
          }
        }
      }
      if (residueExchangeTotal > stock) {
        throw new Error('可兑换总数不能大于库存');
      }
    }
  }

  async softDeletePointsProduct(ctx: RequestContext, id: ID) {
    const pointsProduct = await this.findOne(ctx, id);
    if (pointsProduct) {
      pointsProduct.deletedAt = new Date();
      await this.connection.getRepository(ctx, PointsProduct).save(pointsProduct);
      await this.cacheService.removeCache(CacheKeyManagerService.pointsProduct(pointsProduct.productId, ctx.channelId));
    }

    return {
      result: DeletionResult.Deleted,
      message: '删除成功',
    };
  }

  async getPointsProducts(
    ctx: RequestContext,
    isAllowExchange: boolean,
    options: ListQueryOptions<PointsProduct>,
    relations: RelationPaths<PointsProduct>,
  ) {
    const qb = this.listQueryBuilder.build(PointsProduct, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.product`, 'product');
    qb.andWhere('pointsProduct.status = :status', {status: ActivityStatus.Normal});
    qb.andWhere('product.enabled = :enabled', {enabled: true});
    qb.andWhere('product.deletedAt IS NULL');
    qb.leftJoinAndSelect(`${qb.alias}.pointsProductSkus`, 'pointsProductSkus');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    if (isAllowExchange) {
      const customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new Error('用户未登录');
      }
      const points = await this.customerPointsService.findCustomerPoints(ctx, customer.id);
      qb.andWhere('pointsProductSkus.enabled = :enabled', {enabled: true});
      qb.andWhere('pointsProductSkus.points <= :points', {points: points});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async findAll(
    ctx: RequestContext,
    productName: String,
    options?: ListQueryOptions<PointsProduct>,
    relations?: RelationPaths<PointsProduct>,
  ) {
    const qb = this.listQueryBuilder.build(PointsProduct, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.product`, 'product');
    if (productName) {
      qb.leftJoin('product.translations', 'translations');
      qb.andWhere(`translations.name like :productName`, {productName: `%${productName}%`});
    }
    qb.leftJoinAndSelect(`${qb.alias}.pointsProductSkus`, 'pointsProductSkus');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async findOne(ctx: RequestContext, id: ID, relations?: RelationPaths<PointsProduct>) {
    const qb = this.connection.getRepository(ctx, PointsProduct).createQueryBuilder('pointsProduct');
    qb.leftJoinAndSelect('pointsProduct.product', 'product');
    qb.leftJoinAndSelect('pointsProduct.pointsProductSkus', 'pointsProductSkus');
    qb.andWhere('pointsProduct.id = :id', {id});
    qb.andWhere('pointsProduct.deletedAt IS NULL');
    const items = await qb.getMany();
    if (items.length > 0) {
      return items[0];
    }
    return null;
  }

  async pointsConfigStateChangeAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.pointsConfigStateChange(ctx);
    }
  }
  async pointsConfigStateChange(ctx: RequestContext) {
    const currentTime = new Date();
    const notYetStarted = await this.connection
      .getRepository(ctx, PointsProduct)
      .createQueryBuilder('pointsProduct')
      .andWhere('pointsProduct.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('pointsProduct.startTime <= :currentTime', {currentTime})
      .andWhere('pointsProduct.deletedAt IS NULL')
      .andWhere('pointsProduct.endTime > :currentTime', {currentTime})
      .leftJoin('pointsProduct.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .getMany();
    const notYetStartedIds = notYetStarted.map(item => item.id);
    const notYestStartProductIds = notYetStarted.map(item => item.productId);
    if (notYetStartedIds.length > 0) {
      await this.connection
        .getRepository(ctx, PointsProduct)
        .update({id: In(notYetStartedIds)}, {status: ActivityStatus.Normal});
      await this.cacheService.removeCache([
        ...notYestStartProductIds.map(id => CacheKeyManagerService.pointsProduct(id, ctx.channelId)),
      ]);
    }
    const haveEnded = await this.connection
      .getRepository(ctx, PointsProduct)
      .createQueryBuilder('pointsProduct')
      .andWhere('pointsProduct.status = :status', {status: ActivityStatus.Normal})
      .andWhere('pointsProduct.endTime <= :currentTime', {currentTime})
      .andWhere('pointsProduct.deletedAt IS NULL')
      .leftJoin('pointsProduct.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    const haveEndedProductIds = haveEnded.map(item => item.productId);
    if (haveEndedIds.length > 0) {
      await this.connection
        .getRepository(ctx, PointsProduct)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
      await this.cacheService.removeCache([
        ...haveEndedProductIds.map(id => CacheKeyManagerService.pointsProduct(id, ctx.channelId)),
      ]);
    }
  }
}
