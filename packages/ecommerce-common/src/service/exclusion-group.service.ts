import {Injectable} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {WeChatPaymentService} from '@scmally/wechat';
import {AbstractExclusionGroup} from '@scmally/wechat/dist/service/abstract-exclusion-group';
import {
  ChannelService,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  ProductVariant,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {ExclusionGroup, ExclusionProduct} from '../entities';
import {
  DeletionResult,
  ExclusionGroupInput,
  ExclusionProductInput,
  ProductExclusionGroupType,
} from '../generated-admin-types';
import {CacheService} from './cache.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
@Injectable()
export class ExclusionGroupService extends AbstractExclusionGroup {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private memoryStorageService: MemoryStorageService,
    private cacheService: CacheService,
    private customerProductVariantService: CustomerProductVariantService,
    public weChatPaymentService: WeChatPaymentService,
  ) {
    super(weChatPaymentService);
  }

  async canPurchaseByProductVariant(ctx: RequestContext, productVariant: ProductVariant) {
    const productId = productVariant.productId;
    const productVisitorId = productVariant.id;
    let exclusionGroups = await this.getEnabledExclusionGroups(ctx);
    if (!exclusionGroups) {
      return true;
    }
    exclusionGroups = exclusionGroups.filter(
      exclusionGroup => exclusionGroup.type === ProductExclusionGroupType.MustCombineWithNonGroup,
    );
    if (exclusionGroups.length <= 0) {
      return true;
    }
    for (const exclusionGroup of exclusionGroups) {
      const exclusionProducts = exclusionGroup.exclusionProducts;
      if (!exclusionProducts || exclusionProducts.length <= 0) {
        continue;
      }
      for (const exclusionProduct of exclusionProducts) {
        if (
          idsAreEqual(exclusionProduct.productId, productId) &&
          (!exclusionProduct.skuIds ||
            exclusionProduct.skuIds.length <= 0 ||
            exclusionProduct.skuIds.includes(productVisitorId))
        ) {
          return false;
        }
      }
    }
    return true;
  }

  async exclusionGroupsTypeName(ctx: RequestContext, productVariant: ProductVariant) {
    const productId = productVariant.productId;
    const productVisitorId = productVariant.id;
    // const exclusionGroups = await this.getEnabledExclusionGroups(ctx);
    const exclusionGroupByType = await this.findOneByType(ctx, ProductExclusionGroupType.MustCombineWithNonGroup);
    if (!exclusionGroupByType) {
      return '';
    }
    const exclusionGroups = [exclusionGroupByType];
    for (const exclusionGroup of exclusionGroups) {
      const exclusionProducts = exclusionGroup.exclusionProducts;
      if (!exclusionProducts || exclusionProducts.length <= 0) {
        continue;
      }
      for (const exclusionProduct of exclusionProducts) {
        if (
          idsAreEqual(exclusionProduct.productId, productId) &&
          (!exclusionProduct.skuIds ||
            exclusionProduct.skuIds.length <= 0 ||
            exclusionProduct.skuIds.includes(productVisitorId))
        ) {
          return exclusionGroup.name;
        }
      }
    }
    return '';
  }

  async exclusionGroupsType(ctx: RequestContext, productVariant: ProductVariant) {
    const productId = productVariant.productId;
    const productVisitorId = productVariant.id;
    const exclusionGroups = await this.getEnabledExclusionGroups(ctx);
    if (!exclusionGroups) {
      return;
    }
    for (const exclusionGroup of exclusionGroups) {
      const exclusionProducts = exclusionGroup.exclusionProducts;
      if (!exclusionProducts || exclusionProducts.length <= 0) {
        continue;
      }
      for (const exclusionProduct of exclusionProducts) {
        if (
          idsAreEqual(exclusionProduct.productId, productId) &&
          (!exclusionProduct.skuIds ||
            exclusionProduct.skuIds.length <= 0 ||
            exclusionProduct.skuIds.includes(productVisitorId))
        ) {
          return exclusionGroup.type;
        }
      }
    }
    return;
  }

  async removerSKUIdInOtherExclusionGroup(
    ctx: RequestContext,
    exclusionProductInputs: ExclusionProductInput[],
    exclusionGroupId?: ID,
  ) {
    if (!exclusionProductInputs || exclusionProductInputs.length <= 0) {
      return;
    }
    let exclusionProductGroups = await this.getEnabledExclusionGroups(ctx);
    if (!exclusionProductGroups) {
      return;
    }
    if (exclusionGroupId) {
      // 如果传入了排除组ID，需要排除掉这个排除组
      exclusionProductGroups = exclusionProductGroups.filter(
        exclusionProductGroup => !idsAreEqual(exclusionProductGroup.id, exclusionGroupId),
      );
    }
    for (const exclusionProductGroup of exclusionProductGroups) {
      const exclusionProducts = exclusionProductGroup.exclusionProducts;
      if (!exclusionProducts || exclusionProducts.length <= 0) {
        continue;
      }
      for (const exclusionProduct of exclusionProducts) {
        let isUpdate = false;
        for (const exclusionProductInput of exclusionProductInputs) {
          if (idsAreEqual(exclusionProduct.productId, exclusionProductInput.productId)) {
            if (!exclusionProduct.skuIds || exclusionProduct.skuIds.length <= 0) {
              const productId = exclusionProduct.productId;
              const productVariantsData = await this.customerProductVariantService.getVariantsByProductId(
                ctx,
                productId,
              );
              const skuIds = productVariantsData.items.map(variant => variant.id) as ID[];
              // idsAreEqual
              exclusionProduct.skuIds = skuIds.filter(
                skuId => !exclusionProductInput.skuIds?.some(skuIdInput => idsAreEqual(skuId, skuIdInput as ID)),
              );
              isUpdate = true;
            } else {
              exclusionProduct.skuIds = exclusionProduct.skuIds.filter(
                skuId => !exclusionProductInput.skuIds?.some(skuIdInput => idsAreEqual(skuId, skuIdInput as ID)),
              );
              isUpdate = true;
            }
          }
        }
        if (isUpdate) {
          if (!exclusionProduct.skuIds || exclusionProduct.skuIds.length <= 0) {
            await this.connection.getRepository(ctx, ExclusionProduct).delete({id: exclusionProduct.id});
          } else {
            await this.connection.getRepository(ctx, ExclusionProduct).save(exclusionProduct);
          }
          await this.cacheService.removeCache([
            CacheKeyManagerService.exclusionGroup(exclusionProductGroup.id, ctx.channelId),
            CacheKeyManagerService.exclusionProductExclusionGroup(exclusionProductGroup.id, ctx.channelId),
            CacheKeyManagerService.exclusionGroupUsing(ctx.channelId),
            CacheKeyManagerService.exclusionGroupAll(ctx.channelId),
            CacheKeyManagerService.exclusionGroupType(exclusionProductGroup.type, ctx.channelId),
          ]);
        }
      }
    }
  }

  // 检查SKU_ID是否存在其他排除组
  async checkSKUIdInOtherExclusionGroup(
    ctx: RequestContext,
    exclusionProductInputs: ExclusionProductInput[],
    exclusionGroupId?: ID,
  ) {
    if (!exclusionProductInputs || exclusionProductInputs.length <= 0) {
      return false;
    }
    let exclusionProductGroups = await this.getEnabledExclusionGroups(ctx);
    if (!exclusionProductGroups) {
      return false;
    }
    if (exclusionGroupId) {
      // 如果传入了排除组ID，需要排除掉这个排除组
      exclusionProductGroups = exclusionProductGroups.filter(
        exclusionProductGroup => !idsAreEqual(exclusionProductGroup.id, exclusionGroupId),
      );
    }
    for (const exclusionProductGroup of exclusionProductGroups) {
      const exclusionProducts = exclusionProductGroup.exclusionProducts;
      if (exclusionProducts.length <= 0) {
        continue;
      }
      for (const exclusionProduct of exclusionProducts) {
        for (const exclusionProductInput of exclusionProductInputs) {
          if (
            idsAreEqual(exclusionProduct.productId, exclusionProductInput.productId) &&
            (!exclusionProduct.skuIds ||
              exclusionProduct.skuIds.length <= 0 ||
              exclusionProduct.skuIds.some(skuId =>
                exclusionProductInput.skuIds?.some(skuIdInput => idsAreEqual(skuId, skuIdInput as ID)),
              ))
          ) {
            return true;
          }
        }
      }
    }
    return false;
  }

  //获取全部可用的排除组
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.exclusionGroupAll(ctx.channelId);
      }
      return '';
    },
  })
  async getEnabledExclusionGroups(ctx: RequestContext) {
    const memoryStorageCacheKey = CacheKeyManagerService.exclusionGroupAll(ctx.channelId);
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (memoryData) {
        return memoryData.exclusionGroups as ExclusionGroup[];
      }
    }

    const qb = this.connection.getRepository(ctx, ExclusionGroup).createQueryBuilder('exclusionGroup');
    qb.leftJoin('exclusionGroup.channels', 'channel');
    qb.leftJoinAndSelect('exclusionGroup.exclusionProducts', 'exclusionProduct');
    qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    qb.andWhere('exclusionGroup.deletedAt IS NULL');
    qb.andWhere('exclusionGroup.enabled = :enabled', {enabled: true});
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const exclusionGroups = await qb.getMany();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {exclusionGroups: exclusionGroups});
    }
    return exclusionGroups;
  }

  async upsertExclusionGroup(ctx: RequestContext, input: ExclusionGroupInput) {
    const id = input.id;
    let exclusionGroupId = id as ID;
    const isExistSku = await this.checkSKUIdInOtherExclusionGroup(
      ctx,
      input.exclusionProducts as ExclusionProductInput[],
      exclusionGroupId,
    );
    if (isExistSku) {
      // 如果SKU_ID存在其他排除组，则需要移除其他排除组中的SKU_ID
      await this.removerSKUIdInOtherExclusionGroup(
        ctx,
        input.exclusionProducts as ExclusionProductInput[],
        exclusionGroupId,
      );
    }
    if (id) {
      let exclusionGroup = await this.findOne(ctx, id);
      if (!exclusionGroup) {
        throw new Error('ExclusionGroup not found');
      }
      if (input.name) {
        exclusionGroup.name = input.name;
      }
      if (input.remarks) {
        exclusionGroup.remarks = input.remarks;
      }
      exclusionGroup = await this.connection.getRepository(ctx, ExclusionGroup).save(exclusionGroup);
      await this.connection.getRepository(ctx, ExclusionProduct).delete({exclusionGroupId: exclusionGroup.id});
    } else {
      const isExist = await this.findOneByType(ctx, input.type);
      if (isExist) {
        throw new Error('该类型的排除组已存在,请勿重复添加');
      }
      let exclusionGroup = new ExclusionGroup({
        name: input.name,
        remarks: input.remarks,
        type: input.type,
      });
      exclusionGroup = await this.channelService.assignToCurrentChannel(exclusionGroup, ctx);
      exclusionGroup = await this.connection.getRepository(ctx, ExclusionGroup).save(exclusionGroup);
      exclusionGroupId = exclusionGroup.id as ID;
    }
    if (exclusionGroupId && input.exclusionProducts && input.exclusionProducts.length > 0) {
      const exclusionProducts = input.exclusionProducts.map(exclusionProduct => {
        return new ExclusionProduct({
          productId: exclusionProduct?.productId,
          skuIds: exclusionProduct?.skuIds as ID[],
          exclusionGroupId: exclusionGroupId as ID,
        });
      });
      await this.connection.getRepository(ctx, ExclusionProduct).save(exclusionProducts);
    }
    await this.cacheService.removeCache([
      CacheKeyManagerService.exclusionGroup(exclusionGroupId, ctx.channelId),
      CacheKeyManagerService.exclusionProductExclusionGroup(exclusionGroupId, ctx.channelId),
      CacheKeyManagerService.exclusionGroupUsing(ctx.channelId),
      CacheKeyManagerService.exclusionGroupAll(ctx.channelId),
      CacheKeyManagerService.exclusionGroupType(input.type, ctx.channelId),
    ]);
    return this.findOne(ctx, exclusionGroupId);
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, exclusionGroupId: ID) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.exclusionProductExclusionGroup(exclusionGroupId, ctx.channelId);
      }
      return '';
    },
  })
  async exclusionProducts(ctx: RequestContext, exclusionGroupId: ID) {
    if (!exclusionGroupId) {
      throw new Error('ExclusionGroup not found');
    }
    const cacheKey = CacheKeyManagerService.exclusionProductExclusionGroup(exclusionGroupId, ctx.channelId);
    let isCache = false;
    let exclusionProducts: ExclusionProduct[] = [];
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(cacheKey);
      if (memoryData) {
        exclusionProducts = memoryData;
        isCache = true;
      }
    }
    if (!isCache) {
      exclusionProducts = await this.connection.getRepository(ctx, ExclusionProduct).find({
        where: {
          exclusionGroupId,
        },
        relations: ['product', 'exclusionGroup'],
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(cacheKey, exclusionProducts);
      }
    }
    return exclusionProducts;
  }
  async softDeleteExclusionGroup(ctx: RequestContext, exclusionGroupId: ID) {
    const exclusionGroup = await this.findOne(ctx, exclusionGroupId);
    if (!exclusionGroup) {
      throw new Error('ExclusionGroup not found');
    }
    exclusionGroup.deletedAt = new Date();
    await this.connection.getRepository(ctx, ExclusionGroup).save(exclusionGroup);
    await this.cacheService.removeCache([
      CacheKeyManagerService.exclusionGroup(exclusionGroupId, ctx.channelId),
      CacheKeyManagerService.exclusionProductExclusionGroup(exclusionGroupId, ctx.channelId),
      CacheKeyManagerService.exclusionGroupUsing(ctx.channelId),
      CacheKeyManagerService.exclusionGroupAll(ctx.channelId),
      CacheKeyManagerService.exclusionGroupType(exclusionGroup.type, ctx.channelId),
    ]);
    return {
      result: DeletionResult.Deleted,
    };
  }
  @cacheableAccess({
    cacheKeyFn: (
      ctx: RequestContext,
      id: ID,
      options?: ListQueryOptions<ExclusionGroup>,
      relations?: RelationPaths<ExclusionGroup>,
    ) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.exclusionGroup(id, ctx.channelId);
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<ExclusionGroup>,
    relations?: RelationPaths<ExclusionGroup>,
  ) {
    let cacheKey = ``;
    let exclusionGroup: ExclusionGroup | undefined;
    if (ctx.apiType === 'shop') {
      cacheKey = CacheKeyManagerService.exclusionGroup(id, ctx.channelId);
      exclusionGroup = this.memoryStorageService.get(cacheKey);
    }
    if (!exclusionGroup) {
      const qb = this.listQueryBuilder.build(ExclusionGroup, options, {
        ctx,
        relations: [],
      });
      qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
      qb.andWhere(`${qb.alias}.id = :id`, {id});
      if (ctx.apiType === 'shop') {
        qb.cache(cacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      exclusionGroup = (await qb.take(1).getOne()) as ExclusionGroup;
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(cacheKey, exclusionGroup);
      }
    }
    return exclusionGroup;
  }

  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<ExclusionGroup>,
    relations?: RelationPaths<ExclusionGroup>,
  ) {
    const qb = this.listQueryBuilder.build(ExclusionGroup, options, {
      ctx,
      relations: [],
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, total] = await qb.getManyAndCount();
    return {
      items,
      totalItems: total,
    };
  }

  async getProductVariants(ctx: RequestContext, skuIds: ID[]) {
    const productVariants: ProductVariant[] = [];
    if (!skuIds || skuIds.length <= 0) {
      return productVariants;
    }
    for (const skuId of skuIds) {
      const productVariant = await this.customerProductVariantService.getVariantByVariantId(ctx, skuId);
      if (productVariant) {
        productVariants.push(productVariant);
      }
    }
    return productVariants;
  }

  async checkProductCanCheckout(ctx: RequestContext, skuIds: ID[]) {
    // 返回值需要一个布尔值和一个字符串和对应的枚举code
    const result = {
      canCheckout: true,
      reason: '',
    };
    if (!skuIds || skuIds.length <= 0) {
      return result;
    }
    const productVariants = await this.getProductVariants(ctx, skuIds);
    const productIds = productVariants.map(productVariant => productVariant.productId);
    const mustCombineWithNonGroup = await this.findOneByType(ctx, ProductExclusionGroupType.MustCombineWithNonGroup);
    const canCombineWithGroup = await this.findOneByType(ctx, ProductExclusionGroupType.CanCombineWithGroup);
    const noCombineWithGroup = await this.findOneByType(ctx, ProductExclusionGroupType.NoCombineWithGroup);
    if (!mustCombineWithNonGroup && !canCombineWithGroup && !noCombineWithGroup) {
      return result;
    }
    // 获取三个组内全部的商品ID
    let exclusionGroupProductIds: ID[] = [];
    if (mustCombineWithNonGroup) {
      const exclusionProductIds = mustCombineWithNonGroup.exclusionProducts.map(
        exclusionProduct => exclusionProduct.productId,
      );
      exclusionGroupProductIds.push(...exclusionProductIds);
    }
    if (canCombineWithGroup) {
      const exclusionProductIds = canCombineWithGroup.exclusionProducts.map(
        exclusionProduct => exclusionProduct.productId,
      );
      exclusionGroupProductIds.push(...exclusionProductIds);
    }
    if (noCombineWithGroup) {
      const exclusionProductIds = noCombineWithGroup.exclusionProducts.map(
        exclusionProduct => exclusionProduct.productId,
      );
      exclusionGroupProductIds.push(...exclusionProductIds);
    }
    exclusionGroupProductIds = Array.from(new Set(exclusionGroupProductIds));
    // 只要有一个商品不在排除组内或者排查组内的SKU_IDS不为空并且不包含productVariantsID，就允许结算
    const hasProductNotInExclusionGroup = productIds.some(productId => {
      const variants = productVariants.filter(variant => idsAreEqual(variant.productId, productId));
      return (
        !exclusionGroupProductIds.some(id => idsAreEqual(id, productId)) ||
        ((!mustCombineWithNonGroup?.exclusionProducts.some(exclusionProduct =>
          idsAreEqual(exclusionProduct.productId, productId),
        ) ||
          mustCombineWithNonGroup?.exclusionProducts?.some(
            exclusionProducts =>
              idsAreEqual(exclusionProducts.productId, productId) &&
              exclusionProducts.skuIds.length > 0 &&
              variants.some(variant => !exclusionProducts.skuIds.includes(variant.id)),
          )) &&
          (!canCombineWithGroup?.exclusionProducts.some(exclusionProduct =>
            idsAreEqual(exclusionProduct.productId, productId),
          ) ||
            canCombineWithGroup?.exclusionProducts?.some(
              exclusionProducts =>
                idsAreEqual(exclusionProducts.productId, productId) &&
                exclusionProducts.skuIds.length > 0 &&
                variants.some(variant => !exclusionProducts.skuIds.includes(variant.id)),
            )) &&
          (!noCombineWithGroup?.exclusionProducts.some(exclusionProduct =>
            idsAreEqual(exclusionProduct.productId, productId),
          ) ||
            noCombineWithGroup?.exclusionProducts?.some(
              exclusionProducts =>
                idsAreEqual(exclusionProducts.productId, productId) &&
                exclusionProducts.skuIds.length > 0 &&
                variants.some(variant => !exclusionProducts.skuIds.includes(variant.id)),
            )))
      );
    });
    // 如果存在至少一个商品不在排除组内，允许结算
    if (hasProductNotInExclusionGroup) {
      return result; // 可以结算
    }

    // 检查SKU_IDS是否是必须与非限制组组合购买
    const exclusionNotGroup = this.checkMustCombineWithNonGroup(
      ctx,
      productVariants,
      productIds,
      mustCombineWithNonGroup,
      canCombineWithGroup,
      noCombineWithGroup,
    );
    if (exclusionNotGroup.canCheckout === false) {
      return exclusionNotGroup;
    }
    // 检查canCombineWithGroup和noCombineWithGroup同时存在的组合购买逻辑
    const exclusionMultipleGroup = this.checkMultipleGroup(
      ctx,
      productVariants,
      productIds,
      mustCombineWithNonGroup,
      canCombineWithGroup,
      noCombineWithGroup,
    );
    if (exclusionMultipleGroup.canCheckout === false) {
      return exclusionMultipleGroup;
    }

    // 检查noCombineWithGroup是否存在多个商品
    const exclusionNoCombineWithGroup = this.checkNoCombineWithGroup(
      ctx,
      productVariants,
      productIds,
      mustCombineWithNonGroup,
      canCombineWithGroup,
      noCombineWithGroup,
    );
    if (exclusionNoCombineWithGroup.canCheckout === false) {
      return exclusionNoCombineWithGroup;
    }
    return result;
  }

  checkMustCombineWithNonGroup(
    ctx: RequestContext,
    productVariants: ProductVariant[],
    productIds: ID[],
    mustCombineWithNonGroup?: ExclusionGroup,
    canCombineWithGroup?: ExclusionGroup,
    noCombineWithGroup?: ExclusionGroup,
  ) {
    const result = {
      canCheckout: true,
      reason: '',
    };
    if (!mustCombineWithNonGroup) {
      return result;
    } else {
      const productVariantIds = productVariants.map(productVariant => productVariant.id);
      // 只要存在一个商品在限制组内 并且限制组的SKU_IDS为空或者SKU_IDS包含productVariantsID，就不允许结算
      const hasProductInExclusionGroup = productIds.some(productId =>
        // mustCombineWithNonGroupProductIds.some(id => idsAreEqual(id, productId)),
        mustCombineWithNonGroup.exclusionProducts.some(
          exclusionProducts =>
            idsAreEqual(exclusionProducts.productId, productId) &&
            (exclusionProducts.skuIds.length === 0 ||
              exclusionProducts.skuIds.some(skuId => productVariantIds.includes(skuId))),
        ),
      );
      if (hasProductInExclusionGroup) {
        result.canCheckout = false;
        result.reason = `${
          mustCombineWithNonGroup.exclusionDescription ?? '升级奖品只能和正价商品随单,请加购其他商品再结算'
        }`;
        return result;
      }
    }
    return result;
  }

  checkMultipleGroup(
    ctx: RequestContext,
    productVariants: ProductVariant[],
    productIds: ID[],
    mustCombineWithNonGroup?: ExclusionGroup,
    canCombineWithGroup?: ExclusionGroup,
    noCombineWithGroup?: ExclusionGroup,
  ) {
    const result = {
      canCheckout: true,
      reason: '',
    };
    if (!canCombineWithGroup || !noCombineWithGroup) {
      return result;
    }
    const productVariantIds = productVariants.map(productVariant => productVariant.id);
    // 只要存在一个商品即在canCombineWithGroup又在noCombineWithGroup中，并且SKU_IDS为空或者SKU_IDS包含productVariantsID，就不允许结算
    const hasProductInMultipleGroup =
      productIds.some(productIdA =>
        canCombineWithGroup.exclusionProducts.some(
          exclusionProductA =>
            idsAreEqual(exclusionProductA.productId, productIdA) &&
            (exclusionProductA.skuIds.length === 0 ||
              exclusionProductA.skuIds.some(skuId => productVariantIds.includes(skuId))),
        ),
      ) &&
      productIds.some(productIdB =>
        noCombineWithGroup.exclusionProducts.some(
          exclusionProductB =>
            idsAreEqual(exclusionProductB.productId, productIdB) &&
            (exclusionProductB.skuIds.length === 0 ||
              exclusionProductB.skuIds.some(skuId => productVariantIds.includes(skuId))),
        ),
      );

    if (hasProductInMultipleGroup) {
      result.canCheckout = false;
      result.reason = `${canCombineWithGroup.exclusionDescription ?? '奖品只能与正价商品随单,请加购其他商品再结算'}`;
      return result;
    }
    return result;
  }

  checkNoCombineWithGroup(
    ctx: RequestContext,
    productVariants: ProductVariant[],
    productIds: ID[],
    mustCombineWithNonGroup: ExclusionGroup,
    canCombineWithGroup: ExclusionGroup,
    noCombineWithGroup: ExclusionGroup,
  ) {
    const result = {
      canCheckout: true,
      reason: '',
    };
    if (!noCombineWithGroup) {
      return result;
    }
    const productVariantIds = productVariants.map(productVariant => productVariant.id);
    // 如果存在多个商品在noCombineWithGroup中，并且SKU_IDS为空或者SKU_IDS包含productVariantsID，就不允许结算
    // 计算在 noCombineWithGroup 中的商品数量
    const noCombineProductsCount = productIds.filter(productId =>
      noCombineWithGroup.exclusionProducts.some(
        exclusionProduct =>
          idsAreEqual(exclusionProduct.productId, productId) &&
          (exclusionProduct.skuIds.length === 0 ||
            exclusionProduct.skuIds.some(skuId => productVariantIds.includes(skuId))),
      ),
    ).length;

    // 如果存在多个商品在 noCombineWithGroup 中，不允许结算
    if (noCombineProductsCount > 1) {
      result.canCheckout = false;
      // result.reason = '【单拍付邮】商品只能拍一个,请修改';
      result.reason = `${noCombineWithGroup.exclusionDescription ?? '奖品只能拍一个,请修改'}`;
    }
    return result;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, type: ProductExclusionGroupType) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.exclusionGroupType(type, ctx.channelId);
      }
      return '';
    },
  })
  async findOneByType(ctx: RequestContext, type: ProductExclusionGroupType): Promise<ExclusionGroup> {
    const cacheKey = CacheKeyManagerService.exclusionGroupType(type, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(cacheKey);
      if (memoryData) {
        return memoryData.exclusionGroup;
      }
    }
    const qb = this.connection
      .getRepository(ctx, ExclusionGroup)
      .createQueryBuilder('exclusionGroup')
      .leftJoinAndSelect('exclusionGroup.exclusionProducts', 'exclusionProduct')
      .leftJoinAndSelect('exclusionGroup.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('exclusionGroup.deletedAt IS NULL')
      .andWhere('exclusionGroup.enabled = :enabled', {enabled: true})
      .andWhere('exclusionGroup.type = :type', {type});
    const exclusionGroup = (await qb.take(1).getOne()) as ExclusionGroup;
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(cacheKey, {exclusionGroup: exclusionGroup});
    }
    return exclusionGroup;
  }

  async checkProductInSameExclusionGroup(ctx: RequestContext, productIds: ID[]) {
    if (!productIds || productIds.length <= 0) {
      return false;
    }
    const exclusionGroupId = await this.getUsingExclusionGroupId(ctx);
    if (!exclusionGroupId) {
      return false;
    }
    const exclusionProducts = await this.exclusionProducts(ctx, exclusionGroupId);
    if (!exclusionProducts) {
      return false;
    }
    const exclusionProductIds = exclusionProducts.map(exclusionProduct => exclusionProduct.productId);
    if (exclusionProductIds.length <= 0) {
      return false;
    }
    for (const productId of productIds) {
      if (!exclusionProductIds.find(id => idsAreEqual(id, productId))) {
        // 如果有一个 productId 找不到匹配项，直接返回 false
        return false;
      }
    }
    // 如果所有的 productId 都找到了对应的 exclusionProductIds，则返回 true
    return true;
  }

  // 获取正在使用的一个限制组ID
  async getUsingExclusionGroupId(ctx: RequestContext) {
    const cacheKey = CacheKeyManagerService.exclusionGroupUsing(ctx.channelId);
    let exclusionGroupId: ID | undefined;
    let isCache = false;
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(cacheKey);
      if (memoryData) {
        exclusionGroupId = memoryData;
        isCache = true;
      }
    }
    if (!isCache) {
      const qb = this.connection.getRepository(ctx, ExclusionGroup).createQueryBuilder('exclusionGroup');
      qb.andWhere('exclusionGroup.deletedAt IS NULL');
      qb.leftJoinAndSelect('exclusionGroup.channels', 'channel');
      qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
      qb.andWhere('exclusionGroup.enabled = :enabled', {enabled: true});
      const exclusionGroup = await qb.take(1).getOne();
      if (exclusionGroup) {
        exclusionGroupId = exclusionGroup.id;
        this.memoryStorageService.set(cacheKey, exclusionGroupId);
      }
    }
    return exclusionGroupId;
  }
}
