import {Injectable} from '@nestjs/common';
import {cacheableAccess, CacheService, MemoryStorageService, CacheKeyManagerService} from '@scmally/kvs';
import {RequestContext, TransactionalConnection} from '@vendure/core';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PersonalCenter} from '../entities';
import {PersonalCenterInput} from '../generated-admin-types';
@Injectable()
export class PersonalCenterService {
  constructor(
    private connection: TransactionalConnection,
    private memoryStorageService: MemoryStorageService,
    private cacheService: CacheService,
  ) {}

  async upsertPersonalCenter(ctx: RequestContext, input: PersonalCenterInput) {
    const channelId = ctx.channelId;
    let personalCenter = await this.findOne(ctx);
    personalCenter = new PersonalCenter({
      ...personalCenter,
      topComponentType: input.topComponentType,
      topComponent: input.topComponent,
      bottomComponentType: input.bottomComponentType,
      bottomComponent: input.bottomComponent,
      channelId: channelId,
    });
    personalCenter = await this.connection.getRepository(ctx, PersonalCenter).save(personalCenter);
    await this.cacheService.removeCache(CacheKeyManagerService.personalCenter(channelId));
    return personalCenter;
  }
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext) => {
      return CacheKeyManagerService.personalCenter(ctx.channelId);
    },
  })
  async findOne(ctx: RequestContext) {
    const channelId = ctx.channelId;
    const memoryStorageKey = CacheKeyManagerService.personalCenter(channelId);

    if (ctx.apiType === 'shop') {
      const memoryStorage = this.memoryStorageService.get(memoryStorageKey);
      if (memoryStorage) {
        return memoryStorage;
      }
    }
    const qb = this.connection.getRepository(ctx, PersonalCenter).createQueryBuilder('personalCenter');
    qb.where('personalCenter.channelId = :channelId', {channelId});
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageKey, DEFAULT_CACHE_TIMEOUT);
    }
    const personalCenter = await qb.take(1).getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageKey, personalCenter);
    }
    return personalCenter;
  }
}
