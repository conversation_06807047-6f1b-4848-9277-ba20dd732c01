import {Inject, Injectable, forwardRef} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  Asset,
  ConfigService,
  FacetValue,
  GlobalSettingsService,
  ID,
  InternalServerError,
  ListQueryBuilder,
  ListQueryOptions,
  PaginatedList,
  Product,
  ProductOption,
  ProductPriceApplicator,
  ProductVariant,
  ProductVariantService,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  StockLevel,
  TaxCategory,
  TransactionalConnection,
  Translated,
  TranslatorService,
} from '@vendure/core';
import {IsNull} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT, STOCK_CACHE_TIMEOUT} from '../consts';
import {CustomerProductService} from './custom-product.service';
@Injectable()
export class CustomerProductVariantService {
  constructor(
    private configService: ConfigService,
    private globalSettingsService: GlobalSettingsService,
    private connection: TransactionalConnection,
    private productVariantService: ProductVariantService,
    private translator: TranslatorService,
    private listQueryBuilder: ListQueryBuilder,
    private requestCache: RequestContextCacheService,
    private productPriceApplicator: ProductPriceApplicator,
    @Inject(forwardRef(() => CustomerProductService))
    private customerProductService: CustomerProductService,
    private memoryStorageService: MemoryStorageService,
  ) {}
  // 根据查询的字段添加关联
  addProductVariantRelations(relations: RelationPaths<ProductVariant>): RelationPaths<ProductVariant> {
    // if (relations.includes('facetValues')) {
    //   relations.push('facetValues.channels');
    // }
    // if (relations.includes('product')) {
    //   relations = relations.concat([
    //     'product',
    //     'product.assets',
    //     'product.assets.asset',
    //     'product.assets.asset.channels',
    //     'product.featuredAsset',
    //   ]);
    // }
    // relations = relations.concat([
    //   'assets',
    //   'assets.asset',
    //   'assets.asset.channels',
    //   'productVariantPrices',
    //   'taxCategory',
    //   'stockLevels',
    //   'stockMovements',
    // ]);
    relations = relations.concat([
      'options',
      'options.group',
      'featuredAsset',
      'stockLevels',
      'productVariantPrices',
      'taxCategory',
    ]);
    // 去重
    return Array.from(new Set(relations));
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, variant: ProductVariant) => {
      if (!variant.product) {
        return '';
      }
      return CacheKeyManagerService.productVariantProduct(variant?.productId, ctx.channelId);
    },
  })
  async getProductForVariant(ctx: RequestContext, variant: ProductVariant): Promise<Translated<Product>> {
    const memoryStorageCacheKey = CacheKeyManagerService.productVariantProduct(variant?.productId, ctx.channelId);
    let product;
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    if (!variant.product) {
      if (ctx.apiType === 'shop') {
        product = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      if (!product) {
        product = await this.connection.getEntityOrThrow(ctx, Product, variant.productId, {
          includeSoftDeleted: true,
          cache: cache,
        });
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, product);
        }
      }
    } else {
      product = variant.product;
    }
    return this.translator.translate(product, ctx);
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, variantId: ID) => {
      return CacheKeyManagerService.productVariantOptions(variantId, ctx.channelId);
    },
  })
  async getOptionsForVariant(ctx: RequestContext, variantId: ID): Promise<Array<Translated<ProductOption>>> {
    const memoryStorageCacheKey = CacheKeyManagerService.productVariantOptions(variantId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let variant: ProductVariant | undefined;
    if (ctx.apiType === 'shop') {
      variant = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!variant) {
      variant = await this.connection.findOneInChannel(ctx, ProductVariant, variantId, ctx.channelId, {
        relations: ['options'],
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, variant);
      }
    }

    return !variant ? [] : variant.options.map(o => this.translator.translate(o, ctx));
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, variantId: ID) => {
      return CacheKeyManagerService.productVariantFacetValues(variantId, ctx.channelId);
    },
  })
  async getFacetValuesForVariant(ctx: RequestContext, variantId: ID): Promise<Array<Translated<FacetValue>>> {
    const memoryStorageCacheKey = CacheKeyManagerService.productVariantFacetValues(variantId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: 0,
          }
        : false;
    let variant: ProductVariant | undefined;
    if (ctx.apiType === 'shop') {
      variant = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!variant) {
      variant = await this.connection.findOneInChannel(ctx, ProductVariant, variantId, ctx.channelId, {
        relations: ['facetValues', 'facetValues.facet', 'facetValues.channels'],
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, variant);
      }
    }
    return !variant ? [] : variant.facetValues.map(o => this.translator.translate(o, ctx, ['facet']));
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, variant: ProductVariant) => {
      if (variant.productVariantPrices?.length) {
        return '';
      }
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.productVariantPrices(variant.id, ctx.channelId);
      }
      return '';
    },
  })
  async hydratePriceFields<F extends 'currencyCode' | 'price' | 'priceWithTax' | 'taxRateApplied'>(
    ctx: RequestContext,
    variant: ProductVariant,
    priceField: F,
  ): Promise<ProductVariant[F]> {
    const cacheKey = `hydrate-variant-price-fields-${variant.id}:${ctx.channelId}`;
    let populatePricesPromise = this.requestCache.get<Promise<ProductVariant>>(ctx, cacheKey);

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    if (!populatePricesPromise) {
      // eslint-disable-next-line @typescript-eslint/no-misused-promises, no-async-promise-executor
      populatePricesPromise = new Promise(async (resolve, reject) => {
        try {
          if (!variant.productVariantPrices?.length) {
            const memoryStorageCacheKey = CacheKeyManagerService.productVariantPrices(variant.id, ctx.channelId);
            const cache =
              ctx.apiType === 'shop'
                ? {
                    id: memoryStorageCacheKey,
                    milliseconds: DEFAULT_CACHE_TIMEOUT,
                  }
                : false;
            let variantWithPrices;
            if (ctx.apiType === 'shop') {
              variantWithPrices = this.memoryStorageService.get(memoryStorageCacheKey);
            }
            if (!variantWithPrices) {
              variantWithPrices = await this.connection.getEntityOrThrow(ctx, ProductVariant, variant.id, {
                relations: ['productVariantPrices'],
                includeSoftDeleted: true,
                cache: cache,
              });
              if (ctx.apiType === 'shop') {
                this.memoryStorageService.set(memoryStorageCacheKey, variantWithPrices);
              }
            }
            variant.productVariantPrices = variantWithPrices.productVariantPrices;
          }
          if (!variant.taxCategory) {
            const taxCategoryMemoryStorageCacheKey = CacheKeyManagerService.productVariantTaxCategory(
              variant.id,
              ctx.channelId,
            );
            const cache =
              ctx.apiType === 'shop'
                ? {
                    id: taxCategoryMemoryStorageCacheKey,
                    milliseconds: DEFAULT_CACHE_TIMEOUT,
                  }
                : false;
            let variantWithTaxCategory;
            if (ctx.apiType === 'shop') {
              variantWithTaxCategory = this.memoryStorageService.get(taxCategoryMemoryStorageCacheKey);
            }
            if (!variantWithTaxCategory) {
              variantWithTaxCategory = await this.connection.getEntityOrThrow(ctx, ProductVariant, variant.id, {
                relations: ['taxCategory'],
                includeSoftDeleted: true,
                cache: cache,
              });
            }
            variant.taxCategory = variantWithTaxCategory.taxCategory;
          }
          resolve(await this.productPriceApplicator.applyChannelPriceAndTax(variant, ctx, undefined));
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (e: any) {
          reject(e);
        }
      });
      this.requestCache.set(ctx, cacheKey, populatePricesPromise);
    }
    const hydratedVariant = await populatePricesPromise;
    return hydratedVariant[priceField];
  }

  async getDisplayStockLevel(ctx: RequestContext, variant: ProductVariant): Promise<string> {
    const {stockDisplayStrategy} = this.configService.catalogOptions;
    const saleableStockLevel = await this.getSaleableStockLevel(ctx, variant);
    return stockDisplayStrategy.getStockLevel(ctx, variant, saleableStockLevel);
  }

  async getSaleableStockLevel(ctx: RequestContext, variant: ProductVariant): Promise<number> {
    const {stockOnHand, stockAllocated} = await this.getAvailableStock(ctx, variant);
    return stockOnHand - stockAllocated;
  }
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, variant: ProductVariant) => {
      if (variant.stockLevels?.length) {
        return '';
      }
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.productVariantStockLevel(variant.id, ctx.channelId);
      }
      return '';
    },
  })
  async getAvailableStock(
    ctx: RequestContext,
    variant: ProductVariant,
  ): Promise<{stockOnHand: number; stockAllocated: number}> {
    const {stockLocationStrategy} = this.configService.catalogOptions;
    let stockLevels: StockLevel[] = [];
    if (variant.stockLevels?.length) {
      stockLevels = variant.stockLevels;
    } else {
      const memoryStorageCacheKey = CacheKeyManagerService.productVariantStockLevel(variant.id, ctx.channelId);
      const cache =
        ctx.apiType === 'shop'
          ? {
              id: memoryStorageCacheKey,
              milliseconds: STOCK_CACHE_TIMEOUT,
            }
          : false;
      if (ctx.apiType === 'shop') {
        stockLevels = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      if (!stockLevels || stockLevels.length === 0) {
        stockLevels = await this.connection.getRepository(ctx, StockLevel).find({
          where: {
            productVariantId: variant.id,
          },
          cache: cache,
        });
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, stockLevels);
        }
      }
    }
    return stockLocationStrategy.getAvailableStock(ctx, variant.id, stockLevels);
  }

  async applyPricesAndTranslateVariants(
    ctx: RequestContext,
    variants: ProductVariant[],
  ): Promise<Array<Translated<ProductVariant>>> {
    return Promise.all(
      variants.map(async variant => {
        const variantWithPrices = await this.productVariantService.applyChannelPriceAndTax(variant, ctx);
        return this.translator.translate(variantWithPrices, ctx, ['options', 'facetValues', ['facetValues', 'facet']]);
      }),
    );
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID) => {
      return CacheKeyManagerService.productVariants(productId, ctx.channelId);
    },
  })
  async getVariantsByProductId(
    ctx: RequestContext,
    productId: ID,
    options: ListQueryOptions<ProductVariant> = {},
    relations?: RelationPaths<ProductVariant>,
  ): Promise<PaginatedList<Translated<ProductVariant>>> {
    let variants, totalItems;
    const memoryStorageCacheKey = CacheKeyManagerService.productVariants(productId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        [variants, totalItems] = cacheData;
      }
    }
    if (!variants || !totalItems) {
      const qb = this.listQueryBuilder
        .build(ProductVariant, options, {
          relations: [
            // ...(relations || ['options', 'facetValues', 'facetValues.facet', 'assets', 'featuredAsset']),
            // 必须要的关联 不然会报错
            // 'taxCategory',
          ],
          orderBy: {id: 'ASC'},
          where: {deletedAt: IsNull()},
          ctx,
        })
        .innerJoinAndSelect('productvariant.channels', 'channel', 'channel.id = :channelId', {
          channelId: ctx.channelId,
        })
        .innerJoinAndSelect('productvariant.product', 'product', 'product.id = :productId', {
          productId,
        });

      if (ctx.apiType === 'shop') {
        qb.andWhere('productvariant.enabled = :enabled', {enabled: true});
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      [variants, totalItems] = await qb.getManyAndCount();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, [variants, totalItems]);
      }
    }
    //TODO 为每个variant设置默认的税率
    const taxCategory = await this.getChannelTaxCategory(ctx);
    for (const variant of variants) {
      variant.taxCategory = taxCategory as TaxCategory;
    }
    const items = await this.applyPricesAndTranslateVariants(ctx, variants);
    return {
      items,
      totalItems,
    };
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productVariantId: ID) => {
      return CacheKeyManagerService.productVariantIncludeDelete(productVariantId, ctx.channelId);
    },
  })
  async getVariantByVariantIdIncludeDelete(ctx: RequestContext, productVariantId: ID): Promise<ProductVariant> {
    const memoryStorageCacheKey = CacheKeyManagerService.productVariantIncludeDelete(productVariantId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let variant: ProductVariant | undefined;
    if (ctx.apiType === 'shop') {
      variant = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!variant) {
      // variant = await this.connection.getEntityOrThrow(ctx, ProductVariant, productVariantId, {
      //   relations: ['product'],
      //   cache: cache,
      // });
      variant = (await this.connection.getRepository(ctx, ProductVariant).findOne({
        where: {id: productVariantId},
        cache: cache,
        relations: ['product'],
      })) as ProductVariant;
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, variant);
      }
    }
    return variant;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productVariantId: ID) => {
      return CacheKeyManagerService.productVariant(productVariantId, ctx.channelId);
    },
  })
  async getVariantByVariantId(ctx: RequestContext, productVariantId: ID): Promise<ProductVariant> {
    const memoryStorageCacheKey = CacheKeyManagerService.productVariant(productVariantId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let variant: ProductVariant | undefined;
    if (ctx.apiType === 'shop') {
      variant = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!variant) {
      variant = (await this.connection.getRepository(ctx, ProductVariant).findOne({
        where: {
          id: productVariantId,
          enabled: true,
          deletedAt: IsNull(),
        },
        relations: ['product'],
        cache: cache,
      })) as ProductVariant;
      // variant = await this.connection.getEntityOrThrow(ctx, ProductVariant, productVariantId, {
      //   relations: ['product'],
      //   where: {
      //     enabled: true,
      //     deletedAt: IsNull(),
      //   },
      //   cache: cache,
      // });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, variant);
      }
    }
    return variant;
  }
  // 根据variantId获取variant 需要关联税和价格相关信息
  async getVariantWithPriceAndTax(ctx: RequestContext, productVariantId: ID): Promise<ProductVariant> {
    let variant = await this.getVariantByVariantId(ctx, productVariantId);
    if (!variant) {
      return variant;
    }
    const taxCategory = await this.getChannelTaxCategory(ctx);
    const productVariantFeaturedAsset = await this.customerProductService.getFeaturedAsset(
      ctx,
      variant,
      'ProductVariant',
    );
    variant.taxCategory = taxCategory as TaxCategory;
    variant.featuredAsset = productVariantFeaturedAsset as Asset;
    if (!productVariantFeaturedAsset) {
      variant.product.featuredAsset = (await this.customerProductService.getFeaturedAsset(
        ctx,
        variant.product,
        'Product',
      )) as Asset;
    }
    variant = await this.applyChannelPriceAndTax(variant, ctx);
    return variant;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext) => {
      return CacheKeyManagerService.taxCategory(ctx.channelId);
    },
  })
  async getChannelTaxCategory(ctx: RequestContext): Promise<TaxCategory | null> {
    const memoryStorageCacheKey = CacheKeyManagerService.taxCategory(ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData;
      }
    }
    const taxCategory = await this.connection
      .getRepository(ctx, TaxCategory)
      .createQueryBuilder('taxCategory')
      .cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT)
      .take(1)
      .getOne();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, taxCategory);
    }
    return taxCategory;
  }

  async applyChannelPriceAndTax(variant: ProductVariant, ctx: RequestContext): Promise<ProductVariant> {
    const {productVariantPriceSelectionStrategy} = this.configService.catalogOptions;
    // 选择价格 没有sql查询
    const channelPrice = await productVariantPriceSelectionStrategy.selectPrice(ctx, variant.productVariantPrices);
    if (!channelPrice) {
      throw new InternalServerError('error.no-price-found-for-channel', {
        variantId: variant.id,
        channel: ctx.channel.code,
      });
    }
    variant.listPrice = channelPrice.price;
    variant.listPriceIncludesTax = false;
    variant.currencyCode = channelPrice.currencyCode;
    return variant;
  }
}
