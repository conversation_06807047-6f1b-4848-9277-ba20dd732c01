import {Injectable} from '@nestjs/common';
import {DEFAULT_CACHE_TIMEOUT, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {ID, Promotion, RequestContext, TransactionalConnection} from '@vendure/core';
@Injectable()
export class CustomPromotionService {
  constructor(private connection: TransactionalConnection, private memoryStorageService: MemoryStorageService) {}

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, promotionId: ID) => {
      return CacheKeyManagerService.promotion(promotionId, ctx.channelId);
    },
  })
  async getPromotionByPromotionId(ctx: RequestContext, promotionId: ID) {
    const promotionMemoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: promotionMemoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let promotion: Promotion | undefined | null;
    if (ctx.apiType === 'shop') {
      promotion = this.memoryStorageService.get(promotionMemoryStorageCacheKey);
    }
    if (!promotion) {
      promotion = await this.connection.getRepository(ctx, Promotion).findOne({
        where: {id: promotionId},
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(promotionMemoryStorageCacheKey, promotion);
      }
    }
    return promotion;
  }
}
