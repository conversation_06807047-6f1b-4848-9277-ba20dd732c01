import {Inject, Injectable, forwardRef} from '@nestjs/common';
import {GiftCard, InterfaceMemberCoupon, MemberService, MembershipOrder, MembershipPlan} from '@scmally/member';
import {AbstractCoupon, WeChatPaymentService} from '@scmally/wechat';
import {cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
// import {JobState} from '@vendure/admin-ui/core';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  ActiveOrderService,
  Channel,
  ChannelService,
  Customer,
  CustomerGroup,
  CustomerService,
  EntityNotFoundError,
  ID,
  // JobQueue,
  // JobQueueService,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderService,
  ProductVariant,
  ProductVariantPrice,
  Promotion,
  PromotionCondition,
  PromotionService,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  RequestContextService,
  Transaction,
  // SerializedRequestContext,
  TransactionalConnection,
  UnauthorizedError,
  UserInputError,
  idsAreEqual,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {getConfig} from '@vendure/core/dist/config/config-helpers';
import {DateTime} from 'luxon';
// import {of} from 'rxjs';
// import {catchError, map} from 'rxjs/operators';
import {MemoryStorageService} from '@scmally/kvs';
import {In, LessThanOrEqual} from 'typeorm';
import {MembershipPlanState} from '../../../member/dist/ui/generated-shop-types';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {Coupon, OrderPromotionResult} from '../entities';
import {UserCoupon} from '../entities/user-coupon.entity';
import {OperationError} from '../error.type';
import {
  ApplicableType,
  ComplimentaryCouponObjectInput,
  CouponState,
  CouponType,
  OrderCustomFields,
  PreferentialType,
  ProgramLinkInput,
  PromotionType,
  SourceType,
  UserCouponState,
  ValidityPeriodType,
} from '../generated-admin-types';
import {ComplimentaryType, CouponInput, DeletionResult, OrderPurchaseType, RightsCoupon} from '../generated-shop-types';
import {convertibilityAction} from '../promotion/action/convertibility.action';
import {orderDiscountMax} from '../promotion/action/order.discount.max';
import {orderFixedAmount} from '../promotion/action/order.fixed.amount.action';
import {productTotalPriceConditions} from '../promotion/conditions/product-total-price.conditions';
import {productConvertibility} from '../promotion/conditions/product.convertibility';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
import {CustomerProductService} from './custom-product.service';
import {OrderPromotionResultService} from './order-promotion-result.service';
import {ProductCustomService} from './product-custom.service';
import {PromotionActivityService} from './promotion-activity.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
import {SendMessageService} from './send-message.service';
@Injectable()
export class CouponService extends AbstractCoupon implements InterfaceMemberCoupon {
  async totalOrderAmount(ctx: RequestContext, id: ID) {
    const userCoupon = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id = :couponId', {couponId: id})
      .andWhere('userCoupon.state = :state', {state: UserCouponState.HaveUsed})
      .leftJoin('userCoupon.order', 'order')
      .select('SUM(order.subTotal)', 'totalAmount')
      .getRawOne<{totalAmount: number}>();
    return Number(userCoupon?.totalAmount ?? 0);
  }
  async haveBeenUsedNumber(ctx: RequestContext, id: ID) {
    const userCouponCount = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id = :couponId', {couponId: id})
      .andWhere('userCoupon.state = :state', {state: UserCouponState.HaveUsed})
      .getCount();
    return Number(userCouponCount ?? 0);
  }
  async receivedNumber(ctx: RequestContext, id: ID) {
    const userCouponCount = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id = :couponId', {couponId: id})
      .andWhere('userCoupon.isReturned = false')
      .getCount();
    return Number(userCouponCount ?? 0);
  }

  // 获取多个优惠券领取数量和总数 按照优惠券id分组
  async receivedNumberGroup(ctx: RequestContext, ids: ID[]) {
    const userCouponCount = await this.connection
      .getRepository(ctx, Coupon)
      .createQueryBuilder('coupon')
      .select('COUNT(userCoupon.id)', 'count')
      .addSelect('coupon.id', 'couponId')
      .addSelect('coupon.totalQuantity', 'totalQuantity')
      .leftJoin('coupon.userCoupons', 'userCoupon')
      .andWhere('coupon.id IN (:...ids)', {ids})
      .groupBy('coupon.id')
      .getRawMany<{count: number; couponId: string; totalQuantity: number}>();
    userCouponCount.forEach(item => {
      item.count = Number(item.count);
      item.totalQuantity = Number(item.totalQuantity);
    });
    return userCouponCount;
  }
  constructor(
    public connection: TransactionalConnection,
    public listQueryBuilder: ListQueryBuilder,
    public channelService: ChannelService,
    public promotionService: PromotionService,
    public customerService: CustomerService,
    public activeOrderService: ActiveOrderService,
    public orderService: OrderService,
    public requestContextService: RequestContextService,
    @Inject(forwardRef(() => CommonService))
    public commonService: CommonService,
    public weChatPaymentService: WeChatPaymentService,
    public memberService: MemberService,
    public sendMessageService: SendMessageService,
    public requestContextCacheService: RequestContextCacheService,
    public promotionActivityService: PromotionActivityService,
    @Inject(forwardRef(() => OrderPromotionResultService))
    public orderPromotionResultService: OrderPromotionResultService,
    public promotionResultDetailService: PromotionResultDetailService, // private jobQueueService: JobQueueService,
    public cacheService: CacheService,
    public customerProductService: CustomerProductService,
    public memoryStorageService: MemoryStorageService,
    public productCustomService: ProductCustomService,
    public customerProductVariantService: CustomerProductVariantService,
  ) {
    super(weChatPaymentService);
    this.memberService.registerCoupon(this);
  }

  async removeNewTag(ctx: RequestContext, userCouponId: ID) {
    const userCoupon = await this.getUserCouponOne(ctx, userCouponId);
    if (!userCoupon) {
      throw new Error('user coupon not exist');
    }
    userCoupon.isNew = false;
    await this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
    return true;
  }

  async getAvailableCouponList(
    ctx: RequestContext,
    customerId: ID,
    couponName: string,
    options: ListQueryOptions<Coupon>,
    relations: RelationPaths<Coupon>,
  ) {
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer) {
      throw new Error('customer not exist');
    }
    const sql = this.connection
      .getRepository(ctx, Coupon)
      .createQueryBuilder('coupon')
      .leftJoin('coupon.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('coupon.state in (:...states)', {states: [CouponState.Normal, CouponState.NotStarted]})
      .andWhere('coupon.enable = :enable', {enable: true})
      .andWhere('coupon.totalQuantity > 0');
    if (couponName) {
      sql.andWhere('coupon.name like :couponName', {couponName: `%${couponName}%`});
    }
    const coupons = await sql.getMany();
    const couponIds = [];
    for (const coupon of coupons) {
      try {
        await this.verifyCoupon(ctx, coupon, customerId);
        couponIds.push(coupon.id as string);
      } catch (error) {
        continue;
      }
    }
    if (couponIds.length === 0) {
      return {
        items: [],
        totalItems: 0,
      };
    }
    options.filter = {
      ...options.filter,
      id: {
        in: couponIds,
      },
    };
    return this.findAll(ctx, options, relations);
  }

  async grantCoupon(ctx: RequestContext, quantity: number, couponId: ID, customerId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    //验证优惠券是否可领
    await this.verifyCoupon(ctx, coupon, customerId, quantity);
    for (let i = 0; i < quantity; i++) {
      await this.createUserCoupon(ctx, coupon, customerId);
    }
    return coupon;
  }

  async grantCouponWithUserCoupon(ctx: RequestContext, couponId: ID, customerId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    //验证优惠券是否可领
    await this.verifyCoupon(ctx, coupon, customerId, 1);
    return this.createUserCoupon(ctx, coupon, customerId);
  }
  //验证优惠券是否可领
  async verifyCoupon(ctx: RequestContext, coupon: Coupon, customerId: ID, quantity = 1) {
    if (quantity <= 0) {
      throw new Error('发放的优惠券必须大于零');
    }
    if ((coupon.state !== CouponState.Normal && coupon.state !== CouponState.NotStarted) || coupon.enable === false) {
      throw new Error('优惠券不可用');
    }
    const receivedNumber = await this.receivedNumber(ctx, coupon.id);
    const residualNumber = coupon.totalQuantity - receivedNumber;
    if (residualNumber <= 0 || residualNumber < quantity) {
      throw new Error('优惠券数量不足');
    }
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer) {
      throw new Error('用户未找到');
    }
    if (coupon.claimRestriction !== -1) {
      const count = await this.getUserCouponCount(ctx, customerId, coupon.id);
      if (count + quantity > coupon.claimRestriction) {
        throw new Error('您发放的优惠券的数量超过该用户最多可领取的数量，发放失败。');
      }
    }
  }
  async getCouponHold(
    ctx: RequestContext,
    couponId: ID,
    options: ListQueryOptions<Coupon>,
    relations: RelationPaths<Coupon>,
  ) {
    const coupon = await this.findOne(ctx, couponId, options, relations);
    if (!coupon) {
      throw new EntityNotFoundError('Coupon', couponId);
    }
    const userId = ctx.activeUserId;
    if (!userId) {
      return {
        coupon,
      };
    }

    const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      return {
        coupon,
      };
    }
    const userCouponList = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .leftJoinAndSelect('userCoupon.customer', 'customer')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('customer.id =:customerId', {customerId: customer.id})
      .andWhere('coupon.id =:couponId', {couponId: couponId})
      .getMany();
    //未使用的该优惠券
    const usableCoupons = userCouponList.filter(userCoupon => userCoupon.state === UserCouponState.Unused);
    //已过期的该优惠券
    const expiredCoupons = userCouponList.filter(userCoupon => userCoupon.state === UserCouponState.Expire);
    //已使用的该优惠券
    const usedCoupons = userCouponList.filter(
      userCoupon => userCoupon.state === UserCouponState.HaveUsed || userCoupon.state === UserCouponState.Lock,
    );
    //未开始的该优惠券
    const notStartedCoupons = userCouponList.filter(userCoupon => userCoupon.state === UserCouponState.NotStarted);
    //剩余可领取的数量
    const residualNumber = await this.couponAvailableCount(ctx, couponId);
    return {
      coupon,
      residualNumber,
      usableCoupons,
      expiredCoupons,
      usedCoupons,
      notStartedCoupons,
    };
  }
  // private jobQueue: JobQueue<{
  //   ctx: SerializedRequestContext;
  //   input: ComplimentaryCouponObjectInput;
  // }>;

  // async onModuleInit() {
  //   this.jobQueue = await this.jobQueueService.createQueue({
  //     name: 'coupon-grant',
  //     process: async job => {
  //       const ctx = RequestContext.deserialize(job.data.ctx);
  //       const result = await this.complimentaryCouponToMember(ctx, job.data.input);
  //       return result;
  //     },
  //   });
  // }

  // async couponGrant(ctx: RequestContext, input: ComplimentaryCouponObjectInput) {
  //   const job = await this.jobQueue.add({input, ctx: ctx.serialize()}, {retries: 2});
  //   return job.updates().pipe(
  //     map(update => {
  //       // The returned Observable will emit a value for every update to the job
  //       // such as when the `progress` or `status` value changes.
  //       Logger.info(`Job ${update.id}: progress: ${update.progress}`);
  //       if (update.state === JobState.COMPLETED) {
  //         Logger.info(`COMPLETED ${update.id}: ${update.result}`);
  //       }
  //       return update.result;
  //     }),
  //     catchError(err => of(err.message)),
  //   );
  // }

  /**
   * 赠送优惠券给指定会员组-废弃
   * @param ctx
   * @param couponId 优惠券id
   * @param membershipPlanId 会员组id
   */
  async complimentaryCouponToMember(ctx: RequestContext, input: ComplimentaryCouponObjectInput) {
    const {couponId, quantity, complimentaryType, membershipPlanIds} = input;
    let customers: Customer[] = [];
    if (complimentaryType === ComplimentaryType.All) {
      const {items} = await this.customerService.findAll(ctx, {filter: {deletedAt: null}});
      customers = items;
    } else {
      if (membershipPlanIds) {
        for (const membershipPlanId of membershipPlanIds) {
          const membershipPlan = await this.connection
            .getRepository(ctx, MembershipPlan)
            .findOne({where: {id: membershipPlanId}, relations: ['customerGroup', 'customerGroup.customers']});
          if (!membershipPlan) {
            Logger.debug('membership plan not exist');
            continue;
          }
          customers = customers.concat(membershipPlan.customerGroup.customers);
        }
      }
    }
    if (customers.length === 0) {
      throw new Error('customer group not exist');
    }
    const customerIds = [];
    for (const customer of customers) {
      customerIds.push(customer.id);
    }
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    const receivedNumber = await this.receivedNumber(ctx, couponId);
    const residualNumber = coupon.totalQuantity - receivedNumber;
    const customerLength = customerIds.length;
    const quantityNumber = quantity || 1;
    if (customerLength * quantityNumber > residualNumber) {
      throw new Error('剩余优惠券已不够发放!');
    }
    await this.giveCouponToCustomer(ctx, customerIds, couponId, quantityNumber);
    return this.findOne(ctx, couponId);
  }

  /**
   * 会员退卡退回优惠券
   */
  async returnMembershipCardCoupon(ctx: RequestContext, memberOrderIds: ID[]) {
    if (memberOrderIds.length === 0) {
      return [];
    }
    const userCoupons = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .andWhere('userCoupon.sourceType = :sourceType', {sourceType: SourceType.MemberOrder})
      .andWhere('userCoupon.sourceId IN (:...memberOrderIds)', {memberOrderIds: memberOrderIds})
      .andWhere('userCoupon.state in (:...state)', {state: [UserCouponState.NotStarted, UserCouponState.Unused]})
      .getMany();
    const userCouponIds = userCoupons.map(userCoupon => userCoupon.id) ?? [];
    if (userCouponIds.length === 0) {
      return [];
    }
    await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .update()
      .set({
        state: UserCouponState.Expire,
        isReturned: true,
      })
      .andWhere('id IN (:...userCouponIds)', {userCouponIds})
      .execute();
    return userCouponIds;
  }

  /**
   * 购买会员免费获取优惠券
   * @param ctx
   * @param customerId 用户id
   * @param membershipPlanId 会员计划id
   * @returns
   */
  async buyMembershipFreeCoupon(
    ctx: RequestContext,
    customerId: ID,
    membershipPlanId: ID,
    membershipOrderId: ID,
  ): Promise<boolean> {
    // throw new Error('Method not implemented.');
    const membershipPlan = await this.connection
      .getRepository(ctx, MembershipPlan)
      .findOne({where: {id: membershipPlanId}});
    if (!membershipPlan) {
      throw new Error('membership plan not exist');
    }
    if (membershipPlan.rightsCoupon.enable) {
      const presentedCoupon = membershipPlan.rightsCoupon.presentedCoupon || [];
      for (const coupon of presentedCoupon) {
        if (coupon) {
          const couponId = coupon.couponId;
          const presentedCount = coupon.presentedCount;
          await this.giveCouponToCustomer(
            ctx,
            [customerId],
            couponId,
            presentedCount,
            true,
            SourceType.MemberOrder,
            membershipOrderId,
          );
        }
      }
    }
    return true;
  }

  /**
   * 礼品卡赠送优惠券
   */
  async giftCardFreeCoupon(ctx: RequestContext, customerId: ID, giftCardId: ID, giftCardOrderId: ID): Promise<boolean> {
    const giftCard = await this.connection.getRepository(ctx, GiftCard).findOne({where: {id: giftCardId}});
    if (!giftCard) {
      throw new Error('gift card not exist');
    }
    if (giftCard.rightsCoupon.enable) {
      const presentedCoupon = giftCard.rightsCoupon.presentedCoupon || [];
      for (const coupon of presentedCoupon) {
        if (coupon) {
          const couponId = coupon.couponId;
          const presentedCount = coupon.presentedCount;
          await this.giveCouponToCustomer(
            ctx,
            [customerId],
            couponId,
            presentedCount,
            true,
            SourceType.GiftCardOrder,
            giftCardOrderId,
          );
        }
      }
    }
    return true;
  }

  /**
   * 赠送优惠券
   * @param ctx
   * @param customerIds 用户id
   * @param couponId 优惠券id
   * @param presentedCount 赠送数量 默认1
   */
  async giveCouponToCustomer(
    ctx: RequestContext,
    customerIds: ID[],
    couponId: ID,
    presentedCount = 1,
    outStockSkip = false,
    sourceType = SourceType.OwnMall,
    sourceId: ID = '',
  ) {
    const userCouponMap = new Map<ID, UserCoupon[]>();
    for (const customerId of customerIds) {
      // 生成用户优惠券
      for (let i = 0; i < presentedCount; i++) {
        await this.claimCoupon(ctx, couponId, customerId, outStockSkip, sourceType, sourceId);
      }
    }
    return userCouponMap;
  }

  async deleteShoppingCartPromotion(ctx: RequestContext, orderId: ID): Promise<boolean> {
    await this.connection
      .getRepository(ctx, OrderPromotionResult)
      .createQueryBuilder('orderPromotionResult')
      .update()
      .set({promResult: null})
      .where('orderId = :orderId', {orderId: orderId})
      .execute();
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return true;
  }

  /**
   * 验证优惠券是否可用
   * @param ctx
   * @param customerId
   * @param orderId
   * @returns
   */
  async isCouponAvailable(ctx: RequestContext, customerId: ID, orderId: ID): Promise<boolean> {
    const order = await this.orderService.findOne(ctx, orderId, ['customer']);
    if (!order) {
      throw new Error('order not exist');
    }
    const userCouponId = await this.getPromotionCouponId(ctx, orderId);
    if (userCouponId) {
      const userCoupon = await this.getUserCouponOne(ctx, userCouponId);
      if (!userCoupon) {
        return false;
      }
      if (userCoupon.maturityType !== ValidityPeriodType.LongTime) {
        if (new Date(userCoupon.maturityAt) < new Date() || userCoupon.state === UserCouponState.HaveUsed) {
          return false;
        }
      }
      userCoupon.state = UserCouponState.Lock;
      let orderIds = userCoupon.orderIds;
      if (orderIds && orderIds.length > 0) {
        orderIds = orderIds.filter(id => id !== order.id);
      }
      userCoupon.orderIds = [order.id];
      if (orderIds && orderIds.length > 0) {
        await this.removeOtherOrdersUsingThisCoupon(ctx, orderIds, userCoupon.id);
      }
      await this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
    }
    return true;
    // const promotions = order.promotions;
    // for (const promotion of promotions) {
    //   const promotionType = (promotion.customFields as PromotionCustomFields).type;
    //   if (promotionType === PromotionType.Coupon) {
    //     const userCoupon = await this.getUsedCouponByOrderIdAndPromotionId(ctx, order.id, promotion.id);
    //     if (!userCoupon) {
    //       return false;
    //     }
    //     if (userCoupon.maturityType === ValidityPeriodType.LongTime) {
    //       continue;
    //     } else {
    //       if (userCoupon.maturityAt < new Date() || userCoupon.state === UserCouponState.HaveUsed) {
    //         return false;
    //       }
    //     }

    //     userCoupon.state = UserCouponState.Lock;
    //     let orderIds = userCoupon.orderIds;
    //     orderIds = orderIds.filter(id => id !== order.id);
    //     userCoupon.orderIds = [order.id];
    //     await this.removeOtherOrdersUsingThisCoupon(ctx, orderIds, userCoupon.id);
    //     await this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
    //   }
    //   const availableState = await this.verifyTheOfferIsAvailable(ctx, promotion, order);
    //   if (!availableState) {
    //     return false;
    //   }
    // }
    // return true;
  }

  /**
   * 移除加入了该优惠券的订单
   * @param ctx
   * @param orderIds 订单id
   * @param userCouponId 用户优惠券id
   */
  async removeOtherOrdersUsingThisCoupon(ctx: RequestContext, orderIds: ID[], userCouponId: ID) {
    for (const orderId of orderIds) {
      await this.removeOrderCoupon(ctx, userCouponId, orderId);
    }
  }

  /**
   * 优惠券使用
   * @param ctx
   * @param order 订单
   * @returns
   */
  @Transaction()
  async couponUse(ctx: RequestContext, order: Order) {
    // const orderContainingPromotional = await this.orderService.findOne(ctx, order.id, ['promotions']);
    // if (!orderContainingPromotional) {
    //   return;
    // }
    // const promotions = orderContainingPromotional.promotions;
    // const userCouponIds = await this.getCouponIdsByOrderIdAndPromotions(ctx, order.id, promotions);
    // if (userCouponIds.length > 0) {
    //   await this.connection
    //     .getRepository(ctx, UserCoupon)
    //     .update(userCouponIds, {state: UserCouponState.HaveUsed, useAt: new Date()});
    // }
    const userCouponId = await this.getPromotionCouponId(ctx, order.id);
    if (userCouponId) {
      await this.connection
        .getRepository(ctx, UserCoupon)
        .update(userCouponId, {state: UserCouponState.HaveUsed, useAt: new Date(), order: {id: order.id}});
    }
  }

  async getPromotionCouponId(ctx: RequestContext, orderId: ID): Promise<ID> {
    const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    if (!promotionResult?.promResult) {
      return 0;
    }
    const coupons = promotionResult.promResult.coupons;
    if (!coupons || coupons.length <= 0) {
      return 0;
    }
    let userCouponId: ID = 0;
    for (const coupon of coupons) {
      if (coupon?.selected) {
        userCouponId = coupon.couponId as ID;
        break;
      }
    }
    return userCouponId;
  }
  /**
   * 根据订单id和优惠信息获取优惠券id（废弃）
   * @param ctx
   * @param orderId 订单id
   * @param promotions 优惠信息
   * @returns
   */
  async getCouponIdsByOrderIdAndPromotions(ctx: RequestContext, orderId: ID, promotions: Promotion[]) {
    const userCouPonIds: string[] = [];
    for (const promotion of promotions) {
      const userCoupon = await this.getUsedCouponByOrderIdAndPromotionId(ctx, orderId, promotion.id);
      if (userCoupon) {
        userCouPonIds.push(String(userCoupon.id));
      }
    }
    return userCouPonIds;
  }

  /**
   * 获取订单优惠信息
   * @param ctx
   * @returns
   */
  // async getOrderDiscounts(ctx: RequestContext, orderId?: ID) {
  //   if (!orderId) {
  //     const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
  //     if (!order) {
  //       throw new Error('order not exist');
  //     }
  //     orderId = order.id;
  //   }
  //   const order = await this.orderService.findOne(ctx, orderId);
  //   if (!order) {
  //     throw new Error('order not exist');
  //   }
  //   const discounts = order.discounts;
  //   const couponDiscounts = [];
  //   const memberDiscounts = [];
  //   const purchasePremiumDiscounts = [];
  //   const discountActivities = [];
  //   const fullDiscountPresent = [];
  //   for (const discount of discounts) {
  //     const id = discount.adjustmentSource.split(':')[1];
  //     const promotion = await this.promotionService.findOne(ctx, id);
  //     if (promotion) {
  //       const activities = await this.promotionActivityService.productActivitiesFindOne(ctx, promotion.id);
  //       if ((promotion.customFields as PromotionCustomFields).type === PromotionType.Coupon) {
  //         const userCoupon = await this.getActiveOrderCoupon(ctx, promotion.id, orderId);
  //         const couponDiscount = {
  //           discount: discount,
  //           activities: userCoupon,
  //         };
  //         couponDiscounts.push(couponDiscount);
  //       } else if ((promotion.customFields as PromotionCustomFields).type === PromotionType.Member) {
  //         memberDiscounts.push({discount});
  //       } else if ((promotion.customFields as PromotionCustomFields).type === PromotionType.PurchaseAtAPremium) {
  //         purchasePremiumDiscounts.push({discount, activities: activities as PurchasePremium});
  //       } else if ((promotion.customFields as PromotionCustomFields).type === PromotionType.DiscountActivity) {
  //         discountActivities.push({discount, activities: activities as DiscountActivity});
  //       } else if ((promotion.customFields as PromotionCustomFields).type === PromotionType.FullDiscountPresent) {
  //         fullDiscountPresent.push({discount, activities: activities as FullDiscountPresent});
  //       }
  //     }
  //   }
  //   return {couponDiscounts, memberDiscounts, purchasePremiumDiscounts, discountActivities, fullDiscountPresent};
  // }

  /**
   * 获取活跃订单优惠信息（废弃）
   * @param ctx
   * @param promotionId
   * @returns
   */
  async getActiveOrderCoupon(ctx: RequestContext, promotionId: ID, orderId?: ID) {
    if (!orderId) {
      const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
      if (!order) {
        throw new Error('order not exist');
      }
      orderId = order.id;
    }
    return this.getUsedCouponByOrderIdAndPromotionId(ctx, orderId, promotionId);
  }

  /**
   * 根据订单id和优惠id获取已经使用的优惠券信息（废弃）
   * @param ctx
   * @param orderId
   * @param promotionId
   * @returns
   */
  async getUsedCouponByOrderIdAndPromotionId(ctx: RequestContext, orderId: ID, promotionId: ID) {
    const coupons = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .leftJoinAndSelect('coupon.promotion', 'promotion')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      // .andWhere('userCoupon.state =:userCouponState', {userCouponState: UserCouponState.Lock})
      .andWhere('promotion.id =:promotionId', {promotionId})
      .andWhere(`FIND_IN_SET (:orderId, userCoupon.orderIds)`, {orderId: orderId})
      .getMany();
    if (coupons.length > 0) {
      return coupons[0];
    }
    return;
  }

  /**
   * 优惠券统计
   * @param ctx
   * @param couponId
   */
  async couponDataStatistic(ctx: RequestContext, couponId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    const statisticsData = await this.promotionResultDetailService.statisticsPromotion(
      ctx,
      coupon.promotion.id,
      PromotionType.Coupon,
    );
    const customerCount = await this.promotionResultDetailService.statisticsOfNewAndOldCustomers(
      ctx,
      coupon.promotion.id,
      PromotionType.Coupon,
    );
    // 用券单比价
    const couponAverage = statisticsData.totalOrders
      ? Math.floor(statisticsData.totalPayment / statisticsData.totalOrders)
      : 0;
    return {
      couponAverage,
      ...statisticsData,
      ...customerCount,
    };
  }

  /**
   * 获取优惠券二维码
   * @param ctx
   * @param input
   * @returns
   */
  async getCouponSmallProgramQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const couponId = input.id;
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new EntityNotFoundError('Coupon', couponId);
    }
    if (coupon.smallProgramQRCodeLink) {
      return coupon.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    coupon.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, Coupon).save(coupon);
    await this.cacheService.removeCache([
      CacheKeyManagerService.promotion(coupon.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(coupon.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }

  /**
   * 优惠券持有者
   * @param ctx
   * @param couponId  优惠券id
   * @param options 查询的参数
   * @param relations 关联的参数
   * @returns
   */
  async couponHolder(
    ctx: RequestContext,
    couponId: ID,
    options: ListQueryOptions<UserCoupon>,
    relations: RelationPaths<UserCoupon>,
  ) {
    const qb = this.listQueryBuilder.build(UserCoupon, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.coupon`, 'coupon').andWhere('coupon.id =:couponId', {
      couponId: couponId,
    });
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  /**
   * 删除使用的优惠券
   * @param ctx
   * @param userCouponId   用户优惠券id
   * @returns
   */
  async removeOrderCoupon(ctx: RequestContext, userCouponId: ID, orderId?: ID) {
    let userCouPon = await this.getUserCouponOne(ctx, userCouponId, undefined, ['coupon', 'coupon.promotion']);
    if (!userCouPon) {
      throw new Error('Membership coupon information could not be found');
    }
    userCouPon.state = UserCouponState.Unused;
    if (!orderId) {
      const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
      if (!order) {
        throw new Error('Order does not exist');
      }
      orderId = order.id;
    }
    userCouPon.orderIds = userCouPon.orderIds.filter(id => id !== orderId);
    userCouPon = await this.connection.getRepository(ctx, UserCoupon).save(userCouPon);
    const promotionCode = userCouPon.coupon.promotion.couponCode;
    await this.orderService.removeCouponCode(ctx, orderId, promotionCode);
    return this.orderService.findOne(ctx, orderId);
  }

  //-------修改到order-promotion-result.service.ts中
  // /**
  //  * 应用优惠券
  //  * @param ctx
  //  * @param userCouponId  用户优惠券id
  //  * @returns
  //  */
  // async applyCoupon(ctx: RequestContext, userCouponId: ID, orderId?: ID) {
  //   const userCouPon = await this.getUserCouponOne(ctx, userCouponId, undefined, ['coupon', 'coupon.promotion']);
  //   if (!userCouPon) {
  //     throw new Error('Membership coupon information could not be found');
  //   }
  //   if (userCouPon.state !== UserCouponState.Unused) {
  //     throw new Error('The coupon is not available');
  //   }
  //   const promotionCode = userCouPon.coupon.promotion.couponCode;
  //   if (!orderId) {
  //     const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
  //     if (!order) {
  //       throw new Error('Order does not exist');
  //     }
  //     orderId = order.id;
  //   }
  //   const order = await this.orderService.findOne(ctx, orderId, ['promotions', 'lines']);
  //   if (!order) {
  //     throw new Error('Order does not exist');
  //   }
  //   const promotions = order.promotions;
  //   const removeDiscounts: PromotionType[] = [];
  //   if (promotions) {
  //     for (const promotion of promotions) {
  //       // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //       const discountsType = (promotion.customFields as any).type;
  //       if (userCouPon.coupon.type === CouponType.Discount) {
  //         if (discountsType === PromotionType.Member) {
  //           await this.orderService.removeCouponCode(ctx, order.id, promotion.couponCode);
  //           if (removeDiscounts.indexOf(discountsType) === -1) {
  //             removeDiscounts.push(discountsType);
  //           }
  //         }
  //       }
  //       if (discountsType === PromotionType.Coupon) {
  //         await this.orderService.removeCouponCode(ctx, order.id, promotion.couponCode);
  //         if (removeDiscounts.indexOf(discountsType) === -1) {
  //           removeDiscounts.push(discountsType);
  //         }
  //       }
  //       if ((promotion.customFields as PromotionCustomFields).type === PromotionType.Coupon) {
  //         await this.couponUnboundOrder(ctx, order, promotion);
  //       }
  //     }
  //   }
  //   // userCouPon.state = UserCouponState.Lock;
  //   // userCouPon.orderIds.push(order.id);
  //   const orderIds = userCouPon.orderIds ?? [];
  //   orderIds.push(order.id);
  //   userCouPon.orderIds = orderIds;
  //   await this.connection.getRepository(ctx, UserCoupon).save(userCouPon);
  //   await this.orderService.applyCouponCode(ctx, order.id, promotionCode);
  //   return {order: await this.orderService.findOne(ctx, order.id), removeDiscounts};
  // }

  /**
   * 优惠券解绑订单（废弃）
   * @param ctx
   * @param order 订单
   * @param promotion 优惠信息
   */
  async couponUnboundOrder(ctx: RequestContext, order: Order, promotion: Promotion) {
    const userCouPon = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .leftJoinAndSelect('coupon.promotion', 'promotion')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere(`FIND_IN_SET (:orderId, userCoupon.orderIds)`, {orderId: order.id})
      .andWhere('promotion.id=:promotionId', {promotionId: promotion.id})
      .take(1)
      .getOne();

    if (userCouPon) {
      userCouPon.orderIds = userCouPon.orderIds.filter(id => id !== order.id);
      await this.connection.getRepository(ctx, UserCoupon).save(userCouPon);
    }
  }

  /**
   * 获取活跃订单可用的优惠券
   * @param ctx
   * @returns
   */
  async getActiveCoupons(ctx: RequestContext, orderId?: ID) {
    if (!orderId) {
      const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
      if (!order) {
        throw new Error('order not exist');
      }
      orderId = order.id;
    }
    const orderPromotionResults = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    const couponPromLineResults = orderPromotionResults?.promResult?.promLineResults?.find(
      promLineResult => promLineResult?.type === PromotionType.Coupon,
    );
    const orderType = (orderPromotionResults?.order?.customFields as OrderCustomFields)
      ?.purchaseType as OrderPurchaseType;
    const availableCouponsIds = orderPromotionResults?.promResult?.coupons?.map(coupon => coupon?.couponId);
    let lackOfExchangeGoodsCouponIds = couponPromLineResults?.lackOfExchangeGoodsCoupons?.map(
      coupon => coupon?.couponId as ID,
    );
    if (orderType === OrderPurchaseType.RegularOrder) {
      lackOfExchangeGoodsCouponIds = [];
    }
    let userCoupons = await this.getTheCouponValid(ctx);
    const availableCoupons: UserCoupon[] = [];
    const couponsAreNotAvailable: UserCoupon[] = [];
    const lackOfExchangeGoodsCoupons: UserCoupon[] = [];
    // 如果存在缺少兑换商品的优惠券
    if (lackOfExchangeGoodsCouponIds && lackOfExchangeGoodsCouponIds.length > 0) {
      lackOfExchangeGoodsCouponIds.map(couponId => {
        const userExchangeCoupon = userCoupons.find(userCoupon => userCoupon.id === couponId);
        if (userExchangeCoupon) {
          lackOfExchangeGoodsCoupons.push(userExchangeCoupon);
          userCoupons = userCoupons.filter(userCoupon => userCoupon.id !== couponId);
        }
      });
    }
    if (!availableCouponsIds || availableCouponsIds.length <= 0) {
      return {availableCoupons, couponsAreNotAvailable: userCoupons, lackOfExchangeGoodsCoupons};
    }

    userCoupons.map(userCoupon => {
      if ((availableCouponsIds as ID[]).indexOf(userCoupon.id) !== -1) {
        availableCoupons.push(userCoupon);
      } else {
        couponsAreNotAvailable.push(userCoupon);
      }
    });
    // const order = await this.orderService.findOne(ctx, orderId);
    // if (!order) {
    //   throw new Error('order not exist');
    // }
    // const userCoupons = await this.getTheCouponValid(ctx);
    // const availableCoupons: UserCoupon[] = [];
    // const couponsAreNotAvailable: UserCoupon[] = [];
    // for (const userCoupon of userCoupons) {
    //   const promotion = userCoupon.coupon.promotion;
    //   const availableState = await this.verifyTheOfferIsAvailable(ctx, promotion, order);
    //   if (availableState) {
    //     availableCoupons.push(userCoupon);
    //   } else {
    //     couponsAreNotAvailable.push(userCoupon);
    //   }
    // }
    return {availableCoupons, couponsAreNotAvailable, lackOfExchangeGoodsCoupons};
  }

  /**
   * 验证订单是否可用指定优惠
   * @param ctx
   * @param promotion 优惠信息
   * @param order 订单
   * @returns
   */
  async verifyTheOfferIsAvailable(ctx: RequestContext, promotion: Promotion, order: Order) {
    const conditions = getConfig().promotionOptions.promotionConditions || [];
    // const actions = getConfig().promotionOptions.promotionActions || [];
    const allConditions: {[code: string]: PromotionCondition} = conditions.reduce(
      (hash, o) => ({...hash, [o.code]: o}),
      {},
    );
    // this.allActions = actions.reduce((hash, o) => ({...hash, [o.code]: o}), {});
    let availableState = true;
    for (const condition of promotion.conditions) {
      const promotionCondition = allConditions[condition.code];
      if (!promotionCondition) {
        availableState = false;
      }
      const applicableOrConditionState = await promotionCondition.check(ctx, order, condition.args, promotion);
      if (!applicableOrConditionState) {
        availableState = false;
      }
    }
    return availableState;
  }

  /**
   * 获取有效的优惠券
   * @param ctx
   * @returns
   */
  async getTheCouponValid(ctx: RequestContext) {
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }

    const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new Error('User information not found');
    }
    const userCoupon = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .leftJoinAndSelect('coupon.promotion', 'promotion')
      .leftJoinAndSelect('userCoupon.customer', 'customer')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('customer.id =:customerId', {customerId: customer.id})
      .andWhere('userCoupon.state=:userCouponState', {userCouponState: UserCouponState.Unused})
      .getMany();
    return userCoupon;
  }

  /**
   * 获取优惠券列表
   * @param ctx
   * @param options 查询的参数
   * @param relations 关联的参数
   * @returns
   */
  async getUserCouponAll(
    ctx: RequestContext,
    options?: ListQueryOptions<UserCoupon>,
    relations?: RelationPaths<UserCoupon>,
    isAdmin = false,
    customerId?: ID,
    customerPhone?: string,
    couponName?: string,
    couponType?: CouponType,
  ) {
    const qb = this.listQueryBuilder.build(UserCoupon, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    if (!isAdmin) {
      const userId = ctx.activeUserId;
      if (userId) {
        const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
        if (!customer) {
          throw new Error('User information not found');
        }
        customerId = customer.id;
      }
    }
    qb.leftJoinAndSelect(`${qb.alias}.customer`, 'customer').leftJoinAndSelect(`${qb.alias}.coupon`, 'coupon');
    if (customerId) {
      qb.andWhere('customer.id =:customerId', {
        customerId: customerId,
      });
    }
    if (customerPhone) {
      qb.andWhere('customer.phoneNumber =:customerPhone', {
        customerPhone: customerPhone,
      });
    }
    if (couponName) {
      qb.andWhere('coupon.name =:couponName', {
        couponName: couponName,
      });
    }
    if (couponType) {
      qb.andWhere('coupon.type =:couponType', {
        couponType: couponType,
      });
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  getUserCouponAllByCustomerId(ctx: RequestContext, customerId: ID) {
    const qb = this.connection.getRepository(ctx, UserCoupon).createQueryBuilder('userCoupon');
    qb.leftJoin('userCoupon.channels', 'channel').andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    qb.leftJoinAndSelect('userCoupon.coupon', 'coupon');
    qb.leftJoinAndSelect('userCoupon.customer', 'customer').andWhere('customer.id =:customerId', {
      customerId: customerId,
    });
    qb.andWhere('userCoupon.state=:userCouponState', {userCouponState: UserCouponState.Unused});
    return qb.getMany();
  }

  /**
   * 获取用户优惠券详情
   * @param ctx
   * @param userCouponId
   * @param options
   * @param relations
   * @returns
   */
  async getUserCouponOne(
    ctx: RequestContext,
    userCouponId: ID,
    options?: ListQueryOptions<UserCoupon>,
    relations?: RelationPaths<UserCoupon>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(UserCoupon, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.where(`${qb.alias}.id=:userCouponId`, {userCouponId});
    if (!isAdmin) {
      const userId = ctx.activeUserId;
      if (userId) {
        const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
        if (!customer) {
          throw new Error('User information not found');
        }
        qb.leftJoinAndSelect(`${qb.alias}.customer`, 'customer').andWhere('customer.id =:customerId', {
          customerId: customer.id,
        });
      }
    }
    const userCoupons = await qb.getMany();

    if (userCoupons.length > 0) {
      return userCoupons[0];
    }
    return null;
  }

  /**
   * 领取优惠券
   * @param ctx
   * @param couponId  优惠券id
   * @returns
   */
  async claimCoupon(
    ctx: RequestContext,
    couponId: ID,
    customerId?: ID,
    outStockSkip = false,
    sourceType = SourceType.OwnMall,
    sourceId: ID = '',
  ) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      if (outStockSkip) {
        Logger.debug('coupon not exist');
        return;
      }
      throw new OperationError('优惠券信息已失效');
    }
    if (coupon.enable === false) {
      if (outStockSkip) {
        Logger.info('优惠券禁用,跳过');
        return;
      }
      throw new OperationError('你来晚了，红包已抢完');
    }
    if (!customerId) {
      const userId = ctx.activeUserId;
      if (!userId) {
        throw new UnauthorizedError();
      }
      const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new UnauthorizedError();
      }
      customerId = customer.id;
    }
    const validateState = await this.validateClaimCoupon(ctx, coupon, customerId, outStockSkip);
    if (!validateState) {
      return;
    }
    const userCoupon = await this.createUserCoupon(ctx, coupon, customerId, sourceType, sourceId);
    return userCoupon;
  }

  //创建用户优惠券
  async createUserCoupon(
    ctx: RequestContext,
    coupon: Coupon,
    customerId: ID,
    sourceType = SourceType.OwnMall,
    sourceId: ID = '',
  ) {
    const maturityDate = await this.getCouponMaturityDate(ctx, coupon);
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer) {
      throw new Error('User information not found');
    }

    let userCouponState = UserCouponState.Unused;
    if (coupon.state === CouponState.NotStarted) {
      userCouponState = UserCouponState.NotStarted;
    }
    let userCoupon = new UserCoupon({
      claimAt: new Date(),
      validFromAt: maturityDate.validFromAt,
      maturityAt: maturityDate.maturityAt,
      maturityType: maturityDate.maturityType,
      coupon: coupon,
      state: userCouponState,
      customer: customer,
      sourceType,
      sourceId,
    });
    userCoupon = await this.channelService.assignToCurrentChannel(userCoupon, ctx);
    userCoupon = await this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
    await this.sendMessageService.claimCouponNotification(ctx, customerId, userCoupon);
    return userCoupon;
  }

  /**
   * 获取优惠券到期时间
   * @param ctx
   * @param coupon
   * @returns
   */
  async getCouponMaturityDate(ctx: RequestContext, coupon: Coupon) {
    let maturityType: ValidityPeriodType;
    let maturityAt: Date = new Date();
    let validFromAt = new Date();
    const validityPeriod = coupon.validityPeriod;
    if (validityPeriod.type === ValidityPeriodType.LongTime) {
      maturityType = ValidityPeriodType.LongTime;
    } else if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      maturityType = ValidityPeriodType.TemporalInterval;
      maturityAt = validityPeriod.endTime;
      const startTime = new Date(validityPeriod.startTime);
      if (startTime > new Date()) {
        validFromAt = validityPeriod.startTime;
      } else {
        validFromAt = new Date();
      }
    } else if (validityPeriod.type === ValidityPeriodType.ValidDays) {
      if (!validityPeriod.numberOfDays) {
        throw new Error('numberOfDays error');
      }
      const maturityTime = DateTime.fromJSDate(new Date()).plus({days: validityPeriod.numberOfDays});
      maturityType = ValidityPeriodType.ValidDays;
      maturityAt = maturityTime.toJSDate();
      validFromAt = new Date();
    } else {
      throw new Error('Incorrect time type');
    }
    return {
      maturityType,
      maturityAt,
      validFromAt,
    };
  }

  /**
   * 验证优惠券是否可领取
   * @param ctx
   * @param coupon
   */
  async validateClaimCoupon(ctx: RequestContext, coupon: Coupon, customerId?: ID, outStockSkip = false) {
    // 未开始的优惠券也可以领取
    if (coupon.state !== CouponState.Normal && coupon.state !== CouponState.NotStarted) {
      if (outStockSkip) {
        Logger.info('优惠券状态错误,跳过');
        return false;
      }
      // if (coupon.state === CouponState.NotStarted) {
      //   throw new OperationError('优惠券活动未开始');
      // } else
      if (coupon.state === CouponState.HaveEnded) {
        throw new OperationError('优惠券活动已结束');
      } else if (coupon.state === CouponState.Failure) {
        throw new OperationError('优惠券活动已失效');
      }
      throw new OperationError('你来晚了，红包已抢完');
    }
    if (!customerId) {
      const userId = ctx.activeUserId;
      if (!userId) {
        throw new UnauthorizedError();
      }

      const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new UnauthorizedError();
      }
      customerId = customer.id;
    }
    if (coupon.claimRestriction !== -1) {
      const count = await this.getUserCouponCount(ctx, customerId, coupon.id);
      if (count >= coupon.claimRestriction) {
        if (outStockSkip) {
          Logger.info('领取数量超过限制,跳过');
          return false;
        }
        throw new OperationError('您领取的该优惠券已达到最大领取数量');
      }
    }
    const totalCount = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoin('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id =:couponId', {couponId: coupon.id})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .getCount();
    if (totalCount >= coupon.totalQuantity) {
      if (outStockSkip) {
        Logger.info('领取数量超过限制,跳过');
        return false;
      }
      throw new OperationError('你来晚了，红包已抢完');
    }
    // if (coupon.whetherRestrictUsers) {
    //   if (!coupon.memberPlanIds.find(id => idsAreEqual(id, customer.id))) {
    //     throw new IllegalOperationError(`You can't claim the coupon`);
    //   }
    // }
    return this.verifyThatTheUserIsInTheCouponMembershipGroup(ctx, coupon, customerId, outStockSkip);
  }

  // 获取指定优惠券的全部用户优惠券数量
  async getAllUserCouponCount(ctx: RequestContext, couponId: ID) {
    const totalCount = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoin('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id =:couponId', {couponId: couponId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('userCoupon.isReturned = false')
      .getCount();
    return totalCount;
  }

  //获取用户已领优惠券数
  async getUserCouponCount(ctx: RequestContext, customerId: ID, couponId: ID) {
    const count = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoin('userCoupon.channels', 'channel')
      .leftJoin('userCoupon.coupon', 'coupon')
      .leftJoin('userCoupon.customer', 'customer')
      .andWhere('customer.id=:customerId', {customerId: customerId})
      .andWhere('coupon.id =:couponId', {couponId: couponId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .getCount();
    return count;
  }

  /**
   * 验证用户是否是优惠券会员组的成员
   * @param ctx
   * @param coupon 优惠券
   * @param customerId 用户id
   */
  async verifyThatTheUserIsInTheCouponMembershipGroup(
    ctx: RequestContext,
    coupon: Coupon,
    customerId: ID,
    outStockSkip = false,
  ) {
    if (coupon.whetherRestrictUsers) {
      const memberPlanIds = coupon.memberPlanIds;
      const customerGroupIds = [];
      for (const memberPlanId of memberPlanIds) {
        const memberPlan = await this.connection.getEntityOrThrow(ctx, MembershipPlan, memberPlanId, {
          relations: ['customerGroup'],
        });
        if (memberPlan.state !== MembershipPlanState.Shelf && memberPlan.state === MembershipPlanState.Disable) {
          continue;
        }
        const validityPeriod = memberPlan.validityPeriod;
        if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
          if (new Date(validityPeriod.startTime) > new Date() || new Date(validityPeriod.endTime) < new Date()) {
            continue;
          }
        }
        customerGroupIds.push(memberPlan.customerGroup.id);
      }
      const customerGroup = await this.connection
        .getRepository(ctx, CustomerGroup)
        .createQueryBuilder('customerGroup')
        .leftJoin('customerGroup.customers', 'customer')
        .andWhere('customer.id =:customerId', {customerId: customerId})
        .andWhere('customerGroup.id IN (:...customerGroupIds)', {customerGroupIds: customerGroupIds})
        .take(1)
        .getOne();
      if (!customerGroup) {
        if (outStockSkip) {
          Logger.info('领取数量超过限制,跳过');
          return false;
        }
        throw new OperationError(`该优惠券只能会员领取!`);
      }
    }
    return true;
  }

  /**
   * 查询全部优惠券
   * @param ctx
   * @param options   查询的参数
   * @param relations 关联的参数
   * @returns
   */
  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<Coupon>,
    relations?: RelationPaths<Coupon>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(Coupon, options, {
      ctx,
      relations: (relations || []).concat(['userCoupons']),
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          PromotionType.Coupon,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }
  /**
   * 查询优惠券详情
   * @param ctx
   * @param couponId  优惠券id
   * @param options  查询的参数
   * @param relations 关联的参数
   * @returns
   */
  async findOne(
    ctx: RequestContext,
    couponId: ID,
    options?: ListQueryOptions<Coupon>,
    relations?: RelationPaths<Coupon>,
  ) {
    const qb = this.listQueryBuilder.build(Coupon, options, {
      ctx,
      relations: relations ?? ['promotion', 'userCoupons'],
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.id=:couponId`, {couponId: couponId});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const coupons = await qb.getMany();
    if (coupons.length > 0) {
      return coupons[0];
    }
    return null;
  }

  /**
   * 查询优惠券详情
   * @param ctx
   * @param couponId  优惠券id
   * @param options  查询的参数
   * @param relations 关联的参数
   * @returns
   */
  async findByIds(
    ctx: RequestContext,
    couponIds: ID[],
    options?: ListQueryOptions<Coupon>,
    relations?: RelationPaths<Coupon>,
  ) {
    const qb = this.listQueryBuilder.build(Coupon, options, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.leftJoin(`${qb.alias}.channels`, 'channel');
    qb.andWhere(`${qb.alias}.id IN (:...couponIds)`, {couponIds});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    const coupons = await qb.getMany();
    return coupons;
  }

  async couponAvailableCount(ctx: RequestContext, couponId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }

    const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new UserInputError('User information not found');
    }
    const customerId = customer.id;
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    if (coupon.claimRestriction !== -1) {
      const count = await this.connection
        .getRepository(ctx, UserCoupon)
        .createQueryBuilder('userCoupon')
        .leftJoin('userCoupon.channels', 'channel')
        .leftJoin('userCoupon.coupon', 'coupon')
        .leftJoin('userCoupon.customer', 'customer')
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
        .andWhere('customer.id=:customerId', {customerId: customerId})
        .andWhere('coupon.id =:couponId', {couponId: coupon.id})
        .getCount();
      if (count >= coupon.claimRestriction) {
        return 0;
      } else {
        return coupon.claimRestriction - count;
      }
    } else {
      return -1;
    }
  }

  /**
   * 失效用户优惠券
   * @param ctx
   * @param userCouponId  用户优惠券id
   * @returns
   */
  async failureUserCoupon(ctx: RequestContext, userCouponId: ID) {
    const userCoupon = await this.getUserCouponOne(ctx, userCouponId, undefined, undefined, true);
    if (!userCoupon) {
      throw new Error('user coupon not exist ');
    }
    if (userCoupon.state !== UserCouponState.Unused && userCoupon.state !== UserCouponState.NotStarted) {
      throw new Error('The coupon has been used or has expired');
    }
    userCoupon.state = UserCouponState.Expire;
    return this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
  }

  /**
   * 失效优惠券
   * @param ctx
   * @param couponId 优惠券id
   * @returns
   */
  async failure(ctx: RequestContext, couponId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new Error('coupon not exist');
    }
    coupon.state = CouponState.Failure;
    await this.connection.getRepository(ctx, Promotion).update(coupon.promotion.id, {enabled: false});
    await this.connection
      .getRepository(ctx, UserCoupon)
      .update({coupon: {id: couponId}, state: UserCouponState.Unused}, {state: UserCouponState.Expire});
    await this.cacheService.removeCache([
      CacheKeyManagerService.promotion(coupon?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(coupon?.promotion.id, ctx.channelId),
    ]);
    return this.connection.getRepository(ctx, Coupon).save(coupon);
  }

  /**
   * 新增或修改优惠券
   * @param ctx
   * @param input 优惠券信息
   * @returns
   */
  async upsertCoupon(ctx: RequestContext, input: CouponInput) {
    this.validate(ctx, input);
    //满减或折扣
    let coupon = new Coupon({
      ...(input as unknown as Coupon),
    });
    coupon.state = CouponState.Normal;
    if (
      input.validityPeriod.type === ValidityPeriodType.LongTime ||
      input.validityPeriod.type === ValidityPeriodType.ValidDays
    ) {
      coupon.state = CouponState.Normal;
    }
    if (input.validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      if (new Date(input.validityPeriod.startTime) > new Date()) {
        coupon.state = CouponState.NotStarted;
      }
      if (new Date(input.validityPeriod.endTime) <= new Date()) {
        coupon.state = CouponState.HaveEnded;
      }
    }
    coupon = await this.channelService.assignToCurrentChannel(coupon, ctx);
    coupon = await this.connection.getRepository(ctx, Coupon).save(coupon);
    const promotion = await this.upsertPromotion(ctx, coupon);
    coupon.promotion = promotion;
    await this.connection.getRepository(ctx, Coupon).save(coupon);
    if (input.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return this.findOne(ctx, coupon.id);
  }

  /**
   * 新增或修改促销活动
   * @param ctx
   * @param coupon  优惠券信息
   * @returns
   */
  async upsertPromotion(ctx: RequestContext, coupon: Coupon) {
    const promotionInput = {
      name: coupon.name,
      enabled: coupon.promotion ? coupon.promotion.enabled : true,
      couponCode: coupon.promotion ? coupon.promotion.couponCode : generatePublicId(),
      conditions: [],
      actions: [],
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: coupon.name,
        },
      ],
    };
    let minimum = 0;
    if (coupon.preferentialContent.preferentialType === PreferentialType.ThresholdFree) {
      minimum = 0;
    } else {
      if (!coupon.preferentialContent.minimum) {
        throw new Error('The minimum threshold cannot be empty');
      }
      minimum = coupon.preferentialContent.minimum;
    }
    if (coupon.type === CouponType.Exchange) {
      //兑换券限制条件
      promotionInput.conditions.push({
        code: productConvertibility.code,
        arguments: [
          {name: 'productIds', value: coupon.applicableProduct.productIds},
          {
            name: 'includingDiscountProduct',
            value: coupon.preferentialContent.includingDiscountProducts,
          },
          {name: 'minimum', value: minimum},
        ],
      } as never);
      //兑换券优惠
      promotionInput.actions.push({
        code: convertibilityAction.code,
        arguments: [{name: 'productIds', value: coupon.applicableProduct.productIds}],
      } as never);
    } else if (coupon.type === CouponType.Discount) {
      //折扣限制条件
      promotionInput.conditions.push({
        code: productTotalPriceConditions.code,
        arguments: [
          {name: 'productIds', value: coupon.applicableProduct.productIds},
          {name: 'type', value: coupon.applicableProduct.applicableType},
          {name: 'minimum', value: minimum},
        ],
      } as never);
      //折扣优惠
      promotionInput.actions.push({
        code: orderDiscountMax.code,
        arguments: [
          {name: 'discount', value: coupon.preferentialContent.discount},
          {name: 'ceiling', value: coupon.preferentialContent.maximumOffer},
        ],
      } as never);
    } else if (coupon.type === CouponType.FullSubtraction) {
      //满减限制条件
      promotionInput.conditions.push({
        code: productTotalPriceConditions.code,
        arguments: [
          {name: 'productIds', value: coupon.applicableProduct.productIds},
          {name: 'type', value: coupon.applicableProduct.applicableType},
          {name: 'minimum', value: minimum},
        ],
      } as never);
      //满减优惠
      promotionInput.actions.push({
        code: orderFixedAmount.code,
        arguments: [{name: 'discount', value: coupon.preferentialContent.discount}],
      } as never);
    }
    //再次查询优惠券信息
    coupon = (await this.findOne(ctx, coupon.id)) as Coupon;
    //如果有促销活动则更新，没有则创建
    if (coupon.promotion) {
      const updatePromotion = {...promotionInput, id: coupon.promotion.id};
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection
        .getRepository(ctx, Promotion)
        .update(promotion.id, {customFields: {type: PromotionType.Coupon}});
      return promotion;
    }
  }
  /**
   * 验证优惠券信息参数是否正确
   * @param ctx
   * @param input
   */
  validate(ctx: RequestContext, input: CouponInput) {
    const validityPeriod = input.validityPeriod;
    if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      if (!validityPeriod.startTime || !validityPeriod.endTime) {
        throw new Error('When the validity period is temporalInterval, the start time and end time cannot be empty');
      }
      if (validityPeriod.startTime > validityPeriod.endTime) {
        throw new Error('The start time cannot be greater than the end time');
      }
    } else if (validityPeriod.type === ValidityPeriodType.ValidDays) {
      if (!validityPeriod.numberOfDays || validityPeriod.numberOfDays < 0) {
        throw new Error(
          'When the discount volume is valid for days, the valid days cannot be empty and cannot be less than zero',
        );
      }
    }
    const preferentialContent = input.preferentialContent;
    if (preferentialContent.preferentialType === PreferentialType.Satisfy) {
      if (!preferentialContent.minimum || preferentialContent.minimum <= 0) {
        throw new Error('The preferential minimum threshold cannot be empty and cannot be less than zero');
      }
    }
    if (input.type === CouponType.Exchange) {
      //兑换券
      const applicableProduct = input.applicableProduct;
      if (applicableProduct.applicableType !== ApplicableType.AvailableGoods) {
        throw new Error('Exchange roll item type can only be availableGoods');
      }
      if (!applicableProduct.productIds || applicableProduct.productIds.length <= 0) {
        throw new Error('The exchange goods in the exchange roll cannot be empty');
      }
    } else {
      if (!preferentialContent.discount || preferentialContent.discount <= 0) {
        throw new Error('The discount value cannot be empty and cannot be less than zero');
      }
    }

    if (input.type === CouponType.Discount) {
      const discount = input.preferentialContent.discount;
      if (!discount && discount !== 0) {
        throw new UserInputError('The discount cannot be empty');
      }
      if (discount < 0 || discount >= 100) {
        throw new UserInputError('The discount can only be 1 to 99');
      }
    }
  }

  // 定时执行检查更新优惠券状态
  async timeoutCouponAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.timeoutCoupon(ctx);
    }
  }

  /**
   * 检查更新优惠券状态
   */
  async timeoutCoupon(ctx: RequestContext) {
    const coupons = await this.connection
      .getRepository(ctx, Coupon)
      .find({where: {state: In([CouponState.NotStarted, CouponState.Normal])}, relations: ['promotion']});
    const promotionId = [];
    for (const coupon of coupons) {
      const validityPeriod = coupon.validityPeriod;
      if (validityPeriod.type !== ValidityPeriodType.TemporalInterval) {
        continue;
      }
      let isUpdate = false;
      if (new Date(validityPeriod.startTime) <= new Date() && coupon.state === CouponState.NotStarted) {
        coupon.state = CouponState.Normal;
        isUpdate = true;
      }
      if (new Date(validityPeriod.endTime) <= new Date()) {
        coupon.state = CouponState.HaveEnded;
        isUpdate = true;
      }
      await this.connection.getRepository(ctx, Coupon).save(coupon);
      if (isUpdate) {
        promotionId.push(coupon.promotion.id);
      }
    }
    const keys = [
      ...promotionId.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
      ...promotionId.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
    ];
    await this.cacheService.removeCache(keys);
  }

  async timeoutUserCouponAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.timeoutUserCoupon(ctx);
    }
  }

  /**
   * 检查更新用户优惠券状态
   */
  async timeoutUserCoupon(ctx: RequestContext) {
    const notStartCoupon = await this.connection.getRepository(ctx, UserCoupon).find({
      where: {
        state: UserCouponState.NotStarted,
        validFromAt: LessThanOrEqual(new Date()),
      },
    });
    for (const userCoupon of notStartCoupon) {
      userCoupon.state = UserCouponState.Unused;
      await this.connection.getRepository(ctx, UserCoupon).save(userCoupon);
    }
    const userCoupons = await this.connection
      .getRepository(ctx, UserCoupon)
      .find({where: {state: UserCouponState.Unused, maturityAt: LessThanOrEqual(new Date())}});
    for (const userCoupon of userCoupons) {
      await this.connection.getRepository(ctx, UserCoupon).update(userCoupon.id, {state: UserCouponState.Expire});
    }
  }

  /**
   * 用户优惠券过期提醒
   */
  async userCouponExpirationReminder() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const expirationReminderDays = Number(process.env.COUPON_EXPIRATION_REMINDER_DAYS || 1);
    const expirationReminderTime = DateTime.fromJSDate(new Date()).minus({days: expirationReminderDays}).toJSDate();
    const userCoupons = await this.connection.getRepository(ctx, UserCoupon).find({
      where: {state: UserCouponState.Unused, maturityAt: LessThanOrEqual(expirationReminderTime)},
      relations: ['customer', 'coupon', 'channels'],
    });
    for (const userCoupon of userCoupons) {
      const userCtx = await this.createCtxContinueNext(ctx, userCoupon);
      if (!userCtx) {
        continue;
      }
      await this.sendMessageService.couponExpiredNotice(userCtx, userCoupon.customer.id, userCoupon);
    }
  }

  async createCtxContinueNext(ctx: RequestContext, userCoupon: UserCoupon) {
    const customer = userCoupon.customer;
    const user = customer.user;
    let channelOrToken;
    if (userCoupon.channels.length > 1) {
      for (const channel of userCoupon.channels) {
        if (channel.code !== DEFAULT_CHANNEL_CODE) {
          channelOrToken = channel;
        }
      }
    } else {
      channelOrToken = userCoupon.channels[0];
    }
    if (!channelOrToken) {
      return;
    }
    channelOrToken = await this.connection
      .getRepository(ctx, Channel)
      .findOne({where: {id: channelOrToken.id}, relations: ['defaultTaxZone']});
    if (!channelOrToken) {
      return;
    }
    const newCtx = await this.requestContextService.create({
      apiType: 'admin',
      channelOrToken: channelOrToken,
      user: user,
      languageCode: LanguageCode.zh_Hans,
    });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (ctx as any).session['customer'] = customer;
    this.requestContextCacheService.set(ctx, 'activeTaxZone', channelOrToken!.defaultTaxZone);

    return newCtx;
  }

  /**
   * 取消订单优惠券
   * @param ctx
   * @param orderId
   */
  @Transaction()
  async couponCancel(ctx: RequestContext, orderId: ID) {
    // const order = await this.orderService.findOne(ctx, orderId, ['promotions', 'lines', 'customer']);
    // if (!order) {
    //   throw new Error('order not exist');
    // }
    // Logger.debug(`订单取消,检查优惠券是否需要解锁`);
    // const promotions = order.promotions;
    // if (promotions) {
    //   Logger.debug(`存在优惠信息:${promotions.length}条}`);
    //   for (const promotion of promotions) {
    //     if ((promotion.customFields as PromotionCustomFields).type === PromotionType.Coupon) {
    //       Logger.debug(`优惠信息为优惠券:${promotion.id}`);
    //       await this.couponUnlocking(ctx, order, promotion);
    //     }
    //   }
    // }
    const userCouponId = await this.getPromotionCouponId(ctx, orderId);
    if (userCouponId) {
      await this.couponUnlocking(ctx, userCouponId, orderId);
    }
  }
  /**
   * 优惠券解锁
   * @param ctx
   * @param order
   * @param promotion
   */
  async couponUnlocking(ctx: RequestContext, userCouponId: ID, orderId: ID) {
    const userCouPon = await this.connection.getRepository(ctx, UserCoupon).findOne({where: {id: userCouponId}});
    if (
      userCouPon &&
      (userCouPon.state === UserCouponState.HaveUsed || userCouPon.state === UserCouponState.Lock) &&
      userCouPon.orderIds.includes(String(orderId))
    ) {
      Logger.debug(`优惠券解锁:${userCouPon.id}`);
      userCouPon.state = UserCouponState.Unused;
      userCouPon.useAt = null;
      userCouPon.orderIds = userCouPon.orderIds.filter(id => String(id) !== String(orderId));
      await this.connection.getRepository(ctx, UserCoupon).save(userCouPon);
    }
  }

  async membershipOrderCoupons(ctx: RequestContext, membershipOrder: MembershipOrder) {
    const membershipOrderId = membershipOrder.id;
    const userCoupons = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .andWhere('userCoupon.sourceType = :sourceType', {sourceType: SourceType.MemberOrder})
      .andWhere('userCoupon.sourceId = :sourceId', {sourceId: membershipOrderId})
      .getMany();
    return userCoupons;
  }

  async membershipPlanCoupon(ctx: RequestContext, membershipPlan: MembershipPlan) {
    const rightsCoupon = membershipPlan.rightsCoupon;
    return this.rightsCoupon(ctx, rightsCoupon, membershipPlan.id);
  }

  async giftCardCoupon(ctx: RequestContext, giftCard: GiftCard) {
    const rightsCoupon = giftCard.rightsCoupon;
    return this.rightsCoupon(ctx, rightsCoupon);
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, rightsCoupon: RightsCoupon, membershipPlanId?: ID) => {
      if (rightsCoupon?.enable) {
        const presentedCoupons = rightsCoupon.presentedCoupon;
        if (presentedCoupons?.length && membershipPlanId) {
          return CacheKeyManagerService.membershipPlanCoupon(membershipPlanId as ID, ctx.channelId);
        }
      }
      return '';
    },
  })
  async rightsCoupon(ctx: RequestContext, rightsCoupon: RightsCoupon, membershipPlanId?: ID) {
    const coupons = [];
    if (rightsCoupon?.enable) {
      const presentedCoupons = rightsCoupon.presentedCoupon;
      if (presentedCoupons?.length) {
        const couponIds = presentedCoupons.map(coupon => coupon.couponId);
        let couponsItems: Coupon[] = [];
        const memoryStorageCacheKey = CacheKeyManagerService.membershipPlanCoupon(
          membershipPlanId as ID,
          ctx.channelId,
        );
        if (ctx.apiType === 'shop' && membershipPlanId) {
          const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
          if (cacheData) {
            couponsItems = cacheData as Coupon[];
          }
        }
        if (!couponsItems || couponsItems.length === 0) {
          const qb = this.connection
            .getRepository(ctx, Coupon)
            .createQueryBuilder('coupon')
            .leftJoinAndSelect('coupon.promotion', 'promotion');
          // .leftJoinAndSelect('coupon.userCoupons', 'userCoupons');
          if (ctx.apiType === 'shop' && membershipPlanId) {
            qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
          }
          couponsItems = await qb
            .andWhere('coupon.id IN (:...couponIds)', {couponIds})
            .andWhere('coupon.deletedAt is null')
            .getMany();
          if (ctx.apiType === 'shop' && membershipPlanId) {
            this.memoryStorageService.set(memoryStorageCacheKey, couponsItems);
          }
        }
        for (const presentedCoupon of presentedCoupons) {
          const coupon = couponsItems.find(item => idsAreEqual(item.id, presentedCoupon.couponId));
          if (coupon && coupon.state !== CouponState.Failure && coupon.state !== CouponState.HaveEnded) {
            coupons.push({coupon, quantity: presentedCoupon.presentedCount});
          }
        }
      }
    }
    return coupons;
  }

  async findOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const coupon = await this.connection
      .getRepository(ctx, Coupon)
      .createQueryBuilder('coupon')
      .leftJoin('coupon.channels', 'channel')
      .leftJoinAndSelect('coupon.promotion', 'promotion')
      .where('promotion.id=:promotionId', {promotionId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    return coupon;
  }

  async softDeleteCoupon(ctx: RequestContext, couponId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new UserInputError(`coupon not found`);
    }
    if (coupon.state !== CouponState.Failure && coupon.state !== CouponState.HaveEnded) {
      throw new UserInputError(`coupon state is not failure`);
    }
    coupon.deletedAt = new Date();
    await this.connection.getRepository(ctx, Coupon).save(coupon);
    await this.cacheService.removeCache([
      CacheKeyManagerService.promotion(coupon?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(coupon?.promotion.id, ctx.channelId),
    ]);
    return {
      result: DeletionResult.Deleted,
    };
  }
  async enableSwitch(ctx: RequestContext, couponId: ID) {
    const coupon = await this.findOne(ctx, couponId);
    if (!coupon) {
      throw new UserInputError(`coupon not found`);
    }
    coupon.enable = !coupon.enable;
    await this.connection.getRepository(ctx, Coupon).save(coupon);
    await this.cacheService.removeCache([
      CacheKeyManagerService.promotion(coupon?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(coupon?.promotion.id, ctx.channelId),
    ]);
    return coupon;
  }

  activityContent(ctx: RequestContext, coupon: Coupon) {
    const preferentialContent = coupon.preferentialContent;
    if (coupon.type === CouponType.Exchange) {
      if (preferentialContent.preferentialType === PreferentialType.ThresholdFree) {
        return [`可兑换指定商品，满任意金额可用`];
      }
      if (preferentialContent.includingDiscountProducts) {
        return [`可兑换指定商品,满${preferentialContent!.minimum! / 100}元使用,包含兑换商品金额`];
      }
      return [`可兑换指定商品,满${preferentialContent.minimum! / 100}元使用,不包含兑换商品金额`];
    } else if (coupon.type === CouponType.FullSubtraction) {
      if (preferentialContent.preferentialType === PreferentialType.ThresholdFree) {
        return [`无门槛,立减${preferentialContent.discount! / 100}元`];
      } else {
        return [`满${preferentialContent.minimum! / 100}元减${preferentialContent.discount! / 100}元`];
      }
    } else if (coupon.type === CouponType.Discount) {
      let str = ``;
      const discount = preferentialContent.discount ? preferentialContent.discount / 10 : 0;
      if (preferentialContent.preferentialType === PreferentialType.ThresholdFree) {
        str = `无门槛,打${discount}折`;
      } else {
        str = `满${preferentialContent.minimum! / 100}元打${discount}折`;
      }
      if (preferentialContent.maximumOffer) {
        str += `,最高优惠${preferentialContent.maximumOffer! / 100}元`;
      }
      return [str];
    }
  }

  async activityTime(ctx: RequestContext, coupon: Coupon) {
    const userCoupon = await this.getUserFirstCoupon(ctx, coupon.id);
    if (!userCoupon) {
      const validityPeriod = coupon.validityPeriod;
      if (validityPeriod.type === ValidityPeriodType.LongTime) {
        return `永久有效`;
      } else if (validityPeriod.type === ValidityPeriodType.TemporalInterval) {
        return `${validityPeriod.startTime}至${validityPeriod.endTime}`;
      } else if (validityPeriod.type === ValidityPeriodType.ValidDays) {
        return `领取后${validityPeriod.numberOfDays}天有效`;
      }
    } else {
      return `${DateTime.fromJSDate(userCoupon.claimAt).toFormat('yyyy-MM-dd')}~${DateTime.fromJSDate(
        userCoupon.maturityAt,
      ).toFormat('yyyy-MM-dd')}`;
    }
  }

  async getUserFirstCoupon(ctx: RequestContext, couponId: ID) {
    try {
      const userId = ctx.activeUserId;
      if (!userId) {
        return;
      }

      const customer = await this.customerProductService.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        return;
      }
      const userCoupon = await this.connection
        .getRepository(ctx, UserCoupon)
        .createQueryBuilder('userCoupon')
        .leftJoin('userCoupon.channels', 'channel')
        .leftJoin('userCoupon.coupon', 'coupon')
        .leftJoin('userCoupon.customer', 'customer')
        .andWhere('customer.id=:customerId', {customerId: customer.id})
        .andWhere('coupon.id =:couponId', {couponId: couponId})
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
        .orderBy('userCoupon.maturityAt', 'ASC')
        .take(1)
        .getOne();
      return userCoupon;
    } catch (error) {
      return;
    }
  }

  async getCouponPrice(
    ctx: RequestContext,
    targetId: ID,
  ): Promise<{
    giftPrice: number;
    giftImage: string;
  }> {
    const coupon = (await this.findOne(ctx, targetId)) as Coupon;
    if (coupon?.type === CouponType.Discount) {
      return {
        giftPrice: 0,
        giftImage: ``,
      };
    } else if (coupon?.type === CouponType.FullSubtraction) {
      return {
        giftPrice: coupon.preferentialContent.discount || 0,
        giftImage: ``,
      };
    }
    const applicableProduct = coupon.applicableProduct;
    if (!applicableProduct) {
      return {
        giftPrice: 0,
        giftImage: ``,
      };
    }
    const qb = this.connection
      .getRepository(ctx, ProductVariant)
      .createQueryBuilder('productVariant')
      .leftJoin('productVariant.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('productVariant.deletedAt is null')
      .leftJoinAndSelect(
        ProductVariantPrice,
        'productVariantPrice',
        'productVariantPrice.variantId = productVariant.id and productVariantPrice.channelId = :channelId',
        {channelId: ctx.channelId},
      )
      .select(`MAX(productVariantPrice.price)`, 'maxPrice')
      .addSelect(`productVariant.id`, 'productVariantId')
      .groupBy('productVariant.id')
      .orderBy('maxPrice', 'DESC')
      .take(1);
    if (applicableProduct.applicableType === ApplicableType.AvailableGoods) {
      qb.andWhere('productVariant.productId IN (:...productIds)', {productIds: applicableProduct.productIds});
    } else if (applicableProduct.applicableType === ApplicableType.UnusableGoods) {
      qb.andWhere('productVariant.productId NOT IN (:...productIds)', {productIds: applicableProduct.productIds});
    }
    // 查询最高价值商品
    const maxPriceProductVariant = await qb.getRawOne<{maxPrice: number; productVariantId: ID}>();
    let giftImage = ``;
    if (maxPriceProductVariant) {
      const productVariant = await this.customerProductVariantService.getVariantWithPriceAndTax(
        ctx,
        maxPriceProductVariant.productVariantId,
      );
      giftImage = productVariant?.featuredAsset?.source ?? productVariant?.product?.featuredAsset?.source ?? ``;
    }

    if (giftImage) {
      const urlPrefix = `https://${process.env.MINIO_ENDPOINT_CDN}/${process.env.ADMIN_BUCKET}/`;
      giftImage = giftImage.includes(urlPrefix) ? giftImage : urlPrefix + giftImage;
    }
    return {
      giftPrice: maxPriceProductVariant?.maxPrice || 0,
      giftImage: giftImage,
    };
  }
}
