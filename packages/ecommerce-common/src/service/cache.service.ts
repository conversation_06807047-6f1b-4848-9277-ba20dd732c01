import {Injectable} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  Collection,
  ID,
  Logger,
  Product,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
  Translated,
  TranslatorService,
} from '@vendure/core';
import Redis from 'ioredis';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
@Injectable()
export class CacheService {
  redis: Redis;
  constructor(
    private connection: TransactionalConnection,
    private requestContextService: RequestContextService,
    private translator: TranslatorService,
    private memoryStorageService: MemoryStorageService,
  ) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: +(process.env.REDIS_PORT ?? 6379),
    });
  }

  async removeCache(key: string | string[]) {
    Logger.info(`Redis执行清除缓存:${JSON.stringify(key)}`);
    // 如果key的类型不是数组 则转换成数组
    if (!Array.isArray(key)) {
      key = [key];
    }
    // 每个key都多添加两个key 一个后面带-count 一个后面带-pagination
    key = key.flatMap(k => [k, `${k}-count`, `${k}-pagination`]);
    // 分批删除缓存
    while (key.length > 0) {
      const batch = key.splice(0, 1000);
      await this.connection.rawConnection.queryResultCache?.remove(batch);
      for (const k of batch) {
        await this.redis.publish('cache_key', k);
      }
    }
  }

  async clearProductCache(ctx: RequestContext, productId: ID) {
    await this.removeCache([
      CacheKeyManagerService.product(productId, ctx.channelId),
      CacheKeyManagerService.productFindOne(productId, ctx.channelId),
      CacheKeyManagerService.productHydrate(productId, ctx.channelId),
      CacheKeyManagerService.productVariants(productId, ctx.channelId),
      CacheKeyManagerService.collection(productId, ctx.channelId),
      CacheKeyManagerService.optionGroups(productId, ctx.channelId),
      CacheKeyManagerService.optionGroupsOptions(productId, ctx.channelId),
      CacheKeyManagerService.facetValues(productId, ctx.channelId),
      CacheKeyManagerService.productFeaturedAsset(productId, ctx.channelId),
      CacheKeyManagerService.productEntityAssets(productId, ctx.channelId),
      CacheKeyManagerService.productPurchasePermission(productId, ctx.channelId),
      CacheKeyManagerService.productPurchasePermissionShowDisable(productId, ctx.channelId),
      CacheKeyManagerService.productPromotionActiveBasic(productId, ctx.channelId),
      CacheKeyManagerService.productPromotionActiveBasic(productId, ctx.channelId, true),
      CacheKeyManagerService.productVariantProduct(productId, ctx.channelId),
      CacheKeyManagerService.productVariantPricesForProduct(productId, ctx.channelId),
      CacheKeyManagerService.productTotalStock(productId, ctx.channelId),
    ]);
    const product = await this.connection.getRepository(ctx, Product).findOne({
      where: {id: productId},
      relations: ['variants', 'optionGroups'],
    });
    if (!product) {
      return;
    }
    const variantIds = product.variants.map(v => v.id);
    await this.removeCache([
      ...variantIds.map(id => CacheKeyManagerService.productVariant(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantIncludeDelete(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantHydrate(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.entityFeaturedAsset('ProductVariant', id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.entityAssets('ProductVariant', id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantStockLevel(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantPrices(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantTaxCategory(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantFacetValues(id, ctx.channelId)),
      ...variantIds.map(id => CacheKeyManagerService.productVariantOptions(id, ctx.channelId)),
    ]);
    const optionGroupIds = product.optionGroups.map(og => og.id);
    await this.removeCache([
      ...optionGroupIds.map(id => CacheKeyManagerService.productOptionGroup(id, ctx.channelId)),
      ...optionGroupIds.map(id => CacheKeyManagerService.productOptionGroupOption(id, ctx.channelId)),
    ]);

    const collections = await this.getCollectionsByProductId(ctx, productId, ctx.apiType === 'shop');
    if (collections?.length > 0) {
      const connectionIds = collections.map(collection => collection.id);
      if (connectionIds.length > 0) {
        await this.removeCache([
          ...connectionIds.map(connectionId => CacheKeyManagerService.productCollections(connectionId, ctx.channelId)),
        ]);
      }
    }
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID, publicOnly: boolean) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.collection(productId, ctx.channelId);
      }
      return '';
    },
  })
  async getCollectionsByProductId(
    ctx: RequestContext,
    productId: ID,
    publicOnly: boolean,
  ): Promise<Array<Translated<Collection>>> {
    let result: Collection[] | undefined;
    const memoryStorageCacheKey = CacheKeyManagerService.collection(productId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      result = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!result) {
      const qb = this.connection
        .getRepository(ctx, Collection)
        .createQueryBuilder('collection')
        .leftJoinAndSelect('collection.translations', 'translation')
        .leftJoin('collection.productVariants', 'variant')
        .where('variant.product = :productId', {productId})
        .groupBy('collection.id, translation.id')
        .orderBy('collection.id', 'ASC');

      if (publicOnly) {
        qb.andWhere('collection.isPrivate = :isPrivate', {isPrivate: false});
      }
      if (ctx.apiType === 'shop') {
        qb.cache(CacheKeyManagerService.collection(productId, ctx.channelId), DEFAULT_CACHE_TIMEOUT);
      }
      result = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, result);
      }
    }

    return result.map(collection => this.translator.translate(collection, ctx));
  }
}
