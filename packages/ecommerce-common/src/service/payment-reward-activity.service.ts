import {Injectable} from '@nestjs/common';
import {CacheKeyManagerService} from '@scmally/kvs';
import {MemberService} from '@scmally/member';
import {
  ChannelService,
  CustomerService,
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  RelationPaths,
  RequestContext,
  RequestContextService,
  Transaction,
  TransactionalConnection,
  idsAreEqual,
} from '@vendure/core';
import {In, IsNull, Not} from 'typeorm';
import {Coupon, OrderPaymentRewardCoupon, OrderPromotionResult, PaymentRewardActivity, UserCoupon} from '../entities';
import {OperationError} from '../error.type';
import {ApplicableType, SourceType, SuperimposeType} from '../generated-admin-types';
import {
  ActivityStatus,
  ApplicableProduct,
  CouponState,
  DeletionResult,
  GrantType,
  PaymentRewardActivityInput,
  PaymentRewardCouponState,
  PreferentialType,
  ProductVariantCustomFields,
  PromotionType,
  UserCouponState,
  ValidityPeriodType,
  VirtualTargetType,
} from '../generated-shop-types';
import {Condition, ConditionService} from '../promotion/automatic-conditions/common-conditions';
import {ActivityProduct} from '../utils';
import {InterfaceAfterSale} from './abstract-after-sale';
import {CacheService} from './cache.service';
import {CouponService} from './coupon.service';
import {FullDiscountPresentService} from './full-discount-present.service';
import {OrderPromotionResultService} from './order-promotion-result.service';
import {ProductCustomService} from './product-custom.service';
@Injectable()
export class PaymentRewardActivityService {
  private conditionService = new ConditionService();
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private fullDiscountPresentService: FullDiscountPresentService,
    private requestContextService: RequestContextService,
    private orderPromotionResultService: OrderPromotionResultService,
    private couponService: CouponService,
    private customerService: CustomerService,
    private cacheService: CacheService,
    private productCustomService: ProductCustomService,
    private memberService: MemberService,
  ) {}

  interfaceAfterSale: InterfaceAfterSale;

  registerAfterSale(interfaceAfterSale: InterfaceAfterSale) {
    this.interfaceAfterSale = interfaceAfterSale;
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<PaymentRewardActivity>,
    relations?: RelationPaths<PaymentRewardActivity>,
  ) {
    const qb = this.listQueryBuilder.build(PaymentRewardActivity, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<PaymentRewardActivity>,
    relations?: RelationPaths<PaymentRewardActivity>,
  ) {
    const qb = this.listQueryBuilder.build(PaymentRewardActivity, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.coupons`, 'coupons');
    qb.andWhere(`${qb.alias}.id = :id`, {id});
    const items = await qb.getMany();
    if (items.length) {
      return items[0];
    }
    return null;
  }

  async failurePaymentRewardActivity(ctx: RequestContext, id: ID) {
    const paymentRewardActivity = await this.findOne(ctx, id);
    if (!paymentRewardActivity) {
      throw new Error('支付有礼活动不存在');
    }
    if (paymentRewardActivity.status === ActivityStatus.Failure) {
      throw new Error('支付有礼活动已失效');
    }
    await this.connection.getRepository(ctx, PaymentRewardActivity).update(id, {status: ActivityStatus.Failure});
    return this.findOne(ctx, id);
  }
  async softDeletePaymentRewardActivity(ctx: RequestContext, id: ID) {
    const paymentRewardActivity = await this.findOne(ctx, id);
    if (!paymentRewardActivity) {
      throw new Error('支付有礼活动不存在');
    }
    if (
      paymentRewardActivity.status !== ActivityStatus.Failure &&
      paymentRewardActivity.status !== ActivityStatus.HaveEnded
    ) {
      throw new Error('支付有礼活动未失效并且未结束');
    }
    await this.connection.getRepository(ctx, PaymentRewardActivity).update(id, {deletedAt: new Date()});
    return {
      result: DeletionResult.Deleted,
      message: '支付有礼活动删除成功',
    };
  }
  async upsertPaymentRewardActivity(ctx: RequestContext, input: PaymentRewardActivityInput) {
    await this.validate(ctx, input);
    let status = ActivityStatus.Normal;
    if (new Date(input.startTime) > new Date()) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date(input.endTime) < new Date()) {
      status = ActivityStatus.HaveEnded;
    }
    let paymentRewardActivity = new PaymentRewardActivity({
      ...(input as unknown as PaymentRewardActivity),
      status,
      coupons: input?.couponIds?.map(id => ({id: id})) as Coupon[],
    });
    paymentRewardActivity = await this.channelService.assignToCurrentChannel(paymentRewardActivity, ctx);
    paymentRewardActivity = await this.connection.getRepository(ctx, PaymentRewardActivity).save(paymentRewardActivity);
    return this.findOne(ctx, paymentRewardActivity.id);
  }
  async validate(ctx: RequestContext, input: PaymentRewardActivityInput) {
    if (input.startTime >= input.endTime) {
      throw new Error('活动开始时间不能大于等于结束时间');
    }
    if (input.preferentialType === PreferentialType.Satisfy) {
      if (!input.minimum || input.minimum <= 0) {
        throw new Error('最低门槛不能为空');
      }
    }
    if (!input.couponIds || input.couponIds.length === 0) {
      throw new Error('优惠券不能为空');
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      productIds: input.applicableProduct.productIds as ID[],
      applicableType: input.applicableProduct.applicableType,
    };
    await this.fullDiscountPresentService.verifyProductIdsAndPaymentRewardActivityConflict(
      ctx,
      inputProduct,
      input?.id ?? '',
    );
  }

  async paymentSettled(ctx: RequestContext, order: Order) {
    const orderId = order.id;
    const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    if (!promotionResult) {
      return;
    }
    const paymentRewardActivates = await this.findPaymentRewardActivity(ctx);
    if (!paymentRewardActivates) {
      return;
    }
    // 是否有售后 有售后不参与支付有礼活动
    const isAfterSale = await this.interfaceAfterSale.getOrderIsAfterSale(ctx, orderId);
    if (isAfterSale) {
      return;
    }
    const customerMember = await this.memberService.getUserMember(ctx, true, order.customerId as ID);
    const memberPlanId = customerMember?.membershipPlanId;
    let isPayReward = false;
    for (const paymentRewardActivity of paymentRewardActivates) {
      // 判断是否
      const isLimitCheck = await this.conditionService.checkLimit(
        {
          groupIds: paymentRewardActivity.memberPlanIds,
          groupType: paymentRewardActivity.groupType,
          isOpen: paymentRewardActivity.whetherRestrictUsers,
        } as unknown as Condition,
        memberPlanId,
      );
      if (!isLimitCheck) {
        continue;
      }
      const {applicableProduct, preferentialType, minimum} = paymentRewardActivity;
      // 参与计算的商品和订单实际支付价格
      const orderLineIds = this.getOrderLineIdsByApplicableProduct(applicableProduct, order);
      if (orderLineIds.length === 0) {
        continue;
      }
      const orderLineTotal =
        promotionResult.promResult?.orderLinePromResults
          ?.filter(line => {
            // 判断订单行是否在orderLineIds中
            if (!this.lineContainsIds(orderLineIds, line?.orderLineId as ID)) {
              return false;
            }
            const discountDetails = line?.discountDetails ?? [];
            // 判断优惠明细中是否包含不叠加支付优惠活动的优惠
            return discountDetails.every(
              detail =>
                detail?.superimposeType === SuperimposeType.All ||
                detail?.superimposeTypes?.includes(PromotionType.PaymentReward),
            );
          })
          .reduce((total, line) => {
            // 计算总价
            return total + (Number(line?.totalPrice) || 0);
          }, 0) ?? 0;
      if (preferentialType === PreferentialType.Satisfy) {
        // 最低门槛
        if (orderLineTotal < minimum) {
          continue;
        }
      }
      const isPayRewardActivity = await this.addOrderPaymentRewardActivity(ctx, order, paymentRewardActivity);
      if (isPayRewardActivity) {
        isPayReward = true;
      }
    }
    if (isPayReward) {
      await this.connection.getRepository(ctx, OrderPromotionResult).update(
        {
          id: promotionResult.id,
        },
        {
          isPayReward: true,
        },
      );
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    }
  }

  // 添加订单和支付有礼活动,以及优惠券的关联
  async addOrderPaymentRewardActivity(ctx: RequestContext, order: Order, paymentRewardActivity: PaymentRewardActivity) {
    let {coupons} = paymentRewardActivity;
    const {grantType} = paymentRewardActivity;
    const customerId = order.customerId as ID;
    // 获取支付有礼中的优惠券 过滤掉失效,已删除,已过期的优惠券
    coupons = coupons?.filter(coupon => {
      return (
        (coupon?.state === CouponState.Normal || coupon.state === CouponState.NotStarted) && coupon?.deletedAt === null
      );
    });
    const orderPaymentRewardCoupons = [];
    // 根据发放方式发放优惠券
    switch (grantType) {
      case GrantType.Automatic:
        // 自动发放
        for (const coupon of coupons) {
          try {
            const validateState = await this.couponService.validateClaimCoupon(ctx, coupon, customerId, true);
            if (!validateState) {
              continue;
            }
            const userCoupon = await this.couponService.createUserCoupon(
              ctx,
              coupon,
              customerId,
              SourceType.Order,
              order.id,
            );
            orderPaymentRewardCoupons.push({
              orderId: order.id,
              couponId: coupon.id,
              paymentRewardActivityId: paymentRewardActivity.id,
              status: PaymentRewardCouponState.Received,
              userCouponId: userCoupon.id,
            });
          } catch (error) {
            Logger.error(`支付有礼活动自动发放优惠券失败:${error}`);
          }
        }
        break;
      case GrantType.Manual:
        // 手动发放
        for (const coupon of coupons) {
          orderPaymentRewardCoupons.push({
            orderId: order.id,
            couponId: coupon.id,
            paymentRewardActivityId: paymentRewardActivity.id,
          });
        }
        break;
      default:
        break;
    }
    if (orderPaymentRewardCoupons.length > 0) {
      await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).save(orderPaymentRewardCoupons);
      return true;
    }
    return false;
  }

  private getOrderLineIdsByApplicableProduct(applicableProduct: ApplicableProduct, order: Order): ID[] {
    // 过滤掉虚拟商品 (优惠券，会员卡等)
    const orderLines = order.lines.filter(
      line =>
        (line.productVariant.customFields as ProductVariantCustomFields).virtualTargetType !==
          VirtualTargetType.Coupon &&
        (line.productVariant.customFields as ProductVariantCustomFields).virtualTargetType !==
          VirtualTargetType.MemberCard,
    );

    const orderLineIds: ID[] = [];
    switch (applicableProduct.applicableType) {
      case ApplicableType.All:
        orderLines.forEach(line => {
          orderLineIds.push(line.id);
        });
        break;
      case ApplicableType.AvailableGoods: {
        const availableProductIds: ID[] = (applicableProduct.productIds ?? []) as ID[];
        orderLines.forEach(line => {
          if (this.lineContainsIds(availableProductIds, line.productVariant?.productId)) {
            orderLineIds.push(line.id);
          }
        });
        break;
      }
      case ApplicableType.UnusableGoods: {
        const unusableProductIds: ID[] = (applicableProduct.productIds ?? []) as ID[];
        orderLines.forEach(line => {
          if (!this.lineContainsIds(unusableProductIds, line.productVariant?.productId)) {
            orderLineIds.push(line.id);
          }
        });
        break;
      }
      default:
        break;
    }
    return orderLineIds;
  }
  // 验证id是否存在于ID数组中
  lineContainsIds(ids: ID[], singleId: ID): boolean {
    return ids.some(id => idsAreEqual(id, singleId));
  }

  // 获取正在进行中的支付有礼活动
  async findPaymentRewardActivity(ctx: RequestContext, currentTime: Date = new Date()) {
    return this.connection
      .getRepository(ctx, PaymentRewardActivity)
      .createQueryBuilder('paymentRewardActivity')
      .leftJoinAndSelect('paymentRewardActivity.coupons', 'coupons')
      .leftJoinAndSelect('paymentRewardActivity.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('paymentRewardActivity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('paymentRewardActivity.startTime <= :currentTime', {currentTime})
      .andWhere('paymentRewardActivity.endTime > :currentTime', {currentTime})
      .andWhere('paymentRewardActivity.deletedAt IS NULL')
      .getMany();
  }

  async timeoutPaymentRewardActivityAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.timeoutPaymentRewardActivity(ctx);
    }
  }

  async timeoutPaymentRewardActivity(ctx: RequestContext) {
    const currentTime = new Date();
    const notYetStarted = await this.connection
      .getRepository(ctx, PaymentRewardActivity)
      .createQueryBuilder('paymentRewardActivity')
      .andWhere('paymentRewardActivity.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('paymentRewardActivity.startTime <= :currentTime', {currentTime})
      .andWhere('paymentRewardActivity.endTime > :currentTime', {currentTime})
      .andWhere('paymentRewardActivity.deletedAt IS NULL')
      .getMany();
    const notYetStartedIds = notYetStarted.map(item => item.id);
    if (notYetStartedIds?.length > 0) {
      await this.connection
        .getRepository(ctx, PaymentRewardActivity)
        .update({id: In(notYetStartedIds)}, {status: ActivityStatus.Normal});
    }
    const haveEnded = await this.connection
      .getRepository(ctx, PaymentRewardActivity)
      .createQueryBuilder('paymentRewardActivity')
      .andWhere('paymentRewardActivity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('paymentRewardActivity.endTime <= :currentTime', {currentTime})
      .andWhere('paymentRewardActivity.deletedAt IS NULL')
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    if (haveEndedIds?.length > 0) {
      await this.connection
        .getRepository(ctx, PaymentRewardActivity)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
    }
  }

  async orderPaymentRewardCoupons(
    ctx: RequestContext,
    orderId: ID,
    options: ListQueryOptions<OrderPaymentRewardCoupon>,
    relations?: RelationPaths<OrderPaymentRewardCoupon>,
  ) {
    const order = await this.connection.getRepository(ctx, Order).findOne({
      where: {
        id: orderId,
      },
    });
    if (!order) {
      throw new Error('订单不存在');
    }
    const qb = this.listQueryBuilder.build(OrderPaymentRewardCoupon, options, {
      ctx,
      relations,
    });
    qb.andWhere(`${qb.alias}.orderId = :orderId`, {orderId});
    qb.leftJoinAndSelect(`${qb.alias}.coupon`, 'coupon');
    qb.leftJoinAndSelect(`${qb.alias}.userCoupon`, 'userCoupon');
    qb.andWhere(`${qb.alias}.status not in (:...status)`, {
      status: [PaymentRewardCouponState.Locked, PaymentRewardCouponState.Invalid],
    });
    let [items, totalItems] = await qb.getManyAndCount();
    items = items.filter(item => {
      if (!item.userCoupon) return true;
      if (item.userCoupon.state === UserCouponState.NotStarted || item.userCoupon.state === UserCouponState.Unused) {
        return true;
      }
      return false;
    });
    totalItems = items.length;
    return {
      items,
      totalItems,
    };
  }

  async receivePaymentRewardCoupon(ctx: RequestContext, orderPaymentRewardCouponId: ID, customerId?: ID) {
    if (!customerId) {
      if (!ctx.activeUserId) {
        throw new Error('用户未登录');
      }
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId);
      if (!customer) {
        throw new Error('customer not exist');
      }
      customerId = customer.id;
    }
    const orderPaymentRewardCoupon = await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).findOne({
      where: {
        id: orderPaymentRewardCouponId,
      },
      relations: ['coupon', 'order'],
    });
    if (!orderPaymentRewardCoupon) {
      throw new Error('支付有礼优惠券不存在');
    }
    if (orderPaymentRewardCoupon.isClaimed) {
      throw new OperationError('支付有礼优惠券已领取');
    }
    if (orderPaymentRewardCoupon.status !== PaymentRewardCouponState.NotReceived) {
      throw new OperationError('支付有礼优惠券已领取!');
    }
    const {coupon, order} = orderPaymentRewardCoupon;
    if (order.state === 'Cancelled') {
      throw new OperationError('订单已取消');
    }
    const validateState = await this.couponService.validateClaimCoupon(ctx, coupon, customerId);
    if (!validateState) {
      throw new OperationError('优惠券领取失败');
    }
    const userCoupon = await this.couponService.createUserCoupon(ctx, coupon, customerId, SourceType.Order, order.id);
    orderPaymentRewardCoupon.userCouponId = userCoupon.id;
    orderPaymentRewardCoupon.status = PaymentRewardCouponState.Received;
    return this.connection.getRepository(ctx, OrderPaymentRewardCoupon).save(orderPaymentRewardCoupon);
  }
  // 取消退款时返还支付有礼优惠券
  @Transaction()
  async cancelRefundOrderUnlockPaymentRewardCoupon(ctx: RequestContext, orderId: ID) {
    const orderPaymentRewards = await this.connection
      .getRepository(ctx, OrderPaymentRewardCoupon)
      .createQueryBuilder('orderPaymentRewardCoupon')
      .leftJoinAndSelect('orderPaymentRewardCoupon.paymentRewardActivity', 'paymentRewardActivity')
      .leftJoinAndSelect('orderPaymentRewardCoupon.userCoupon', 'userCoupon')
      .andWhere('paymentRewardActivity.isRecovery = :isRecovery', {isRecovery: true})
      .andWhere('orderPaymentRewardCoupon.orderId = :orderId', {orderId})
      .andWhere('orderPaymentRewardCoupon.status = :status', {status: PaymentRewardCouponState.Locked})
      .getMany();
    await this.userCouponUnlock(ctx, orderPaymentRewards);
    // 已经领取的支付有礼优惠券解锁状态为已领取
    const orderPaymentRewardUserCoupon = orderPaymentRewards.filter(item => {
      return item.userCoupon;
    });
    // 未领取的支付有礼优惠券解锁状态为未领取
    const orderPaymentRewardNotUserCoupon = orderPaymentRewards.filter(item => {
      return !item.userCoupon;
    });
    const orderPaymentRewardUserCouponIds = orderPaymentRewardUserCoupon.map(item => item.id);
    const orderPaymentRewardNotUserCouponIds = orderPaymentRewardNotUserCoupon.map(item => item.id);
    if (orderPaymentRewardUserCouponIds.length > 0) {
      await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).update(
        {
          id: In(orderPaymentRewardUserCouponIds),
        },
        {
          status: PaymentRewardCouponState.Received,
        },
      );
    }
    if (orderPaymentRewardNotUserCouponIds.length > 0) {
      await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).update(
        {
          id: In(orderPaymentRewardNotUserCouponIds),
        },
        {
          status: PaymentRewardCouponState.NotReceived,
        },
      );
    }
  }
  // 用户优惠券解锁
  async userCouponUnlock(ctx: RequestContext, orderPaymentRewards: OrderPaymentRewardCoupon[]) {
    const userCoupon = orderPaymentRewards
      .filter(item => {
        if (!item.userCoupon) return false;
        if (
          item.userCoupon.state === UserCouponState.Lock &&
          item.userCoupon.useAt === null &&
          (!item.userCoupon.orderIds || item.userCoupon.orderIds.length === 0)
        ) {
          return true;
        }
      })
      .map(item => item.userCoupon)
      .filter(Boolean) as UserCoupon[];
    return this.userCouponUnlockExecute(ctx, userCoupon);
  }

  async userCouponUnlockExecute(ctx: RequestContext, userCoupon: UserCoupon[]) {
    const notStartedCouponId = [];
    const unused = [];
    const haveUsed = [];
    const now = Date.now();
    for (const item of userCoupon) {
      const validFromAt = new Date(item.validFromAt).getTime();
      const maturityAt = item.maturityAt ? new Date(item.maturityAt).getTime() : null;
      if (validFromAt < now) {
        if (item.maturityType === ValidityPeriodType.LongTime || (maturityAt && maturityAt > now)) {
          unused.push(item.id); // 优惠券未过期,可以使用
        } else {
          haveUsed.push(item.id); // 优惠券已过期
        }
      } else {
        notStartedCouponId.push(item.id); // 优惠券尚未开始
      }
    }
    if (notStartedCouponId.length > 0) {
      await this.connection.getRepository(ctx, UserCoupon).update(
        {
          id: In(notStartedCouponId),
        },
        {
          state: UserCouponState.NotStarted,
        },
      );
    }
    if (unused.length > 0) {
      await this.connection.getRepository(ctx, UserCoupon).update(
        {
          id: In(unused),
        },
        {
          state: UserCouponState.Unused,
        },
      );
    }
    if (haveUsed.length > 0) {
      await this.connection.getRepository(ctx, UserCoupon).update(
        {
          id: In(haveUsed),
        },
        {
          state: UserCouponState.HaveUsed,
        },
      );
    }
  }

  // 创建退款直接锁定优惠券
  @Transaction()
  async applyRefundOrderLockPaymentRewardCoupon(ctx: RequestContext, orderId: ID) {
    const orderPaymentRewards = await this.connection
      .getRepository(ctx, OrderPaymentRewardCoupon)
      .createQueryBuilder('orderPaymentRewardCoupon')
      .leftJoinAndSelect('orderPaymentRewardCoupon.paymentRewardActivity', 'paymentRewardActivity')
      .leftJoinAndSelect('orderPaymentRewardCoupon.userCoupon', 'userCoupon')
      .andWhere('paymentRewardActivity.isRecovery = :isRecovery', {isRecovery: true})
      .andWhere('orderPaymentRewardCoupon.status != :status', {status: PaymentRewardCouponState.Invalid})
      .andWhere('orderPaymentRewardCoupon.orderId = :orderId', {orderId})
      .getMany();
    const orderPaymentRewardUserCoupon = orderPaymentRewards.filter(item => {
      return (
        item.userCoupon &&
        (item.userCoupon.state === UserCouponState.NotStarted || item.userCoupon.state === UserCouponState.Unused)
      );
    });
    const userCouponId = orderPaymentRewardUserCoupon.map(item => item.userCoupon.id);
    if (userCouponId.length > 0) {
      // 已经领取的优惠券需要锁定
      await this.connection.getRepository(ctx, UserCoupon).update(
        {
          id: In(userCouponId),
          useAt: IsNull(),
        },
        {
          state: UserCouponState.Lock,
        },
      );
    }
    const orderPaymentRewardCouponIds = orderPaymentRewards.map(item => item.id);
    if (orderPaymentRewardCouponIds.length > 0) {
      // 需要回收的支付有礼优惠券先锁定
      await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).update(
        {
          id: In(orderPaymentRewardCouponIds),
        },
        {
          status: PaymentRewardCouponState.Locked,
        },
      );
    }
  }

  // 产生任何退款都需要退回改订单的全部优惠券
  @Transaction()
  async refundOrderPaymentRewardCoupon(ctx: RequestContext, orderId: ID): Promise<void> {
    const orderPaymentRewardCoupons = await this.connection
      .getRepository(ctx, OrderPaymentRewardCoupon)
      .createQueryBuilder('orderPaymentRewardCoupon')
      .leftJoinAndSelect('orderPaymentRewardCoupon.paymentRewardActivity', 'paymentRewardActivity')
      .andWhere('paymentRewardActivity.isRecovery = :isRecovery', {isRecovery: true})
      .andWhere('orderPaymentRewardCoupon.orderId = :orderId', {orderId})
      .getMany();
    if (orderPaymentRewardCoupons.length === 0) {
      return;
    }
    const userCouponIds = orderPaymentRewardCoupons.map(item => item.userCouponId).filter(Boolean) as ID[];
    if (userCouponIds.length > 0) {
      await this.connection.getRepository(ctx, UserCoupon).update(
        {
          id: In(userCouponIds),
          useAt: IsNull(),
          state: Not(UserCouponState.Expire),
        },
        {
          state: UserCouponState.Expire,
          isReturned: true,
        },
      );
    }
    const orderPaymentRewardCouponIds = orderPaymentRewardCoupons.map(item => item.id);

    if (orderPaymentRewardCouponIds.length > 0) {
      await this.connection.getRepository(ctx, OrderPaymentRewardCoupon).update(
        {
          id: In(orderPaymentRewardCouponIds),
        },
        {
          status: PaymentRewardCouponState.Invalid,
        },
      );
    }
  }
}
