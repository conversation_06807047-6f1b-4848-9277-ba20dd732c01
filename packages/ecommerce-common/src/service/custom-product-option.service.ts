import {Injectable} from '@nestjs/common';
import {MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ID,
  ProductOption,
  ProductOptionGroup,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  TransactionalConnection,
  Translated,
  TranslatorService,
} from '@vendure/core';
import {IsNull} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
@Injectable()
export class CustomProductOptionGroupService {
  constructor(
    private connection: TransactionalConnection,
    private translator: TranslatorService,
    private requestCache: RequestContextCacheService,
    private memoryStorageService: MemoryStorageService,
  ) {}
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, id: ID, relations?: RelationPaths<ProductOptionGroup>) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.productOptionGroup(id, ctx.channelId);
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    id: ID,
    relations?: RelationPaths<ProductOptionGroup>,
  ): Promise<Translated<ProductOptionGroup> | undefined> {
    const memoryStorageCacheKey = CacheKeyManagerService.productOptionGroup(id, ctx.channelId);
    let group: ProductOptionGroup | null | undefined;
    if (ctx.apiType === 'shop') {
      group = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!group) {
      const cache =
        ctx.apiType === 'shop'
          ? {
              id: memoryStorageCacheKey,
              milliseconds: DEFAULT_CACHE_TIMEOUT,
            }
          : false;
      group = await this.connection.getRepository(ctx, ProductOptionGroup).findOne({
        where: {id},
        relations: [], //relations ?? ['options'],
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, group);
      }
    }
    const groupOptionsCacheKey = CacheKeyManagerService.productOptionGroupOption(id as ID, ctx.channelId);
    const groupOptionsCache =
      ctx.apiType === 'shop'
        ? {
            id: groupOptionsCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let groupOptions: ProductOption[] | undefined = [];
    if (ctx.apiType === 'shop') {
      groupOptions = this.memoryStorageService.get(groupOptionsCacheKey);
    }
    if (!groupOptions || groupOptions.length <= 0) {
      groupOptions = await this.connection.getRepository(ctx, ProductOption).find({
        where: {groupId: id, deletedAt: IsNull()},
        cache: groupOptionsCache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(groupOptionsCacheKey, groupOptions);
      }
    }
    if (group) {
      group.options = groupOptions;
    }
    return (group && this.translator.translate(group, ctx, ['options'])) ?? undefined;
  }
}
