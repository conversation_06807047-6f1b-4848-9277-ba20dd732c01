import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {cacheableAccess, MemoryStorageService, CacheKeyManagerService} from '@scmally/kvs';
import {MemberService, MembershipPlan} from '@scmally/member';
import {ProductListOptions} from '@vendure/common/lib/generated-types';
import {
  Asset,
  ChannelAware,
  ChannelService,
  Collection,
  ConfigService,
  EntityWithAssets,
  FacetValue,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  PaginatedList,
  Product,
  ProductOption,
  ProductOptionGroup,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  StockLevel,
  TransactionalConnection,
  Translated,
  TranslatorService,
  Type,
} from '@vendure/core';
import {FindOneOptions, In, IsNull} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT, PRODUCT_TOTAL_STOCK_CACHE_TIMEOUT} from '../consts';
import {ProductPurchasePermission} from '../entities';
import {OperationError} from '../error.type';
import {ProductPurchasePermissionInput} from '../generated-admin-types';
import {MembershipPlanState, PromotionType, PutOnSaleType, VirtualTargetType} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
import {InterfaceCommonCustomer} from './interface-customer';
@Injectable()
export class CustomerProductService {
  constructor(
    private listQueryBuilder: ListQueryBuilder,
    private translator: TranslatorService,
    private connection: TransactionalConnection,
    private memberService: MemberService,
    private channelService: ChannelService,
    @Inject(forwardRef(() => CustomerProductVariantService))
    private customerProductVariantService: CustomerProductVariantService,
    private cacheService: CacheService,
    private requestCache: RequestContextCacheService,
    private memoryStorageService: MemoryStorageService,
    private configService: ConfigService,
  ) {}

  public interfaceCustomer: InterfaceCommonCustomer;

  registerCustomer(interfaceCustomer: InterfaceCommonCustomer) {
    this.interfaceCustomer = interfaceCustomer;
  }

  async activeProducts(
    ctx: RequestContext,
    promotionType: PromotionType,
    isExchange = false,
    options?: ListQueryOptions<Product>,
    relations?: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    const effectiveRelations = relations || this.relations;
    const customPropertyMap: {[name: string]: string} = {};
    const hasFacetValueIdFilter = !!(options as ProductListOptions)?.filter?.facetValueId;
    if (hasFacetValueIdFilter) {
      effectiveRelations.push('facetValues');
      customPropertyMap.facetValueId = 'facetValues.id';
    }
    const qb = this.listQueryBuilder.build(Product, options, {
      relations: effectiveRelations,
      channelId: ctx.channelId,
      where: {deletedAt: IsNull()},
      ctx,
      customPropertyMap,
    });
    if (promotionType !== PromotionType.Coupon || isExchange) {
      qb.andWhere('product.customFieldsVirtualtargettype NOT IN (:...virtualTargetType)', {
        virtualTargetType: [VirtualTargetType.MemberCard, VirtualTargetType.Coupon],
      });
    } else {
      qb.andWhere('product.customFieldsVirtualtargettype != :virtualTargetType', {
        virtualTargetType: VirtualTargetType.Coupon,
      });
    }
    // qb.andWhere('product.customFieldsHidden = :hidden', {hidden: false});
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items: items.map(product => this.translator.translate(product, ctx, ['facetValues', ['facetValues', 'facet']])),
      totalItems,
    };
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, product: Product) => {
      return CacheKeyManagerService.productTotalStock(product.id, ctx.channelId);
    },
  })
  async getProductTotalStock(ctx: RequestContext, product: Product) {
    const memoryStorageCacheKey = CacheKeyManagerService.productTotalStock(product.id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData.stockOnHand - cacheData.stockAllocated;
      }
    }
    const {stockLocationStrategy} = this.configService.catalogOptions;
    let variants = product.variants;
    if (!variants || variants.length <= 0) {
      const variantList = await this.customerProductVariantService.getVariantsByProductId(ctx, product.id);
      variants = variantList.items;
    }
    const variantIds = variants.map(v => v.id);
    if (variantIds.length <= 0) {
      return 0;
    }
    const cache =
      ctx.apiType === 'shop' ? {id: memoryStorageCacheKey, milliseconds: PRODUCT_TOTAL_STOCK_CACHE_TIMEOUT} : false;
    const stockLevels = await this.connection.getRepository(ctx, StockLevel).find({
      where: {
        productVariantId: In(variantIds),
      },
      cache: cache,
    });
    const {stockOnHand, stockAllocated} = await stockLocationStrategy.getAvailableStock(ctx, '', stockLevels);
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {
        stockOnHand: stockOnHand,
        stockAllocated: stockAllocated,
      });
    }
    return stockOnHand - stockAllocated;
  }

  // 根据查询的字段添加关联
  addProductRelations(relations: RelationPaths<Product>): RelationPaths<Product> {
    // if (relations.includes('variants')) {
    //   relations = relations.concat([
    //     'variants',
    //     'variants.assets',
    //     'variants.assets.asset',
    //     'variants.assets.asset.channels',
    //     'variants.productVariantPrices',
    //     'variants.taxCategory',
    //     'variants.options',
    //     'variants.stockLevels',
    //     'variants.stockMovements',
    //   ]);
    // }
    // if (relations.includes('facetValues')) {
    //   relations.push('facetValues.channels');
    // }
    // relations = relations.concat(['optionGroups', 'optionGroups.options']);
    // relations = relations.concat(['variants', 'variants.collections', 'variants.collections.translations']);
    // relations = relations.concat(['assets', 'assets.asset', 'assets.asset.channels']);
    // 优化后
    // if (relations.includes('variants')) {
    //   relations = relations.concat([
    //     'variants',
    //     'variants.options',
    //     'variants.options.group',
    //     'variants.featuredAsset',
    //     'variants.stockLevels',
    //     'variants.productVariantPrices',
    //     'variants.taxCategory',
    //   ]);
    // }
    relations = relations.concat([
      'optionGroups',
      'optionGroups.options',
      'featuredAsset',
      'assets',
      'assets.asset',
      'assets.asset.channels',
    ]);
    // 去重
    return Array.from(new Set(relations));
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<Product>,
    relations?: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    const effectiveRelations = this.addProductRelations(relations || []);
    const customPropertyMap: {[name: string]: string} = {};
    const hasFacetValueIdFilter = !!(options as ProductListOptions)?.filter?.facetValueId;
    if (hasFacetValueIdFilter) {
      effectiveRelations.push('facetValues');
      customPropertyMap.facetValueId = 'facetValues.id';
    }
    const qb = this.listQueryBuilder.build(Product, options, {
      relations: effectiveRelations,
      channelId: ctx.channelId,
      where: {deletedAt: IsNull()},
      ctx,
      customPropertyMap,
    });
    qb.leftJoin(`${qb.alias}.variants`, 'variants');
    qb.andWhere('variants.deletedAt IS NULL');
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items: items.map(product => this.translator.translate(product, ctx, ['facetValues', ['facetValues', 'facet']])),
      totalItems,
    };
  }

  private readonly relations = ['featuredAsset', 'assets', 'channels', 'facetValues', 'facetValues.facet'];

  async productsSortByProductIds(
    ctx: RequestContext,
    options?: ListQueryOptions<Product>,
    relations?: RelationPaths<Product>,
  ): Promise<PaginatedList<Translated<Product>>> {
    const effectiveRelations = relations || this.relations;
    const customPropertyMap: {[name: string]: string} = {};
    const hasFacetValueIdFilter = !!(options as ProductListOptions)?.filter?.facetValueId;
    if (hasFacetValueIdFilter) {
      effectiveRelations.push('facetValues');
      customPropertyMap.facetValueId = 'facetValues.id';
    }
    const qb = this.listQueryBuilder.build(Product, options, {
      relations: effectiveRelations,
      channelId: ctx.channelId,
      where: {deletedAt: IsNull()},
      ctx,
      customPropertyMap,
    });
    qb.leftJoin(`${qb.alias}.variants`, 'variants');
    qb.andWhere('variants.deletedAt IS NULL');
    const ids = options?.filter?.id?.in || [];
    if (ids.length) {
      const idsString = ids.join(',');
      qb.addSelect(`FIELD(${qb.alias}.id, ${idsString})`, 'fieldOrder').orderBy('fieldOrder', 'ASC');
    }
    return qb.getManyAndCount().then(async ([products, totalItems]) => {
      const items = products.map(product =>
        this.translator.translate(product, ctx, ['facetValues', ['facetValues', 'facet']]),
      );
      return {
        items,
        totalItems,
      };
    });
  }

  async upsertProductPurchase(ctx: RequestContext, input: ProductPurchasePermissionInput) {
    const {productId, isMembershipPlanPurchase, membershipPlanIds, guideMembershipPlanId} = input;
    if (!productId) {
      throw new Error('产品ID不能为空');
    }
    let membershipPlans: MembershipPlan[] = [];
    if (isMembershipPlanPurchase) {
      if (!membershipPlanIds?.length) {
        throw new Error('会员计划ID不能为空');
      }
      membershipPlans = await this.connection
        .getRepository(ctx, MembershipPlan)
        .createQueryBuilder('membershipPlan')
        .leftJoin('membershipPlan.channels', 'channel')
        .where('membershipPlan.deletedAt IS NULL')
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
        .andWhere({id: In(membershipPlanIds)})
        .getMany();
      if (membershipPlans.length !== membershipPlanIds.length) {
        throw new Error('会员卡ID有误');
      }
      // 检查会员卡状态是否正常
      const IsMembershipPlanStateNormal = membershipPlans.every(m => m.state === MembershipPlanState.Shelf);
      if (!IsMembershipPlanStateNormal) {
        throw new Error('会员卡状态有误');
      }
      if (guideMembershipPlanId) {
        const guideMembershipPlan = await this.connection.getRepository(ctx, MembershipPlan).findOne({
          where: {
            id: guideMembershipPlanId,
          },
        });
        if (!guideMembershipPlan) {
          throw new Error('引导会员卡ID有误');
        }
        if (guideMembershipPlan.state !== MembershipPlanState.Shelf) {
          throw new Error('引导会员卡状态有误');
        }
      }
    }
    const productPurchasePermission = await this.connection.getRepository(ctx, ProductPurchasePermission).findOne({
      where: {
        productId: productId,
      },
    });

    if (productPurchasePermission) {
      productPurchasePermission.guideMembershipPlanId = guideMembershipPlanId as ID;
      productPurchasePermission.isMembershipPlanPurchase = isMembershipPlanPurchase;
      productPurchasePermission.membershipPlans = membershipPlans;
      return this.connection.getRepository(ctx, ProductPurchasePermission).save(productPurchasePermission);
    } else {
      const newProductPurchasePermission = new ProductPurchasePermission({
        productId,
        isMembershipPlanPurchase,
        membershipPlans: membershipPlans,
        guideMembershipPlanId,
      });
      return this.connection.getRepository(ctx, ProductPurchasePermission).save(newProductPurchasePermission);
    }
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID, isShowDisable = false) => {
      if (ctx.apiType === 'shop') {
        if (isShowDisable) {
          return CacheKeyManagerService.productPurchasePermissionShowDisable(productId, ctx.channelId);
        } else {
          return CacheKeyManagerService.productPurchasePermission(productId, ctx.channelId);
        }
      }
      return '';
    },
  })
  async getProductPurchasePermission(ctx: RequestContext, productId: ID, isShowDisable = false) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      if (isShowDisable) {
        memoryStorageCacheKey = CacheKeyManagerService.productPurchasePermissionShowDisable(productId, ctx.channelId);
      } else {
        memoryStorageCacheKey = CacheKeyManagerService.productPurchasePermission(productId, ctx.channelId);
      }
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData.productPurchasePermission as ProductPurchasePermission;
      }
    }
    const qb = this.connection
      .getRepository(ctx, ProductPurchasePermission)
      .createQueryBuilder('productPurchasePermission')

      .andWhere('productPurchasePermission.productId = :productId', {productId});
    if (!isShowDisable) {
      qb.leftJoinAndSelect(
        'productPurchasePermission.membershipPlans',
        'membershipPlans',
        'membershipPlans.deletedAt IS NULL AND membershipPlans.state != :state',
      ).leftJoinAndSelect(
        'productPurchasePermission.guideMembershipPlan',
        'guideMembershipPlan',
        'guideMembershipPlan.deletedAt IS NULL AND guideMembershipPlan.state != :state',
      );
      qb.setParameter('state', MembershipPlanState.Disable);
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    } else {
      qb.leftJoinAndSelect(
        'productPurchasePermission.membershipPlans',
        'membershipPlans',
        'membershipPlans.deletedAt IS NULL',
      ).leftJoinAndSelect(
        'productPurchasePermission.guideMembershipPlan',
        'guideMembershipPlan',
        'guideMembershipPlan.deletedAt IS NULL',
      );
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
    }
    const productPurchasePermissions = await qb.getMany();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {productPurchasePermission: productPurchasePermissions[0]});
    }
    if (!productPurchasePermissions?.length) {
      return null;
    }
    return productPurchasePermissions[0];
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, id: ID) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.optionGroups(id, ctx.channelId);
      }
      return '';
    },
  })
  async getOptionGroupsByProductId(ctx: RequestContext, id: ID): Promise<Array<Translated<ProductOptionGroup>>> {
    const cache =
      ctx.apiType === 'shop'
        ? {id: CacheKeyManagerService.optionGroups(id, ctx.channelId), milliseconds: DEFAULT_CACHE_TIMEOUT}
        : false;
    const optionsCache =
      ctx.apiType === 'shop'
        ? {id: CacheKeyManagerService.optionGroupsOptions(id, ctx.channelId), milliseconds: DEFAULT_CACHE_TIMEOUT}
        : false;
    let groups: ProductOptionGroup[] = [];
    const memoryStorageCacheKey = CacheKeyManagerService.optionGroups(id, ctx.channelId);
    let isCacheGroup = false;
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        groups = cacheData.groups;
        isCacheGroup = true;
      }
    }
    if (!isCacheGroup) {
      const qb = this.connection.getRepository(ctx, ProductOptionGroup);
      groups = await qb.find({
        relations: [], //['options'],
        where: {
          product: {id},
        },
        order: {
          id: 'ASC',
        },
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, {groups: groups});
      }
    }
    const groupIds = groups.map(g => g.id);
    let options: ProductOption[] = [];
    let isCacheOptions = false;
    const productOptionMemoryStorageCacheKey = CacheKeyManagerService.optionGroupsOptions(id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(productOptionMemoryStorageCacheKey);
      if (cacheData) {
        options = cacheData.options;
        isCacheOptions = true;
      }
    }
    if (!isCacheOptions) {
      options = await this.connection.getRepository(ctx, ProductOption).find({
        where: {
          groupId: In(groupIds),
        },
        cache: optionsCache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(productOptionMemoryStorageCacheKey, {options: options});
      }
    }
    groups?.forEach(group => {
      group.options = options?.filter(o => idsAreEqual(o.groupId, group.id));
    });
    return groups?.map(group => this.translator.translate(group, ctx, ['options'])) as Array<
      Translated<ProductOptionGroup>
    >;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: string) => {
      return CacheKeyManagerService.collection(productId, ctx.channelId);
    },
  })
  async getCollectionsByProductId(
    ctx: RequestContext,
    productId: ID,
    publicOnly: boolean,
  ): Promise<Array<Translated<Collection>>> {
    let result: Collection[] | undefined;
    const memoryStorageCacheKey = CacheKeyManagerService.collection(productId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      result = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!result) {
      const qb = this.connection
        .getRepository(ctx, Collection)
        .createQueryBuilder('collection')
        .leftJoinAndSelect('collection.translations', 'translation')
        .leftJoin('collection.productVariants', 'variant')
        .where('variant.product = :productId', {productId})
        .groupBy('collection.id, translation.id')
        .orderBy('collection.id', 'ASC');

      if (publicOnly) {
        qb.andWhere('collection.isPrivate = :isPrivate', {isPrivate: false});
      }
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      result = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, result);
      }
    }

    return result.map(collection => this.translator.translate(collection, ctx));
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: string) => {
      return CacheKeyManagerService.facetValues(productId, ctx.channelId);
    },
  })
  async getFacetValuesForProduct(ctx: RequestContext, productId: ID): Promise<Array<Translated<FacetValue>>> {
    let result: Product | undefined | null;
    const memoryStorageCacheKey = CacheKeyManagerService.facetValues(productId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      result = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!result) {
      const cache = ctx.apiType === 'shop' ? {id: memoryStorageCacheKey, milliseconds: DEFAULT_CACHE_TIMEOUT} : false;
      result = await this.connection.getRepository(ctx, Product).findOne({
        where: {id: productId},
        relations: ['facetValues'],
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, result);
      }
    }
    return !result ? [] : result.facetValues.map(o => this.translator.translate(o, ctx, ['facet']));
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, entity: Omit<EntityWithAssets, 'assets'>, cacheKey: string) => {
      return CacheKeyManagerService.entityFeaturedAsset(cacheKey, entity.id, ctx.channelId);
    },
  })
  async getFeaturedAsset<T extends Omit<EntityWithAssets, 'assets'>>(
    ctx: RequestContext,
    entity: T,
    cacheKey: string,
  ): Promise<Asset | undefined> {
    const entityType: Type<T> = Object.getPrototypeOf(entity).constructor;
    const memoryStorageCacheKey = CacheKeyManagerService.entityFeaturedAsset(cacheKey, entity.id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData.featuredAsset;
      }
    }
    let entityWithFeaturedAsset: T | undefined;
    const cache = ctx.apiType === 'shop' ? {id: memoryStorageCacheKey, milliseconds: DEFAULT_CACHE_TIMEOUT} : false;
    if (this.channelService.isChannelAware(entity)) {
      entityWithFeaturedAsset = await this.connection.findOneInChannel(
        ctx,
        entityType as Type<T & ChannelAware>,
        entity.id,
        ctx.channelId,
        {
          relations: ['featuredAsset'],
          cache: cache,
        },
      );
    } else {
      entityWithFeaturedAsset = await this.connection
        .getRepository(ctx, entityType)
        .findOne({
          where: {id: entity.id},
          relations: {
            featuredAsset: true,
          },
          cache: cache,
          // TODO: satisfies
        } as FindOneOptions<T>)
        .then(result => result ?? undefined);
    }
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, {featuredAsset: entityWithFeaturedAsset?.featuredAsset});
    }
    return entityWithFeaturedAsset?.featuredAsset || undefined;
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, entity: Omit<EntityWithAssets, 'assets'>, cacheKey: string) => {
      return CacheKeyManagerService.entityAssets(cacheKey, entity.id, ctx.channelId);
    },
  })
  async getEntityAssets<T extends EntityWithAssets>(
    ctx: RequestContext,
    entity: T,
    cacheKey: string,
  ): Promise<Asset[] | undefined> {
    let orderableAssets = entity.assets;
    if (!orderableAssets) {
      const entityType: Type<EntityWithAssets> = Object.getPrototypeOf(entity).constructor;
      let entityWithAssets: EntityWithAssets | undefined | null;
      const memoryStorageCacheKey = CacheKeyManagerService.entityAssets(cacheKey, entity.id, ctx.channelId);
      if (ctx.apiType === 'shop') {
        entityWithAssets = this.memoryStorageService.get(memoryStorageCacheKey);
      }
      if (!entityWithAssets) {
        const qb = this.connection
          .getRepository(ctx, entityType)
          .createQueryBuilder('entity')
          .leftJoinAndSelect('entity.assets', 'orderable_asset')
          .leftJoinAndSelect('orderable_asset.asset', 'asset')
          .leftJoinAndSelect('asset.channels', 'asset_channel')
          .where('entity.id = :id', {id: entity.id})
          .andWhere('asset_channel.id = :channelId', {channelId: ctx.channelId});
        if (ctx.apiType === 'shop') {
          qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
        }
        entityWithAssets = await qb.take(1).getOne();
        if (ctx.apiType === 'shop') {
          this.memoryStorageService.set(memoryStorageCacheKey, entityWithAssets);
        }
      }
      orderableAssets = entityWithAssets?.assets ?? [];
    } else if (0 < orderableAssets.length) {
      // the Assets are already loaded, but we need to limit them by Channel
      if (orderableAssets[0].asset?.channels) {
        orderableAssets = orderableAssets.filter(
          a => !!a.asset.channels.map(c => c.id).find(id => idsAreEqual(id, ctx.channelId)),
        );
      } else {
        const qb = this.connection
          .getRepository(ctx, Asset)
          .createQueryBuilder('asset')
          .leftJoinAndSelect('asset.channels', 'asset_channel')
          .where('asset.id IN (:...ids)', {ids: orderableAssets.map(a => a.assetId)})
          .andWhere('asset_channel.id = :channelId', {channelId: ctx.channelId});
        if (ctx.apiType === 'shop') {
          qb.cache(CacheKeyManagerService.entityAssets(cacheKey, entity.id, ctx.channelId), DEFAULT_CACHE_TIMEOUT);
        }
        const assetsInChannel = await qb.getMany();

        orderableAssets = orderableAssets.filter(oa => !!assetsInChannel.find(a => idsAreEqual(a.id, oa.assetId)));
      }
    } else {
      orderableAssets = [];
    }
    return orderableAssets.sort((a, b) => a.position - b.position).map(a => a.asset);
  }

  async checkProductVariantPurchasePermission(ctx: RequestContext, variantId: ID, isSkip = false, customerId?: ID) {
    const variant = await this.customerProductVariantService.getVariantByVariantId(ctx, variantId);
    if (!variant) {
      return false;
    }
    return this.checkProductPurchasePermission(ctx, variant.product.id, isSkip, customerId);
  }

  async checkProductPurchasePermission(ctx: RequestContext, productId: ID, isSkip = false, customerId?: ID) {
    const productPurchasePermission = await this.getProductPurchasePermission(ctx, productId);
    if (!productPurchasePermission?.isMembershipPlanPurchase) {
      return true;
    }
    const membershipPlanIds = productPurchasePermission.membershipPlans.map(m => m.id);
    if (!customerId) {
      const userId = ctx.activeUserId;
      if (!userId) {
        if (!isSkip) {
          throw new OperationError('用户未登录');
        }
        return false;
      }
      const customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        if (!isSkip) {
          throw new OperationError('用户未找到');
        }
        return false;
      }
      customerId = customer.id;
    }
    const member = await this.memberService.getUserMember(ctx, true, customerId);

    if (!member) {
      if (!isSkip) {
        throw new OperationError('您不是会员, 请先购买会员卡!');
      }
      return false;
    }
    const membershipPlanId = member.membershipPlanId;
    const isAllowPurchase = membershipPlanIds.filter(id => idsAreEqual(membershipPlanId, id)).length > 0;
    if (!isAllowPurchase) {
      if (!isSkip) {
        throw new OperationError('您的会员卡无法购买此商品');
      }
      return false;
    }
    return true;
  }

  async batchUpdateProductEnabled(ctx: RequestContext, ids: ID[], enabled: boolean) {
    if (!ids?.length) {
      throw new Error('产品ID不能为空');
    }
    if (enabled) {
      await this.connection.getRepository(ctx, Product).update(
        {id: In(ids)},
        {
          enabled: true,
          customFields: {
            putOnSaleType: PutOnSaleType.Immediate,
          },
        },
      );
    } else {
      await this.connection.getRepository(ctx, Product).update(
        {id: In(ids)},
        {
          enabled: false,
          customFields: {
            putOnSaleType: PutOnSaleType.Manual,
          },
        },
      );
    }
    for (const id of ids) {
      await this.cacheService.clearProductCache(ctx, id);
    }
    return this.connection.getRepository(ctx, Product).find({
      where: {id: In(ids)},
    });
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, productId: ID) => {
      return CacheKeyManagerService.productFindOne(productId, ctx.channelId);
    },
  })
  async findOne(
    ctx: RequestContext,
    productId: ID,
    relations?: RelationPaths<Product>,
  ): Promise<Translated<Product> | undefined> {
    const memoryStorageCacheKey = CacheKeyManagerService.productFindOne(productId, ctx.channelId);
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    let product: Product | undefined;
    if (ctx.apiType === 'shop') {
      product = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!product) {
      product = await this.connection.findOneInChannel(ctx, Product, productId, ctx.channelId, {
        relations: [],
        where: {
          deletedAt: IsNull(),
        },
        cache: cache,
      });
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, product);
      }
    }
    if (!product) {
      return;
    }
    return this.translator.translate(product, ctx, ['facetValues', ['facetValues', 'facet']]);
  }
}
