import {Injectable} from '@nestjs/common';
import {cacheableAccess, KvsService, MemoryStorageService, CacheKeyManagerService} from '@scmally/kvs';
import {ChannelService, RequestContext, TransactionalConnection} from '@vendure/core';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PointsConfig} from '../entities';
import {PointsConfigInput} from '../generated-admin-types';
import {CacheService} from './cache.service';
import {PointsGrantTiming, ValidityPeriod} from '../generated-shop-types';
@Injectable()
export class PointsConfigService {
  constructor(
    private connection: TransactionalConnection,
    private channelService: ChannelService,
    private memoryStorageService: MemoryStorageService,
    private cacheService: CacheService,
    private kvsService: KvsService,
  ) {}

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext) => {
      return CacheKeyManagerService.pointsConfig(ctx.channelId);
    },
  })
  async findOne(ctx: RequestContext): Promise<PointsConfig> {
    const memoryKey = CacheKeyManagerService.pointsConfig(ctx.channelId);
    if (ctx.apiType === 'shop') {
      const memoryData = this.memoryStorageService.get(memoryKey);
      if (memoryData) {
        return memoryData.pointsConfig;
      }
    }
    const qb = this.connection.getRepository(ctx, PointsConfig).createQueryBuilder('pointsConfig');
    if (ctx.apiType === 'shop') {
      qb.cache(memoryKey, DEFAULT_CACHE_TIMEOUT);
    }
    qb.leftJoinAndSelect(`${qb.alias}.channels`, 'channel');
    qb.andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    const pointsConfig = (await qb.getOne()) as PointsConfig;
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryKey, {pointsConfig: pointsConfig});
    }
    return pointsConfig;
  }

  async upsertPointsConfig(ctx: RequestContext, input: PointsConfigInput) {
    if ((input.pointsExchangeRate ?? 0) <= 0) {
      throw new Error('积分兑换比例不能小于等于0');
    }
    let pointsConfig = await this.findOne(ctx);
    if (pointsConfig) {
      // 是否修改了积分兑换比例
      const isChangeExchangeRate = pointsConfig.pointsExchangeRate !== input.pointsExchangeRate;
      pointsConfig.validityPeriod = input.validityPeriod as ValidityPeriod;
      pointsConfig.pointsGrantTiming = input.pointsGrantTiming as PointsGrantTiming;
      pointsConfig.pointsExchangeGrant = input.pointsExchangeGrant ?? false;
      pointsConfig.pointsRefundGrant = input.pointsRefundGrant ?? false;
      pointsConfig.pointsExchangeRate = input.pointsExchangeRate ?? 50;
      pointsConfig.pointsRuleText = input.pointsRuleText ?? '';
      await this.connection.getRepository(ctx, PointsConfig).save(pointsConfig);
      if (isChangeExchangeRate) {
        await this.kvsService.pointsConfigUpdateTime.set(String(ctx.channelId), new Date());
      }
    } else {
      pointsConfig = new PointsConfig({
        validityPeriod: input.validityPeriod,
        pointsGrantTiming: input.pointsGrantTiming,
        pointsExchangeGrant: input.pointsExchangeGrant,
        pointsRefundGrant: input.pointsRefundGrant,
        pointsExchangeRate: input.pointsExchangeRate,
        pointsRuleText: input.pointsRuleText ?? '',
      });
      pointsConfig = await this.channelService.assignToCurrentChannel(pointsConfig, ctx);
      await this.connection.getRepository(ctx, PointsConfig).save(pointsConfig);
      await this.kvsService.pointsConfigUpdateTime.set(String(ctx.channelId), new Date());
    }
    await this.cacheService.removeCache(CacheKeyManagerService.pointsConfig(ctx.channelId));
    return pointsConfig;
  }
}
