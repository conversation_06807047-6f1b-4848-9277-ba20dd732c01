import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {MemberService} from '@scmally/member';
import {RedLockService} from '@scmally/red-lock';
import {AbstractOrderPromotionResult, WeChatPaymentService} from '@scmally/wechat';
import {ShippingMethodQuote} from '@vendure/common/lib/generated-shop-types';
import {AddFulfillmentToOrderResult, FulfillOrderInput, HistoryEntryType} from '@vendure/common/lib/generated-types';
import {omit} from '@vendure/common/lib/omit';
import {summate} from '@vendure/common/lib/shared-utils';
import {
  assertFound,
  ChannelService,
  ConfigService,
  Customer,
  CustomFieldRelationService,
  EmptyOrderLineSelectionError,
  EntityNotFoundError,
  ErrorResultUnion,
  EventBus,
  Fulfillment,
  FulfillmentLine,
  FulfillmentService,
  getOrdersFromLines,
  HistoryService,
  ID,
  idsAreEqual,
  IneligibleShippingMethodError,
  InsufficientStockOnHandError,
  isGraphQlErrorResult,
  ItemsAlreadyFulfilledError,
  Logger,
  NegativeQuantityError,
  Order,
  OrderLimitError,
  OrderLine,
  OrderLineEvent,
  OrderModificationError,
  OrderModifier,
  OrderService,
  ProductService,
  ProductVariant,
  ProductVariantService,
  RelationPaths,
  RequestContext,
  ShippingLine,
  StockLevelService,
  Transaction,
  TransactionalConnection,
  TranslatorService,
  UnauthorizedError,
  UserInputError,
} from '@vendure/core';
import {grossPriceOf, netPriceOf} from '@vendure/core/dist/common/tax-utils';
import {ShippingCalculator} from '@vendure/core/dist/service/helpers/shipping-calculator/shipping-calculator';
import NP from 'number-precision';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {
  BlindBoxBuy,
  BlindBoxOrderRecord,
  BlindBoxRefundRecord,
  MemberPromotionRecord,
  MerchantVoluntaryRefund,
  OrderLinePromotionDetail,
  OrderPromotionResult,
  PromotionResultDetail,
  PurchasePremium,
  UserCoupon,
} from '../entities';
import {OperationError} from '../error.type';
import {
  OrderLineCustomFields,
  OrderPromotionResultInput,
  PromLineResult,
  PromResult,
  ShippingFeeType,
  UserCouponState,
} from '../generated-admin-types';
import {
  GiftItem,
  GiftType,
  LimitType,
  OrderCustomFields,
  ProductCustomFields,
  PromotionType,
  PurchasePattern,
  PutOnSaleType,
  SetOrderShippingMethodResult,
  SourceType,
} from '../generated-shop-types';
import {AutomaticActionService} from '../promotion/automatic.action.service';
import {InterfaceAfterSale} from './abstract-after-sale';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {OrderCustomCommonService} from './custom-order-common.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
import {CustomerProductService} from './custom-product.service';
import {FreeGiftService} from './free-gift.service';
import {PointsProductService} from './points-product.service';
@Injectable()
export class OrderPromotionResultService extends AbstractOrderPromotionResult {
  public interfaceAfterSale: InterfaceAfterSale;
  constructor(
    private connection: TransactionalConnection,
    private orderService: OrderService,
    @Inject(forwardRef(() => FreeGiftService))
    private freeGiftService: FreeGiftService,
    private memberService: MemberService,
    private orderModifier: OrderModifier,
    private eventBus: EventBus,
    private configService: ConfigService,
    private customFieldRelationService: CustomFieldRelationService,
    private kvsService: KvsService,
    @Inject(forwardRef(() => OrderCustomCommonService))
    private orderCustomCommonService: OrderCustomCommonService,
    private productService: ProductService,
    @Inject(forwardRef(() => CommonService))
    private commonService: CommonService,
    private channelService: ChannelService,
    private cacheService: CacheService,
    private customProductService: CustomerProductService,
    private customerProductVariantService: CustomerProductVariantService,
    private memoryStorageService: MemoryStorageService,
    private automaticActionService: AutomaticActionService,
    private shippingCalculator: ShippingCalculator,
    private pointsProductService: PointsProductService,
    private fulfillmentService: FulfillmentService,
    private stockLevelService: StockLevelService,
    private historyService: HistoryService,
    private productVariantService: ProductVariantService,
    private translator: TranslatorService,
    private redLockService: RedLockService,
    public weChatPaymentService: WeChatPaymentService,
  ) {
    super(weChatPaymentService);
  }

  async blindBoxRefundableAmount(ctx: RequestContext, orderLineIds: ID[]) {
    // 获取订单项对应的盲盒购买记录
    const blindBoxOrderRecords = await this.connection.getRepository(ctx, BlindBoxOrderRecord).find({
      where: {orderLineId: In(orderLineIds)},
      relations: ['blindBoxBuy'],
    });
    if (blindBoxOrderRecords.length === 0) {
      return 0;
    }
    const blindBoxBuyIds = blindBoxOrderRecords.map(blindBoxOrderRecord => blindBoxOrderRecord.blindBoxBuyId);
    // 获取订单项对应的盲盒购买记录的退款记录
    const refundAmounts = await this.connection.getRepository(ctx, BlindBoxRefundRecord).find({
      where: {blindBoxBuyId: In(blindBoxBuyIds)},
    });
    if (refundAmounts.length === blindBoxOrderRecords.length) {
      return 0;
    }
    //过滤已经退款的盲盒购买记录id
    const refundBlindBoxBuyIds = refundAmounts?.map(refundAmount => refundAmount.blindBoxBuyId) ?? [];
    const blindBoxBuyIdsWithoutRefund =
      blindBoxBuyIds.filter(blindBoxBuyId => !refundBlindBoxBuyIds.includes(blindBoxBuyId)) ?? [];
    // 获取订单项对应的盲盒购买记录的价格
    const blindBoxOrderRecordsWithoutRefund =
      blindBoxOrderRecords.filter(blindBoxOrderRecord =>
        blindBoxBuyIdsWithoutRefund.includes(blindBoxOrderRecord.blindBoxBuyId),
      ) ?? [];
    // 剩余未退款的盲盒购买记录的总价格
    const totalAmount = blindBoxOrderRecordsWithoutRefund
      .map(blindBoxOrderRecord => blindBoxOrderRecord.blindBoxBuy?.price || 0)
      .reduce((a, b) => a + b, 0);

    return totalAmount ?? 0;
  }

  async checkOrderLimitation(ctx: RequestContext, order: Order, userId?: ID, isApplyPriceAdjustments = true) {
    if (!userId) {
      userId = ctx.activeUserId;
      if (!userId) {
        throw new UnauthorizedError();
      }
    }
    const customer = await this.orderCustomCommonService.interfaceSubscriptionOrder.getCustomer(ctx);
    if (!customer) {
      throw new UnauthorizedError();
    }
    let isUpdateOrder = false;
    for (const line of order.lines) {
      let variant = line.productVariant;
      if (!variant?.product) {
        variant = await this.customerProductVariantService.getVariantByVariantId(ctx, variant.id);
        if (!variant) {
          order = (await this.removeItemFromOrder(ctx, order.id, line.id, order)) as Order;
          isUpdateOrder = true;
          continue;
        }
      }
      const product = variant.product;
      if (product.enabled === false) {
        order = (await this.removeItemFromOrder(ctx, order.id, line.id, order)) as Order;
        isUpdateOrder = true;
        continue;
      }
      const limitType = (product.customFields as ProductCustomFields).limitType;
      const limitCount = (product.customFields as ProductCustomFields).limitCount;
      if (limitType && limitType !== LimitType.Unlimited) {
        if (limitCount && limitCount !== -1) {
          const purchasedCount = await this.orderCustomCommonService.getPurchasedCountFromRedis(
            ctx,
            order,
            product,
            true,
            customer,
          );
          if (purchasedCount > limitCount) {
            order = (await this.removeItemFromOrder(ctx, order.id, line.id, order)) as Order;
            isUpdateOrder = true;
            continue;
          }
        }
      }
    }
    if (isUpdateOrder) {
      if (!order?.lines || order.lines.length === 0) {
        await this.connection
          .getRepository(ctx, OrderPromotionResult)
          .update({order: {id: order.id}}, {promResult: null});
        await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
      }
      if (isApplyPriceAdjustments) {
        order = await this.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }
  /**
   * 支付完成创建优惠结果明细
   * @param ctx
   * @param order
   */
  @Transaction()
  async createOrderPromotionResultDetail(ctx: RequestContext, order: Order) {
    const orderId = order.id;
    const customer = order.customer;
    const promotionResult = await this.getResultByOrderId(ctx, orderId);
    const promLineResults = promotionResult?.promResult?.promLineResults;
    const orderLinePromResults = promotionResult?.promResult?.orderLinePromResults;
    if (promLineResults) {
      for (const promLineResult of promLineResults) {
        if (!promLineResult) continue;
        const promInstanceId = await this.getPromInstanceId(ctx, promLineResult);
        if (promInstanceId === -1) continue;
        const orderLines = promLineResult?.orderLines;
        let totalPrice = 0;
        let discountAmount = 0; //优惠金额
        let totalQuantity = 0;
        if (orderLines && orderLines.length > 0) {
          const orderLineIds = orderLines.map(orderLine => orderLine?.orderLineId);
          totalPrice =
            orderLinePromResults
              ?.map(orderLineProm => {
                if (orderLineIds.includes(orderLineProm?.orderLineId)) {
                  discountAmount += Math.floor(orderLineProm?.discountAmount || 0);
                  totalQuantity += orderLineProm?.count || 0;
                  return Math.floor(orderLineProm?.totalPrice || 0);
                } else {
                  return 0;
                }
              })
              .reduce((n, m) => n + m) ?? 0;
          let promotionResultDetail = new PromotionResultDetail({
            type: promLineResult.type,
            promotionId: promInstanceId,
            discountAmount: discountAmount,
            quantity: totalQuantity,
            price: totalPrice,
            order: order,
            customer: customer,
          });
          promotionResultDetail = await this.channelService.assignToCurrentChannel(promotionResultDetail, ctx);
          await this.connection.getRepository(ctx, PromotionResultDetail).save(promotionResultDetail);
        }
      }
    }
    if (orderLinePromResults) {
      for (const orderLinePromResult of orderLinePromResults) {
        if (!orderLinePromResult) continue;
        const orderLine = await this.connection.getEntityOrThrow(
          ctx,
          OrderLine,
          orderLinePromResult.orderLineId as ID,
          {
            relations: ['productVariant', 'productVariant.product'],
          },
        );
        let orderLinePromotionDetail = new OrderLinePromotionDetail({
          promotionPrice: Math.floor(orderLinePromResult.discountAmount ?? 0),
          price: Math.floor(orderLinePromResult.totalPrice ?? 0),
          count: orderLinePromResult.count,
          paymentTime: new Date(),
          product: orderLine.productVariant.product,
          orderLine: orderLine,
          order: order,
          customer: customer,
        });
        orderLinePromotionDetail = await this.channelService.assignToCurrentChannel(orderLinePromotionDetail, ctx);
        await this.connection.getRepository(ctx, OrderLinePromotionDetail).save(orderLinePromotionDetail);
      }
    }
    return;
  }
  /**
   * 根据优惠结果获取promInstanceId
   * @param ctx
   * @param promLineResult
   */
  async getPromInstanceId(ctx: RequestContext, promLineResult: PromLineResult) {
    const type = promLineResult.type;
    if (type !== PromotionType.Coupon) {
      return promLineResult.promInstanceId as ID;
    } else {
      const selectCoupon = promLineResult.coupons?.find(coupon => coupon?.selected === true);
      if (selectCoupon?.couponId) {
        const userCoupon = await this.connection.getEntityOrThrow(ctx, UserCoupon, selectCoupon.couponId, {
          relations: ['coupon', 'coupon.promotion'],
        });
        return userCoupon.coupon.promotion.id;
      }
      return -1;
    }
  }

  public orderRelations: RelationPaths<Order> = [
    'channels',
    'customer',
    'customer.user',
    'lines',
    'lines.productVariant',
    'lines.productVariant.product',
    'lines.productVariant.taxCategory',
    'lines.productVariant.productVariantPrices',
    'lines.productVariant.translations',
    'lines.featuredAsset',
    'lines.taxCategory',
    'shippingLines',
    'surcharges',
  ];

  registerAfterSale(interfaceAfterSale: InterfaceAfterSale) {
    this.interfaceAfterSale = interfaceAfterSale;
  }

  async upsertResult(ctx: RequestContext, orderPromResultInput: OrderPromotionResultInput, order: Order) {
    const orderId = orderPromResultInput.orderId;
    if (!orderId) {
      throw new Error('orderId is required');
    }
    let promResult = orderPromResultInput.promResult;
    if (!promResult) {
      return;
    }
    promResult = await this.sortPromResult(promResult as PromResult, order);
    promResult = await this.updatePromResult(ctx, promResult as PromResult, order);
    let promotionResult = await this.connection
      .getRepository(ctx, OrderPromotionResult)
      .findOne({where: {order: {id: orderId}}});
    if (promotionResult) {
      promResult.disableMember = promotionResult.promResult?.disableMember;
      promResult.disableCoupon = promotionResult.promResult?.disableCoupon;
      promResult.disableShoppingCredits = promotionResult.promResult?.disableShoppingCredits;
      promotionResult.promResult = promResult as PromResult;
      promotionResult = await this.connection.getRepository(ctx, OrderPromotionResult).save(promotionResult);
    } else {
      const newOrderPromResult = new OrderPromotionResult({
        order: {id: orderId},
        promResult: promResult as PromResult,
      });
      promotionResult = await this.connection.getRepository(ctx, OrderPromotionResult).save(newOrderPromResult);
    }
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    Logger.info(`redis缓存清除:${CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId)}`);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (order.customFields as any).orderPromotionResult = promotionResult;
    return promotionResult;
  }
  async updatePromResult(ctx: RequestContext, promResult: PromResult, order: Order): Promise<PromResult> {
    let orderLinePromResults = promResult.orderLinePromResults;
    if (!orderLinePromResults) {
      return promResult;
    }
    orderLinePromResults = orderLinePromResults.map(orderLinePromResult => {
      orderLinePromResult!.totalPrice = (orderLinePromResult?.price || 0) - (orderLinePromResult?.discountAmount || 0);
      return orderLinePromResult;
    });
    let orderTotalPrice = orderLinePromResults
      .map(orderLinePromResult => orderLinePromResult?.totalPrice || 0)
      .reduce((a, b) => a + b, 0);
    const orderTotalPoints = promResult.promLineResults
      ?.map(
        promLineResult =>
          promLineResult?.orderLines?.map(orderLine => orderLine?.points || 0).reduce((a, b) => a + b, 0) || 0,
      )
      .reduce((a, b) => a + b, 0);
    if (promResult.surcharge) {
      orderTotalPrice += promResult.surcharge.amount || 0;
    }
    if (order.shipping) {
      orderTotalPrice += order.shipping;
    }
    promResult.orderTotalPrice = Math.floor(orderTotalPrice);
    promResult.orderTotalPoints = Math.floor(orderTotalPoints ?? 0);

    // 计算赠送的购物金 type = PromotionType.ShoppingCreditsClaim
    const shoppingCreditsClaim = promResult.promLineResults
      ?.filter(promLineResult => promLineResult?.type === PromotionType.ShoppingCreditsClaim)
      .reduce((a, b) => NP.plus(a + (b?.shoppingCredits || 0)), 0);
    promResult.shoppingCreditsClaim = Math.floor(shoppingCreditsClaim ?? 0);

    // 计算抵扣的总购物金
    const shoppingCreditsDeduction = promResult.promLineResults
      ?.filter(promLineResult => promLineResult?.type === PromotionType.ShoppingCreditsDeduction)
      .reduce((a, b) => NP.plus(a + (b?.discountAmount || 0)), 0);
    promResult.shoppingCreditsDeduction = Math.floor(shoppingCreditsDeduction ?? 0);
    return promResult;
  }
  async sortPromResult(promResult: PromResult, order: Order): Promise<PromResult> {
    let promLineResults = promResult.promLineResults;
    const leftOrderLines = promResult.leftOrderLines;
    if (!promLineResults) {
      return promResult;
    }
    order.lines.sort((a, b) => {
      return a.createdAt < b.createdAt ? 1 : -1;
    });
    const orderLineIds = order.lines.map(line => line.id);
    //先对活动里面的orderLine排序
    promLineResults = promLineResults.map(promLineResult => {
      promLineResult?.orderLines?.sort((a, b) => {
        if (!a || !b) {
          return 1;
        }
        return orderLineIds.indexOf(a.orderLineId as ID) < orderLineIds.indexOf(b.orderLineId as ID) ? -1 : 1;
      });
      return promLineResult;
    });
    //再对活动排序
    promLineResults.sort((a, b) => {
      if (!a?.orderLines || !b?.orderLines) {
        return 1;
      }
      return orderLineIds.indexOf(a.orderLines[0]?.orderLineId as ID) <
        orderLineIds.indexOf(b.orderLines[0]?.orderLineId as ID)
        ? -1
        : 1;
    });
    //再对剩余的orderLine排序
    leftOrderLines?.sort((a, b) => {
      if (!a || !b) {
        return 1;
      }
      return orderLineIds.indexOf(a.lineId as ID) < orderLineIds.indexOf(b.lineId as ID) ? -1 : 1;
    });
    promResult.promLineResults = promLineResults;
    promResult.leftOrderLines = leftOrderLines;
    return promResult;
  }

  async getResultByOrderIds(ctx: RequestContext, orderIds: ID[]) {
    if (orderIds.length <= 0) {
      //预下单（删除换购商品和检查库存）移到获取订单信息时删除 2022-07-24
      // for (const orderId of orderIds) {
      //   const order = await this.getOrderOrThrow(ctx, Order, orderId);
      //   if ((order.customFields as OrderCustomFields).purchaseType === OrderPurchaseType.OutrightPurchase) {
      //     if (isRemoveMarkUp) {
      //       await this.cancelAllMarkUp(ctx, orderId);
      //     }
      //     await this.checkInventory(ctx, order);
      //   }
      // }
      return null;
    }

    let promotionResults = [];
    for (const orderId of orderIds) {
      const promotionResult = await this.getResultByOrderId(ctx, orderId);
      if (promotionResult) {
        promotionResults.push(promotionResult);
      }
    }

    for (const promotionResult of promotionResults) {
      if (promotionResult?.order?.state !== 'AddingItems') {
        continue;
      }
      const promLineResults = promotionResult.promResult?.promLineResults;
      if (promLineResults) {
        let isRecalculate = false;
        promLineResults.map(promLineResult => {
          if (promLineResult?.promExpireTime && new Date(promLineResult.promExpireTime) < new Date()) {
            isRecalculate = true;
          }
        });
        if (!isRecalculate) {
          //检查活动是否有更新
          const activeUpdateTime = await this.kvsService.activeTime.get(String(ctx.channelId));
          if (activeUpdateTime) {
            const activeTime = new Date(activeUpdateTime as string);
            if (promotionResult.updatedAt < activeTime) {
              isRecalculate = true;
            }
          }
        }
        if (!isRecalculate) {
          try {
            const customer = await this.orderCustomCommonService.interfaceSubscriptionOrder.getCustomer(ctx);
            if (!customer) {
              throw new UnauthorizedError();
            }
            // 检查会员是否更新
            const userMemberUpdateTime = await this.kvsService.userMemberUpdateTime.get(
              `${customer.id}_${ctx.channelId}`,
            );
            if (userMemberUpdateTime) {
              const memberUpdateTime = new Date(userMemberUpdateTime as string);
              if (promotionResult.updatedAt < memberUpdateTime) {
                isRecalculate = true;
              }
            }
          } catch (error) {
            Logger.error(`获取会员更新时间失败`);
          }
        }
        if (isRecalculate) {
          const order = await this.orderService.findOne(ctx, promotionResult.order.id);
          if (!order) {
            throw new EntityNotFoundError('Order', promotionResult.order.id);
          }
          await this.applyPriceAdjustments(ctx, order);
        }
      }
    }

    promotionResults = [];
    for (const orderId of orderIds) {
      const promotionResult = await this.getResultByOrderId(ctx, orderId);
      if (promotionResult) {
        promotionResults.push(promotionResult);
      }
    }
    return promotionResults;
  }

  async checkDeleteProduct(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    if (!order) {
      return;
    }
    let isUpdateOrder = false;
    for (const orderLine of order.lines) {
      const productVariant = await this.customerProductVariantService.getVariantByVariantIdIncludeDelete(
        ctx,
        orderLine.productVariant.id,
      );
      if (!productVariant?.enabled || productVariant.deletedAt) {
        order = (await this.removeItemFromOrder(ctx, order.id, orderLine.id, order)) as Order;
        isUpdateOrder = true;
      }
    }
    if (isUpdateOrder) {
      if (isApplyPriceAdjustments) {
        order = await this.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }

  /**
   * 检查库存
   * @param ctx
   * @param order
   * @returns
   */
  async checkInventory(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    if (!order) {
      return;
    }
    // 订单商品数量是否变动
    let isUpdateCount = false;
    let isUpdateOrder = false;
    for (const orderLine of order.lines) {
      const productVariant = await this.customerProductVariantService.getVariantByVariantId(
        ctx,
        orderLine.productVariant.id,
      );
      if (!productVariant) {
        order = (await this.removeItemFromOrder(ctx, order.id, orderLine.id, order)) as Order;
        isUpdateOrder = true;
        continue;
      }
      const correctedQuantity = await this.constrainQuantityToSaleable(ctx, productVariant, orderLine.quantity, 0);
      if (
        correctedQuantity === 0 ||
        // correctedQuantity < orderLine.quantity ||
        productVariant.product.enabled === false
      ) {
        //删除订单项 并且需要重新计算价格
        order = (await this.removeItemFromOrder(ctx, order.id, orderLine.id, order)) as Order;
        isUpdateOrder = true;
      } else if (correctedQuantity < orderLine.quantity) {
        // 修改订单项数量
        orderLine.quantity = correctedQuantity;
        isUpdateOrder = true;
        await this.adjustOrderLine(ctx, order.id, orderLine.id, correctedQuantity);
      }
    }
    if (isUpdateOrder) {
      isUpdateCount = true;
      if (isApplyPriceAdjustments) {
        order = await this.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
          isUpdateCount,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
          isUpdateCount,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
      isUpdateCount,
    };
  }

  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, orderId: ID) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId);
      }
      return '';
    },
  })
  async getResultByOrderId(ctx: RequestContext, orderId: ID) {
    const memoryStorageCacheKey = CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      // Logger.info(`内存缓存获取订单活动优惠::${memoryStorageCacheKey}`);
      if (cacheData) {
        return cacheData as OrderPromotionResult;
      }
    }
    const cache =
      ctx.apiType === 'shop'
        ? {
            id: memoryStorageCacheKey,
            milliseconds: DEFAULT_CACHE_TIMEOUT,
          }
        : false;
    const promotionResult = await this.connection.getRepository(ctx, OrderPromotionResult).findOne({
      where: {order: {id: orderId}},
      relations: ['order'],
      cache: cache,
    });
    // Logger.info(`数据库或Redis获取订单活动优惠::${memoryStorageCacheKey}`);
    if (!promotionResult) {
      return null;
    }
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, promotionResult);
    }
    return promotionResult;
  }

  async orderApplyCoupon(ctx: RequestContext, orderId: ID) {
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult) {
      return null;
    }
    if (!orderPromResult.promResult?.disableCoupon) {
      return orderPromResult.order;
    }
    if (orderPromResult.promResult?.coupons) {
      orderPromResult.promResult?.coupons?.map(item => {
        if (item) {
          item.selected = false;
          item.autoSelected = false;
        }
        return item;
      });
    }
    orderPromResult.promResult!.disableCoupon = false;
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    return this.applyPriceAdjustments(ctx, order);
  }

  /**
   * 应用优惠券
   * @param ctx
   * @param userCouponId
   * @param orderId
   * @returns
   */
  async applyCoupon(ctx: RequestContext, userCouponId: ID, orderId: ID) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult?.coupons?.map(item => {
      if (item?.couponId === userCouponId) {
        item.selected = true;
        item.autoSelected = false;
      } else if (item?.selected) {
        item.selected = false;
      }
      return item;
    });
    orderPromResult.promResult!.disableCoupon = false;
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return this.applyPriceAdjustments(ctx, order);
  }

  /**
   * 取消使用优惠
   * @param ctx
   * @param orderId
   * @returns
   */
  async cancelCoupon(ctx: RequestContext, orderId: ID) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult.disableCoupon = true;
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return this.applyPriceAdjustments(ctx, order);
  }

  /**
   * 应用会员优惠,加入传参数isApplyPriceAdjustments为false则不重新计算价格
   * @param ctx
   * @param order 订单
   * @param isApplyPriceAdjustments 是否重新计算优惠价格 默认为true
   * @returns 是否需要重新计算价格 true:需要重新计算价格 false:不需要重新计算价格
   */
  async applyMember(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    const orderPromResult = await this.getResultByOrderId(ctx, order.id);
    if (!orderPromResult?.promResult) {
      return false;
    }
    if (orderPromResult.promResult.disableMember) {
      orderPromResult.promResult.disableMember = false;
      await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
      if (isApplyPriceAdjustments) {
        await this.applyPriceAdjustments(ctx, order);
        return false;
      }
      return true;
    }
    return false;
  }

  /**
   * 取消会员优惠
   * @param ctx
   * @param orderId
   * @returns
   */
  async cancelMember(ctx: RequestContext, orderId: ID) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult.disableMember = true;
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return this.applyPriceAdjustments(ctx, order);
  }

  async cancelShoppingCredits(ctx: RequestContext, order: Order) {
    const orderId = order.id;
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult.disableShoppingCredits = true;
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return this.applyPriceAdjustments(ctx, order);
  }
  async applyShoppingCredits(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    const orderId = order.id;
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      return order;
    }
    if (orderPromResult.promResult.disableShoppingCredits) {
      orderPromResult.promResult.disableShoppingCredits = false;
      await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
      if (isApplyPriceAdjustments) {
        await this.applyPriceAdjustments(ctx, order);
        return order;
      }
      return order;
    }
    return order;
  }

  /**
   * 取消赠品选择
   * @param ctx
   * @param orderId
   * @param giftId
   * @returns
   */
  async cancelGift(ctx: RequestContext, orderId: ID, giftId: ID, promInstanceId: ID) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult.gifts?.map(gift => {
      if (gift?.promInstanceId === promInstanceId) {
        gift.items?.map(item => {
          if (item?.giftId === giftId) {
            item.selected = false;
          }
        });
      }
    });
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return order;
  }

  /**
   * 选择赠品
   * @param ctx
   * @param orderId
   * @param giftId
   * @returns
   */
  async selectGift(ctx: RequestContext, orderId: ID, giftId: ID, skuId: ID, promInstanceId: ID, ladderLevel?: number) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    orderPromResult.promResult.gifts?.map(gift => {
      if (gift?.promInstanceId === promInstanceId) {
        gift.items?.map(item => {
          // ladderLevel不为空时，只选择对应阶梯的赠品 0也是有效值
          if (item?.giftId === giftId && (ladderLevel === undefined || item.ladderLevel === ladderLevel)) {
            item.skuId = String(skuId);
            item.autoSelected = false;
            item.selected = true;
          } else if (item?.selected) {
            item.autoSelected = true;
            item.selected = false;
          }
          return item;
        });
      }
      return gift;
    });
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    return order;
  }

  async markUpByOrder(ctx: RequestContext, orderId: ID) {
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      return [];
    }
    const promInstanceId: ID[] = [];
    if (orderPromResult.promResult.gifts) {
      orderPromResult.promResult.gifts.map(gift => {
        if (gift?.promInstanceId && gift?.giftType === GiftType.MarkUp && gift.items && gift.items.length > 0) {
          promInstanceId.push(gift.promInstanceId as ID);
        }
      });
    }
    if (promInstanceId.length <= 0) {
      return [];
    }
    const purchaseAtAPremium = await this.connection
      .getRepository(ctx, PurchasePremium)
      .createQueryBuilder('opr')
      .leftJoinAndSelect(`opr.purchasePremiumProducts`, `purchasePremiumProducts`)
      .leftJoinAndSelect(`purchasePremiumProducts.product`, `product`)
      .leftJoinAndSelect(`product.variants`, `variants`)
      .andWhere('variants.deletedAt is null')
      .andWhere('product.deletedAt is null')
      .leftJoinAndSelect(`variants.stockLevels`, `stockLevels`)
      .andWhere('product.enabled = true')
      .leftJoinAndSelect(`opr.promotion`, `promotion`)
      .andWhere('promotion.id in (:...promInstanceId)', {promInstanceId})
      .getMany();
    for (const item of purchaseAtAPremium) {
      item.purchasePremiumProducts.sort((a, b) => {
        return a.sort - b.sort;
      });
      // 过滤商品下架或者全部SKU库存不足的商品
      item.purchasePremiumProducts = item.purchasePremiumProducts.filter(purchasePremiumProduct => {
        if (!purchasePremiumProduct.product) {
          return false;
        }
        if (purchasePremiumProduct.product.enabled === false) {
          return false;
        }
        // 过滤全部SKU库存不足的商品 stockLevels也是一个数组 库存是 stockOnHand - stockAllocated
        const stockLevels = purchasePremiumProduct.product.variants
          .filter(variant => variant.enabled)
          .map(variant => variant.stockLevels)
          .reduce((a, b) => a.concat(b), []);
        if (stockLevels.length <= 0) {
          return false;
        }
        const stockOnHand = stockLevels
          .map(stockLevel => (stockLevel.stockOnHand > 0 ? stockLevel.stockOnHand : 0))
          .reduce((a, b) => a + b);
        const stockAllocated = stockLevels
          .map(stockLevel => (stockLevel.stockAllocated > 0 ? stockLevel.stockAllocated : 0))
          .reduce((a, b) => a + b);
        if (stockOnHand - stockAllocated <= 0) {
          return false;
        }
        return true;
      });
    }
    return purchaseAtAPremium;
  }

  async freeGiftByOrder(ctx: RequestContext, orderId: ID) {
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      return [];
    }
    const freeGiftItems: GiftItem[] = [];
    if (orderPromResult.promResult.gifts) {
      orderPromResult.promResult.gifts.forEach(gift => {
        if (gift?.giftType === GiftType.Free) {
          gift.items?.forEach(item => {
            if (item?.giftId) {
              freeGiftItems.push(item);
            }
          });
        }
      });
    }
    if (freeGiftItems.length <= 0) {
      return null;
    }
    const freeGiftIds = freeGiftItems.map(item => item.giftId) as ID[];
    if (freeGiftIds.length <= 0) {
      return null;
    }
    let freeGifts = await this.freeGiftService.findFreeGifts(ctx, freeGiftIds);
    if (!freeGifts?.length) {
      return null;
    }
    // freeGifts 先在gifts中按照items中的ladderPriority升序
    // 当ladderPriority相同时，按照items中的priority升序
    freeGifts = freeGifts.sort((a, b) => {
      const itemA = freeGiftItems.find(item => idsAreEqual(item.giftId as ID, a.id));
      const itemB = freeGiftItems.find(item => idsAreEqual(item.giftId as ID, b.id));
      const ladderPriorityA = itemA?.ladderPriority ?? 0;
      const ladderPriorityB = itemB?.ladderPriority ?? 0;
      if (ladderPriorityA === ladderPriorityB) {
        const priorityA = itemA?.priority ?? 0;
        const priorityB = itemB?.priority ?? 0;
        return priorityA - priorityB;
      }
      return ladderPriorityA - ladderPriorityB;
    });
    // freeGifts = freeGifts.sort((a, b) => {
    //   const itemA = freeGiftItems.find(item => idsAreEqual(item.giftId as ID, a.id));
    //   const itemB = freeGiftItems.find(item => idsAreEqual(item.giftId as ID, b.id));

    //   const priorityA = itemA?.priority ?? 0;
    //   const priorityB = itemB?.priority ?? 0;

    //   return priorityA - priorityB;
    // });
    return freeGifts;
  }

  @Transaction()
  async memberPromotionResult(ctx: RequestContext, order: Order) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }
    const memberPromotionCount = await this.getMemberPromotionCount(ctx, order.id, customerId);
    const discountCount = memberPromotionCount.discountCount;
    const promInstanceId: ID = memberPromotionCount.promInstanceId;
    const memberId: ID = memberPromotionCount.memberId;
    const memberPromotionRecord = new MemberPromotionRecord({
      orderId: order.id,
      memberId,
      customerId,
      promInstanceId,
      count: discountCount,
    });
    const quantity = await this.getMemberPurchaseCount(ctx, customerId, memberId, order.orderPlacedAt);
    await this.setMemberPurchaseCount(customerId, memberId, quantity + discountCount, order.orderPlacedAt);
    await this.connection.getRepository(ctx, MemberPromotionRecord).save(memberPromotionRecord);
  }

  @Transaction()
  async saveCustomerOrderIsMember(ctx: RequestContext, order: Order) {
    const customerId = order.customerId as ID;
    if (!customerId) {
      return;
    }
    const member = await this.memberService.getUserMember(ctx, true, customerId);
    if (member) {
      const orderPromotionResult = await this.getResultByOrderId(ctx, order.id);
      if (orderPromotionResult) {
        await this.connection.getRepository(ctx, OrderPromotionResult).update(
          {
            id: orderPromotionResult.id,
          },
          {
            isMember: true,
          },
        );
      }
    }
  }

  @Transaction()
  async productLimitationResult(ctx: RequestContext, order: Order) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }
    const customerCtx = await this.commonService.getCtxByCustomerAndChannels(
      order.channels,
      order.customer as Customer,
    );
    if (!customerCtx) {
      return;
    }
    for (const line of order.lines) {
      if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        continue;
      }
      const productVariant = line.productVariant;
      const product = await this.productService.findOne(ctx, productVariant.productId);
      if (!product) {
        Logger.error(`product is required${productVariant.productId}`);
        continue;
      }
      await this.kvsService.productLimitation.del(`${customerId}_${productVariant.productId}`);
      const pointsExchangeInfo = await this.pointsProductService.getPointsExchangeInfo(ctx, product.id);
      if (pointsExchangeInfo) {
        await this.kvsService.productLimitation.del(
          `Exchangeable:${customerId}_${productVariant.productId}_${pointsExchangeInfo.id}`,
        );
      }
      // const quantity = await this.orderCustomCommonService.getPurchasedCountFromRedis(
      //   customerCtx,
      //   order,
      //   product,
      //   false,
      // );
      // await this.setPurchasedCountFromRedis(
      //   customerId,
      //   product,
      //   quantity + (line.quantity || line.orderPlacedQuantity),
      // );
    }
  }
  // async setPurchasedCountFromRedis(customerId: ID, product: Product, quantity: number) {
  //   const limitType = (product.customFields as ProductCustomFields).limitType as LimitType;
  //   const limitTypeValue = this.orderCustomCommonService.getLimitTypeValue(limitType);
  //   const ttl = this.orderCustomCommonService.getLimitTypeTtl(limitType);
  //   quantity = quantity < 0 ? 0 : quantity;
  //   await this.kvsService.productLimitation.set(
  //     `${customerId}_${product.id}`,
  //     JSON.stringify({limitType, typeValue: limitTypeValue, quantity: quantity}),
  //     ttl,
  //   );
  // }

  async getMemberPurchaseCount(ctx: RequestContext, customerId: ID, memberId: ID, time?: Date): Promise<number> {
    if (!memberId) {
      return 0;
    }
    if (!time) {
      time = new Date();
    }
    const memberPurchaseQuantity = await this.kvsService.memberPurchase.get(
      `${customerId}_${memberId}_${time.getFullYear()}${time.getMonth() + 1}`,
    );
    if (time.getMonth() !== new Date().getMonth()) {
      return memberPurchaseQuantity ?? 0;
    }
    const purchaseQuantity =
      memberPurchaseQuantity ?? (await this.getUsedMemberPromotionCount(ctx, customerId, memberId));
    if (purchaseQuantity !== memberPurchaseQuantity) {
      await this.kvsService.memberPurchase.set(
        `${customerId}_${memberId}_${time.getFullYear()}${time.getMonth() + 1}`,
        purchaseQuantity,
      );
    }
    return purchaseQuantity;
  }

  async setMemberPurchaseCount(customerId: ID, memberId: ID, quantity: number, time?: Date) {
    if (!memberId) {
      return;
    }
    if (!time) {
      time = new Date();
    }
    await this.kvsService.memberPurchase.set(
      `${customerId}_${memberId}_${time.getFullYear()}${time.getMonth() + 1}`,
      quantity,
    );
  }

  async getMemberId(ctx: RequestContext, customerId: ID) {
    const customerMemberJson = await this.kvsService.customerMember.get(String(customerId + ':' + ctx.channelId));
    if (customerMemberJson) {
      const customerMember = JSON.parse(customerMemberJson);
      const customerMemberId = customerMember.memberId;
      const isMember = customerMember.isMember;
      const memberEndTime = customerMember.memberEndTime;
      if (isMember) {
        if (memberEndTime) {
          if (new Date(memberEndTime) > new Date()) {
            return customerMemberId;
          }
          return 0;
        }
        return customerMemberId;
      }
    } else {
      const member = await this.memberService.getUserMember(ctx, true, customerId);
      if (member) {
        await this.kvsService.customerMember.set(
          String(customerId + ':' + ctx.channelId),
          JSON.stringify({memberId: member.id, isMember: true, memberEndTime: member.maturityAt}),
        );
        return member.id;
      }
    }
    return 0;
  }

  @Transaction()
  async productLimitationRefund(ctx: RequestContext, order: Order, merchantVoluntaryRefund: MerchantVoluntaryRefund) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }

    const orderLineId = merchantVoluntaryRefund.orderLine.id;
    if (!orderLineId) {
      return;
    }
    // 可退金额
    const refundableAmount = await this.getRefundableAmount(ctx, order.id, [orderLineId]);
    if (refundableAmount > 0) {
      return;
    }
    const orderLine = await this.connection.getEntityOrThrow(ctx, OrderLine, orderLineId, {
      relations: ['productVariant', 'productVariant.product'],
    });
    const customerCtx = await this.commonService.getCtxByCustomerAndChannels(
      order.channels,
      order.customer as Customer,
    );
    if (!customerCtx) {
      return;
    }
    if ((orderLine.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
      return;
    }
    const productId = orderLine.productVariant.productId;
    if (productId) {
      await this.kvsService.productLimitation.del(`${customerId}_${productId}`);
      const pointsExchangeInfo = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
      if (pointsExchangeInfo) {
        await this.kvsService.productLimitation.del(`Exchangeable:${customerId}_${productId}_${pointsExchangeInfo.id}`);
      }
    }
  }

  @Transaction()
  async cancelProductLimitationResult(ctx: RequestContext, order: Order) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }
    const customerCtx = await this.commonService.getCtxByCustomerAndChannels(
      order.channels,
      order.customer as Customer,
    );
    if (!customerCtx) {
      return;
    }
    for (const line of order.lines) {
      if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        continue;
      }
      const productVariant = line.productVariant;
      await this.kvsService.productLimitation.del(`${customerId}_${productVariant.productId}`);
      const pointsExchangeInfo = await this.pointsProductService.getPointsExchangeInfo(ctx, productVariant.productId);
      if (pointsExchangeInfo) {
        await this.kvsService.productLimitation.del(
          `Exchangeable:${customerId}_${productVariant.productId}_${pointsExchangeInfo.id}`,
        );
      }
    }
  }

  @Transaction()
  async cancelMemberPromotionResult(ctx: RequestContext, order: Order) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }
    const memberPromotionCount = await this.getMemberPromotionCount(ctx, order.id, customerId);
    const discountCount = memberPromotionCount.discountCount;
    const memberId: ID = memberPromotionCount.memberId;
    const memberPromotionRecord = await this.connection.getRepository(ctx, MemberPromotionRecord).findOne({
      where: {
        orderId: order.id,
        customerId: customerId,
        memberId: memberId,
      },
    });
    if (!memberPromotionRecord) {
      return;
    }
    memberPromotionRecord.count =
      memberPromotionRecord.count > discountCount ? memberPromotionRecord.count - discountCount : 0;
    const quantity = await this.getMemberPurchaseCount(ctx, customerId, memberId, order.orderPlacedAt);
    await this.setMemberPurchaseCount(customerId, memberId, quantity - discountCount, order.orderPlacedAt);
    await this.connection.getRepository(ctx, MemberPromotionRecord).save(memberPromotionRecord);
  }

  async afterSalePromotionResult(ctx: RequestContext, order: Order, memberProductCount: number) {
    const customerId = order.customer?.id;
    if (!customerId) {
      return;
    }
    const memberPromotionCount = await this.getMemberPromotionCount(ctx, order.id, customerId);
    const memberId: ID = memberPromotionCount.memberId;
    const memberPromotionRecord = await this.connection.getRepository(ctx, MemberPromotionRecord).findOne({
      where: {
        orderId: order.id,
        customerId: customerId,
        memberId: memberId,
      },
    });
    if (!memberPromotionRecord) {
      return;
    }
    memberPromotionRecord.count =
      memberPromotionRecord.count > memberProductCount ? memberPromotionRecord.count - memberProductCount : 0;
    const quantity = await this.getMemberPurchaseCount(ctx, customerId, memberId, order.orderPlacedAt);
    await this.setMemberPurchaseCount(
      customerId,
      memberId,
      quantity - memberPromotionCount.discountCount,
      order.orderPlacedAt,
    );
    return this.connection.getRepository(ctx, MemberPromotionRecord).save(memberPromotionRecord);
  }

  async getMemberPromotionCount(ctx: RequestContext, orderId: ID, customerId: ID) {
    let promInstanceId: ID = 0;
    let discountCount = 0;
    let memberId: ID = 0;
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      Logger.debug(`orderPromResult is required${orderId}`);
      return {promInstanceId, discountCount, memberId};
    }
    const promLineResults = orderPromResult.promResult.promLineResults;
    promLineResults?.map(promLineResult => {
      if (promLineResult?.type === PromotionType.Member) {
        promInstanceId = promLineResult.promInstanceId as ID;
        discountCount += promLineResult.discountCount || 0;
      }
    });

    if (promInstanceId !== 0) {
      const member = await this.memberService.getUserMember(ctx, true, customerId);
      if (member) {
        memberId = member.id;
      }
    }
    return {
      promInstanceId,
      discountCount,
      memberId,
    };
  }

  async getUsedMemberPromotionCount(ctx: RequestContext, customerId: ID, id: ID) {
    const currentDate = new Date();
    const startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);
    const memberPromotions = await this.connection
      .getRepository(ctx, MemberPromotionRecord)
      .createQueryBuilder('memberPromotionRecord')
      .andWhere('memberPromotionRecord.customerId = :customerId', {customerId})
      .andWhere('memberPromotionRecord.memberId = :memberId', {memberId: id})
      .andWhere('memberPromotionRecord.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .getMany();

    const usedCount = memberPromotions.reduce((total, memberPromotion) => total + memberPromotion.count, 0);
    return usedCount;
  }
  /**
   * 检查订单是否需要取消
   * @param ctx
   * @param order
   */
  async checkOrderNeedsToBeCancelled(ctx: RequestContext, order: Order) {
    let isCancelOrder = false;
    const orderLineIds = order.lines.map(line => line.id);
    //获取订单全部项的可退款金额 如果可退款金额为0则取消订单
    if (orderLineIds.length > 0) {
      let getRefundableAmount = await this.getRefundableAmount(ctx, order.id, orderLineIds);
      const blindBoxRefundableAmount = await this.blindBoxRefundableAmount(ctx, orderLineIds);
      // 获取需要回收但未回收的购物金
      const shoppingCredits = await this.getOrderUnRecycledShoppingCredits(ctx, order.id);
      // 获取订单全部退回的购物金
      const returnedShoppingCreditsTotal = Number(shoppingCredits.returnedShoppingCreditsTotal);
      // 获取订单抵扣的购物金
      const orderPromResult = await this.getResultByOrderId(ctx, order.id);
      const shoppingCreditsDeductionResult = Number(orderPromResult?.promResult?.shoppingCreditsDeduction);

      // 获取订单需要消耗的全部购物金
      getRefundableAmount = NP.minus(getRefundableAmount, shoppingCredits.unRecycledShoppingCredits);
      if (
        getRefundableAmount <= 0 &&
        blindBoxRefundableAmount <= 0 &&
        returnedShoppingCreditsTotal >= shoppingCreditsDeductionResult
      ) {
        isCancelOrder = true;
      }
    }
    if (isCancelOrder) {
      await this.orderService.cancelOrder(ctx, {orderId: order.id});
    }
  }
  async getOrderUnRecycledShoppingCredits(ctx: RequestContext, orderId: ID) {
    const afterShopping = await this.interfaceAfterSale.getAfterSaleUnRecycledShoppingCredits(ctx, orderId);
    const afterSaleUnRecycledShoppingCredits =
      Number(afterShopping.shouldReclaimedShoppingCredits ?? 0) -
      Number(afterShopping.actualReclaimedShoppingCredits ?? 0);

    const merchantVoluntaryShopping = await this.getMerchantVoluntaryRefundUnRecycledShoppingCredits(ctx, orderId);
    const merchantVoluntaryRefundUnRecycledShoppingCredits =
      Number(merchantVoluntaryShopping?.shouldReclaimedShoppingCredits ?? 0) -
      Number(merchantVoluntaryShopping?.actualReclaimedShoppingCredits ?? 0);
    const unRecycledShoppingCredits =
      afterSaleUnRecycledShoppingCredits + merchantVoluntaryRefundUnRecycledShoppingCredits;
    const totalShippingPrice =
      Number(afterShopping.shouldReclaimedShoppingCredits ?? 0) +
      Number(merchantVoluntaryShopping.shouldReclaimedShoppingCredits ?? 0);

    // 退回的总购物金
    const returnedShoppingCreditsTotal =
      Number(afterShopping.returnedShoppingCredits ?? 0) +
      Number(merchantVoluntaryShopping.returnedShoppingCredits ?? 0);
    return {
      unRecycledShoppingCredits: unRecycledShoppingCredits > 0 ? unRecycledShoppingCredits : 0,
      totalShippingPrice: totalShippingPrice,
      returnedShoppingCreditsTotal: returnedShoppingCreditsTotal,
    };
  }
  async getMerchantVoluntaryRefundUnRecycledShoppingCredits(ctx: RequestContext, orderId: ID) {
    // 获取应回收购物金总和和实际回收购物金总和
    const totalPrice = await this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund')
      .andWhere('merchantVoluntaryRefund.orderId = :orderId', {orderId})
      .select(`SUM(merchantVoluntaryRefund.shouldReclaimedShoppingCredits)`, `totalPrice`)
      .addSelect(`SUM(merchantVoluntaryRefund.actualReclaimedShoppingCredits)`, `actualTotalPrice`)
      .addSelect(`SUM(merchantVoluntaryRefund.returnedShoppingCredits)`, `returnedShoppingCredits`)
      .getRawOne<{
        totalPrice: number;
        actualTotalPrice: number;
        returnedShoppingCredits: number;
      }>();
    // const shouldReclaimedShoppingCredits =
    //   Number(totalPrice?.totalPrice ?? 0) - Number(totalPrice?.actualTotalPrice ?? 0);
    // return shouldReclaimedShoppingCredits > 0 ? shouldReclaimedShoppingCredits : 0;
    return {
      shouldReclaimedShoppingCredits: totalPrice?.totalPrice ?? 0,
      actualReclaimedShoppingCredits: totalPrice?.actualTotalPrice ?? 0,
      returnedShoppingCredits: totalPrice?.returnedShoppingCredits ?? 0,
    };
  }

  async getRefundableAmountByOrderId(ctx: RequestContext, orderId: ID, isUpdate = false) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromotionResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromotionResult) {
      throw new Error(`订单优惠信息未找到`);
    }
    const promLineResults = orderPromotionResult.promResult?.promLineResults;
    // 获取类型为BlindBox的盲盒优惠信息
    const blindBoxResult = promLineResults?.find(item => item?.type === PromotionType.BlindBox);
    // 获取类型为ShoppingCreditsDeduction的购物金抵扣优惠
    const shoppingCreditsDeductionResult = promLineResults?.find(
      item => item?.type === PromotionType.ShoppingCreditsDeduction,
    );
    const orderLineBlindBoxMap = blindBoxResult?.orderLineBlindBoxMap;
    //orderLines 可退金额 添加是否允许售后
    const orderLinesRefundable: {refundableAmount: number; orderLine: OrderLine; isAllowAfterSale: Boolean}[] = [];
    const lineIds = order.lines.map(line => line.id);
    const refundableAmountTotal = await this.getRefundableAmount(
      ctx,
      orderId,
      lineIds,
      true,
      isUpdate,
      isUpdate === false,
    );
    let totalPrice = 0;
    for (const line of order.lines) {
      let isAllowAfterSale = false;
      let refundableAmount = await this.getRefundableAmount(
        ctx,
        orderId,
        [line.id],
        true,
        isUpdate,
        isUpdate === false,
      );
      if (refundableAmount > 0) {
        isAllowAfterSale = true;
        if (totalPrice >= refundableAmountTotal) {
          refundableAmount = 0;
        }
        totalPrice += refundableAmount;
      }
      if (!isAllowAfterSale) {
        if (!isAllowAfterSale) {
          // 判断是否是盲盒订单项
          const isBlindBox = orderLineBlindBoxMap?.some(item => {
            return idsAreEqual(item?.orderLineId as ID, line.id);
          });
          if (isBlindBox) {
            isAllowAfterSale = true;
          } else {
            // 判断是否是购物金抵扣订单项
            const isShoppingCreditsDeduction = shoppingCreditsDeductionResult?.orderLines?.some(item => {
              return idsAreEqual(item?.orderLineId as ID, line.id);
            });
            if (isShoppingCreditsDeduction) {
              isAllowAfterSale = true;
            }
          }
          // 判断是否兑换券商品
          if (!isAllowAfterSale) {
            const orderLinePromResults = orderPromotionResult?.promResult?.orderLinePromResults;
            const isExchangeable = orderLinePromResults?.some(item => {
              return (
                idsAreEqual(item?.orderLineId as ID, line.id) &&
                item?.discountDetails?.some(detail => {
                  return detail?.type === PromotionType.Coupon;
                }) &&
                item.totalPrice === 0
              );
            });
            if (isExchangeable) {
              isAllowAfterSale = true;
            }
          }
        }
      }
      // 判断是否
      orderLinesRefundable.push({
        refundableAmount: refundableAmount,
        orderLine: line,
        isAllowAfterSale: isAllowAfterSale,
      });
    }
    const practicalRefundableAmountTotal = orderLinesRefundable.reduce(
      (total, orderLineRefundable) => total + orderLineRefundable.refundableAmount,
      0,
    );
    if (refundableAmountTotal > practicalRefundableAmountTotal) {
      // 把剩余的金额加到第一个订单项上
      orderLinesRefundable.sort((a, b) => Number(a.orderLine.id) - Number(b.orderLine.id));
      // eslint-disable-next-line @typescript-eslint/prefer-for-of
      for (let i = 0; i < orderLinesRefundable.length; i++) {
        const orderLineRefundable = orderLinesRefundable[i];
        if (orderLineRefundable.refundableAmount > 0) {
          orderLineRefundable.refundableAmount = NP.plus(
            orderLineRefundable.refundableAmount,
            NP.minus(refundableAmountTotal, practicalRefundableAmountTotal),
          );
          break;
        }
      }
    }
    return orderLinesRefundable;
  }

  async checkOrderLineToBeCancelled(
    ctx: RequestContext,
    order: Order,
    orderLineInputs: {orderLineId: ID; quantity: number}[],
  ) {
    let isCancelOrderLine = false;
    if (orderLineInputs.length > 0) {
      const orderLineIds = orderLineInputs.map(orderLineInput => orderLineInput.orderLineId);
      const getRefundableAmount = await this.getRefundableAmount(ctx, order.id, orderLineIds);
      if (getRefundableAmount === 0) {
        isCancelOrderLine = true;
      }
    }
    if (isCancelOrderLine) {
      await this.orderService.cancelOrder(ctx, {
        orderId: order.id,
        lines: orderLineInputs,
      });
    }
  }

  // 获取已经售后或者已经主动退款的订单项
  async getRefundedOrderLineIds(ctx: RequestContext, orderId: ID, merchantVoluntaryRefundId?: ID) {
    const afterSaleOrderLineIds = await this.interfaceAfterSale.getAfterSaleSuccessOrderLineIds(ctx, orderId);
    // 获取商家主动退款的订单项
    const merchantVoluntaryRefundOrderLineIds = await this.getMerchantVoluntaryRefundOrderLineIds(
      ctx,
      orderId,
      merchantVoluntaryRefundId,
    );
    // 合并orderLineIds
    const orderLineIds = [...afterSaleOrderLineIds, ...merchantVoluntaryRefundOrderLineIds];
    // 去重
    const uniqueOrderLineIds = [...new Set(orderLineIds)];
    return uniqueOrderLineIds;
  }

  // 获取主动退款的订单项
  async getMerchantVoluntaryRefundOrderLineIds(ctx: RequestContext, orderId: ID, merchantVoluntaryRefundId?: ID) {
    const qb = this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund');
    qb.andWhere(`${qb.alias}.orderId = :orderId`, {orderId});
    if (merchantVoluntaryRefundId) {
      qb.andWhere(`${qb.alias}.id != :merchantVoluntaryRefundId`, {merchantVoluntaryRefundId});
    }
    const merchantVoluntaryRefunds = await qb.getMany();
    return merchantVoluntaryRefunds?.map(refund => refund.orderLineId as ID) || [];
  }

  async getRefundableAmount(
    ctx: RequestContext,
    orderId: ID,
    lineIds: ID[],
    isIncludedShippingPrice = true,
    isUpdate = false,
    isInclusionProgress = false,
  ) {
    if (lineIds.length <= 0) {
      return 0;
    }
    lineIds.map(lineId => Number(lineId));
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    let orderLinesTotal = 0;
    const notRefundableLineIds: ID[] = [];
    let notRefundableLinesTotal = 0;
    order.lines.map(line => {
      if (lineIds.includes(line.id)) {
        orderLinesTotal += line.unitPrice * (line.quantity || line.orderPlacedQuantity);
      } else {
        notRefundableLineIds.push(line.id);
        notRefundableLinesTotal += line.unitPrice * (line.quantity || line.orderPlacedQuantity);
      }
    });
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    //优惠金额
    let discountAmount = 0;
    let notRefundableDiscountAmount = 0;
    let notPaymentTotal = 0;
    if (orderPromResult?.promResult) {
      orderPromResult.promResult.orderLinePromResults?.map(orderLinePromResult => {
        if (lineIds.includes(orderLinePromResult?.orderLineId as ID)) {
          discountAmount += orderLinePromResult?.discountAmount || 0;
        } else if (notRefundableLineIds.includes(orderLinePromResult?.orderLineId as ID)) {
          notRefundableDiscountAmount += orderLinePromResult?.discountAmount || 0;
          notPaymentTotal += Math.floor(orderLinePromResult?.totalPrice || 0);
        }
      });
    }
    discountAmount = Math.ceil(discountAmount);
    notRefundableDiscountAmount = Math.ceil(notRefundableDiscountAmount);
    //后台改价
    let surchargeAmount = 0;
    let notRefundableSurchargeAmount = 0;
    if (orderPromResult?.promResult?.surcharge) {
      orderPromResult.promResult.surcharge?.details?.map(surchargeDetails => {
        if (lineIds.includes(surchargeDetails?.orderLineId as ID)) {
          surchargeAmount += surchargeDetails?.amount || 0;
        } else if (notRefundableLineIds.includes(surchargeDetails?.orderLineId as ID)) {
          notRefundableSurchargeAmount += surchargeDetails?.amount || 0;
        }
      });
    }

    //商家主动退款
    let merchantVoluntaryRefundPrice = 0;
    const merchantVoluntaryRefunds = await this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund')
      .andWhere('merchantVoluntaryRefund.orderId = :orderId', {orderId})
      .andWhere('merchantVoluntaryRefund.orderLineId IN (:...lineIds)', {lineIds})
      .getMany();
    for (const merchantVoluntaryRefund of merchantVoluntaryRefunds) {
      merchantVoluntaryRefundPrice += merchantVoluntaryRefund.price;
      if (merchantVoluntaryRefund.isIncludeShipping) {
        merchantVoluntaryRefundPrice -= Number(merchantVoluntaryRefund.refundShipping);
      }
    }
    // 剩下商品的主动退款金额
    let notRefundableMerchantVoluntaryRefundPrice = 0;
    // 剩下的商品存在主动退款的订单项ID
    const notRefundableMerchantVoluntaryRefundLineIds: ID[] = [];
    if (notRefundableLineIds.length > 0) {
      const notMerchantVoluntaryRefunds = await this.connection
        .getRepository(ctx, MerchantVoluntaryRefund)
        .createQueryBuilder('merchantVoluntaryRefund')
        .leftJoinAndSelect('merchantVoluntaryRefund.orderLine', 'orderLine')
        .andWhere('merchantVoluntaryRefund.orderId = :orderId', {orderId})
        .andWhere('orderLine.id IN (:...lineIds)', {lineIds: notRefundableLineIds})
        .getMany();
      for (const notMerchantVoluntaryRefund of notMerchantVoluntaryRefunds) {
        notRefundableMerchantVoluntaryRefundPrice += notMerchantVoluntaryRefund.price;
        notRefundableMerchantVoluntaryRefundLineIds.push(notMerchantVoluntaryRefund.orderLine.id);
        if (notMerchantVoluntaryRefund.isIncludeShipping) {
          notRefundableMerchantVoluntaryRefundPrice -= Number(notMerchantVoluntaryRefund.refundShipping);
        }
      }
    }

    //售后退款
    const afterSaleRefundPrice = await this.interfaceAfterSale.getAfterSaleSuccessTotalPrice(
      ctx,
      orderId,
      lineIds,
      isInclusionProgress,
    );
    // 剩下商品的售后退款金额
    let notRefundableAfterSaleRefundPrice = 0;
    if (notRefundableLineIds.length > 0) {
      notRefundableAfterSaleRefundPrice = await this.interfaceAfterSale.getAfterSaleSuccessTotalPrice(
        ctx,
        orderId,
        notRefundableLineIds,
        true,
      );
    }

    // 剩下商品的可退金额
    const notRefundableAmount = Math.floor(
      notRefundableLinesTotal -
        notRefundableDiscountAmount +
        notRefundableSurchargeAmount -
        notRefundableMerchantVoluntaryRefundPrice -
        notRefundableAfterSaleRefundPrice,
    );
    //可退金额 =  订单项总金额 - 优惠金额 + 后台改价 - 商家主动退款 - 售后退款
    const refundableAmount = Math.floor(
      orderLinesTotal - discountAmount + surchargeAmount - merchantVoluntaryRefundPrice - afterSaleRefundPrice,
    );

    let shippingPrice = 0;

    if (Number(order.shipping) > 0 && isIncludedShippingPrice) {
      // 查询已退的邮费 如果是修改售后则过滤掉当前售后的邮费
      const refundedShippingPrice = await this.interfaceAfterSale.getRefundedShippingPrice(
        ctx,
        orderId,
        lineIds,
        isUpdate,
      );
      // 查询主动退款的邮费
      const merchantVoluntaryRefundShippingPrice = await this.getMerchantVoluntaryRefundShippingPrice(ctx, orderId);
      // 剩余可退邮费
      const notRefundableShippingPrice =
        Number(order.shipping) - Number(refundedShippingPrice) - Number(merchantVoluntaryRefundShippingPrice);
      // 判断是否需要退邮费
      const shouldRefundShipping = async () => {
        if (notRefundableAmount <= 0) return true;
        //2024-06-29 产品要求：如果剩下的商品都产生售后或者主动退款，则需要退邮费
        if (notRefundableLineIds.length > 0) {
          // 查询剩下商品的未产生售后的订单项ID
          let notRefundableRefundableOrderLineIds = await this.interfaceAfterSale.getNotRefundableAfterSaleOrderLineIds(
            ctx,
            orderId,
            notRefundableLineIds,
          );
          if (notRefundableRefundableOrderLineIds.length <= 0) return true;
          // 过滤掉已经主动退款的订单项ID
          notRefundableRefundableOrderLineIds = notRefundableRefundableOrderLineIds.filter(
            lineId => !notRefundableMerchantVoluntaryRefundLineIds.includes(lineId),
          );
          if (notRefundableRefundableOrderLineIds.length <= 0) return true;
        }
        return false;
      };
      if (await shouldRefundShipping()) {
        shippingPrice = notRefundableShippingPrice;
      }
    }
    // 可退金额 = 可退金额 + 邮费
    let refundableAmountAll = Math.floor(refundableAmount + shippingPrice);
    const otherRefundAmount = Math.floor(
      notPaymentTotal -
        notRefundableSurchargeAmount -
        notRefundableMerchantVoluntaryRefundPrice -
        notRefundableAfterSaleRefundPrice,
    );
    // 如果剩余订单项可退金额小于等于0，则需要退剩余的全部金额
    if (otherRefundAmount <= 0) {
      // 实际支付的总金额
      const totalAmount = orderPromResult?.promResult?.orderTotalPrice ?? order.subTotal;
      // 如果当前是最后一个订单项，则需要退剩下的全部金额
      // 已退金额 = 剩余商品主动退款金额 + 剩余商品售后退款金额 + 当前商品商家主动退款金额 + 当前商品售后退款金额
      const refundedAmount = Math.floor(
        notRefundableMerchantVoluntaryRefundPrice +
          notRefundableAfterSaleRefundPrice +
          merchantVoluntaryRefundPrice +
          afterSaleRefundPrice,
      );
      // 剩余支付的可退金额
      const notRefundableAmountAll = Math.floor(totalAmount - refundedAmount);
      if (notRefundableAmountAll <= 0) {
        refundableAmountAll = 0;
      } else {
        // 只要剩余支付的可退金额大于0，则需要退剩余的全部金额
        refundableAmountAll = notRefundableAmountAll;
      }
    }
    // 如果订单项是虚拟商品，则判断是否是优惠券，如果是优惠券则扣除已经使用的优惠券金额
    // for (const orderLine of order.lines) {
    //   const productVariantCustomFields = orderLine.productVariant?.customFields as ProductVariantCustomFields;
    //   if (productVariantCustomFields.virtualTargetType === VirtualTargetType.Coupon) {
    //     const couponId = productVariantCustomFields.virtualTargetId;
    //     if (!couponId) {
    //       continue;
    //     }
    //     const orderLinePrice =
    //       orderPromResult?.promResult?.orderLinePromResults?.find(
    //         orderLinePromResult => Number(orderLinePromResult?.orderLineId) === Number(orderLine.id),
    //       )?.totalPrice ?? 0;
    //     const couponAmount = await this.getUseCouponAmount(ctx, orderId, couponId, orderLinePrice);
    //     refundableAmountAll -= couponAmount;
    //   }
    // }
    return refundableAmountAll > 0 ? refundableAmountAll : 0;
  }

  // 获取订单可退优惠券张数
  async getRefundableCount(ctx: RequestContext, orderId: ID) {
    const count = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .andWhere('userCoupon.sourceType = :sourceType', {sourceType: SourceType.OrderBuy})
      .andWhere('userCoupon.sourceId = :sourceId', {sourceId: orderId})
      .andWhere('userCoupon.state in (:...state)', {state: [UserCouponState.NotStarted, UserCouponState.Unused]})
      .getCount();
    return Number(count ?? 0);
  }

  // 失效需要退回的优惠券
  async refundCouponByOrderId(ctx: RequestContext, orderId: ID, count: number) {
    if (count <= 0) {
      return;
    }
    const userCoupons = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .andWhere('userCoupon.sourceType = :sourceType', {sourceType: SourceType.OrderBuy})
      .andWhere('userCoupon.sourceId = :sourceId', {sourceId: orderId})
      .andWhere('userCoupon.state in (:...state)', {state: [UserCouponState.NotStarted, UserCouponState.Unused]})
      .getMany();
    if (userCoupons.length <= 0) {
      return;
    }
    const userCouponIds = userCoupons.map(userCoupon => userCoupon.id);
    // 从需要失效的优惠券中取出count张优惠券
    const refundUserCouponIds = userCouponIds.slice(0, count);
    if (refundUserCouponIds.length <= 0) {
      return;
    }
    await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .update()
      .set({
        state: UserCouponState.Expire,
        isReturned: true,
      })
      .andWhere('id IN (:...refundUserCouponIds)', {refundUserCouponIds})
      .execute();
  }

  // 获取已经使用的优惠券金额
  async getUseCouponAmount(ctx: RequestContext, orderId: ID, couponId: string, orderLinePrice: number) {
    const userCoupon = await this.connection
      .getRepository(ctx, UserCoupon)
      .createQueryBuilder('userCoupon')
      .andWhere('userCoupon.sourceType = :sourceType', {sourceType: SourceType.OrderBuy})
      .andWhere('userCoupon.sourceId = :sourceId', {sourceId: orderId})
      .leftJoinAndSelect('userCoupon.coupon', 'coupon')
      .andWhere('coupon.id = :couponId', {couponId})
      .getMany();
    const count = userCoupon.length;
    if (count <= 0) {
      return 0;
    }
    // 优惠卷单价
    const couponPrice = Number(orderLinePrice) / Number(count);
    const useCoupon = userCoupon.filter(coupon => coupon.useAt);
    const useCouponPrice = couponPrice * useCoupon.length;
    return useCouponPrice;
  }

  async getMerchantVoluntaryRefundShippingPrice(ctx: RequestContext, orderId: ID) {
    const merchantVoluntaryRefunds = await this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund')
      .andWhere('merchantVoluntaryRefund.orderId = :orderId', {orderId})
      .getMany();
    let merchantVoluntaryRefundShippingPrice = 0;
    for (const merchantVoluntaryRefund of merchantVoluntaryRefunds) {
      if (merchantVoluntaryRefund.isIncludeShipping) {
        merchantVoluntaryRefundShippingPrice += Number(merchantVoluntaryRefund.refundShipping);
      }
    }
    return merchantVoluntaryRefundShippingPrice;
  }

  /**
   * 选择换购
   * @param ctx
   * @param orderId
   * @param skuId
   * @param productId
   * @param promInstanceId
   * @returns
   */
  async selectMarkUp(ctx: RequestContext, orderId: ID, skuId: ID, productId: ID, promInstanceId: ID) {
    let order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    const removeSkuIds: ID[] = [];
    const addSkuIds: ID[] = [];
    orderPromResult.promResult.gifts?.map(gift => {
      if (gift?.promInstanceId === promInstanceId) {
        gift.items?.map(item => {
          const oldItemSku = Number(item?.skuId);
          if (item?.productId === productId) {
            if (item.selected && oldItemSku !== skuId) {
              const oldSkuId = Number(item.skuId) as ID;
              if (oldSkuId) {
                removeSkuIds.push(oldSkuId as ID);
              }
            }
            if (!item.selected || oldItemSku !== skuId) {
              addSkuIds.push(skuId);
            }
            item.skuId = String(skuId);
            item.selected = true;
          } else if (item?.selected) {
            const oldSkuId = Number(item.skuId) as ID;
            if (oldSkuId) {
              removeSkuIds.push(oldSkuId as ID);
            }
            item.selected = false;
            item.skuId = String(0);
          }
          return item;
        });
      }
      return gift;
    });
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    if (addSkuIds.length > 0 && addSkuIds.includes(skuId)) {
      await this.addPurchasePremiumToOrder(ctx, skuId, orderId, promInstanceId, order);
    }
    for (const oldSkuId of removeSkuIds) {
      const lineId = order.lines.find(
        line =>
          line.productVariant.id === oldSkuId &&
          (line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium,
      )?.id;
      if (!lineId) {
        return;
      }
      await this.removePurchasePremiumToOrder(ctx, lineId, oldSkuId, orderId, promInstanceId, order);
    }
    order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    await this.applyPriceAdjustments(ctx, order);
    return order;
  }

  /**
   * 取消换购
   * @param ctx
   * @param orderId
   * @param productId
   * @param promInstanceId
   * @returns
   */
  async cancelMarkUp(ctx: RequestContext, orderId: ID, productId: ID, promInstanceId: ID) {
    let order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    if (order.state !== 'AddingItems') {
      throw new Error('order state is not AddingItems');
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      throw new Error('orderPromResult is required');
    }
    const removeSkuIds: ID[] = [];
    orderPromResult.promResult.gifts?.map(gift => {
      if (gift?.promInstanceId === promInstanceId) {
        gift.items?.map(item => {
          if (item?.productId === productId) {
            if (item.selected) {
              const skuId = Number(item.skuId) as ID;
              if (skuId) {
                removeSkuIds.push(skuId as ID);
              }
              item.selected = false;
              item.skuId = String(0);
            }
          }
          return item;
        });
      }
      return gift;
    });
    await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    for (const skuId of removeSkuIds) {
      const lineId = order.lines.find(
        line =>
          line.productVariant.id === skuId &&
          (line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium,
      )?.id;
      if (!lineId) {
        return;
      }
      await this.removePurchasePremiumToOrder(ctx, lineId, skuId, orderId, promInstanceId, order);
    }
    order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    await this.applyPriceAdjustments(ctx, order);
    return order;
  }

  /**
   * 取消所有换购
   * @param ctx
   * @param orderId
   * @returns
   */
  async cancelAllMarkUp(ctx: RequestContext, orderId: ID, order?: Order, isApplyPriceAdjustments = true) {
    if (!order) {
      order = await this.orderService.findOne(ctx, orderId);
      if (!order) {
        throw new EntityNotFoundError('Order', orderId);
      }
    }
    if (order.state !== 'AddingItems') {
      Logger.error('order state is not AddingItems');
      return;
    }
    const orderPromResult = await this.getResultByOrderId(ctx, orderId);
    if (!orderPromResult?.promResult) {
      return;
    }
    // 优惠结果是否有变动
    let isPromResultChanged = false;
    orderPromResult.promResult.gifts?.map(gift => {
      if (gift?.giftType === GiftType.MarkUp) {
        gift.items?.map(item => {
          if (item?.selected) {
            item.selected = false;
            item.skuId = String(0);
            isPromResultChanged = true;
          }
          return item;
        });
      }
      return gift;
    });
    if (isPromResultChanged) {
      await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(orderId, ctx.channelId));
    }
    const lines = order.lines;
    let recalculateOrNot = false;
    for (const line of lines) {
      if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        order = await this.removePurchasePremium(ctx, orderId, line.id as ID, order, isApplyPriceAdjustments);
        recalculateOrNot = true;
      }
    }
    //当需要取消所有换购商品时，但是订单中没有换购商品时，需要重新计算价格 主要是为了重新默认选中获取赠品
    if (recalculateOrNot) {
      if (isApplyPriceAdjustments) {
        order = await this.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }

  async cancelAllPutOnSale(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    if (!order) {
      return;
    }
    let isUpdateOrder = false;
    let orderLineLength = order.lines.length;
    for (const line of order.lines) {
      const product = await this.customProductService.findOne(ctx, line.productVariant.productId);
      if (!product) {
        continue;
      }
      if ((product.customFields as ProductCustomFields).putOnSaleType === PutOnSaleType.Scheduled) {
        const putOnSaleTime = (product.customFields as ProductCustomFields).putOnSaleTime;
        if (putOnSaleTime && new Date(putOnSaleTime) > new Date()) {
          order = (await this.removeItemFromOrder(ctx, order.id, line.id, order)) as Order;
          orderLineLength--;
          isUpdateOrder = true;
        }
      }
    }
    if (orderLineLength <= 0) {
      await this.connection
        .getRepository(ctx, OrderPromotionResult)
        .update({order: {id: order.id}}, {promResult: null});
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
    }
    if (isUpdateOrder) {
      if (isApplyPriceAdjustments) {
        order = await this.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }

  /**
   * 从订单中移除加价购商品
   * @param ctx
   * @param id
   * @param orderLineId
   * @returns
   */
  async removePurchasePremium(
    ctx: RequestContext,
    id: ID,
    orderLineId: ID,
    order?: Order,
    isApplyPriceAdjustments = true,
  ) {
    if (!order) {
      order = await this.orderService.findOne(ctx, id);
    }
    order = (await this.removeItemFromOrder(ctx, id, orderLineId, order)) as Order;
    if (!order) {
      throw new EntityNotFoundError('Order', id);
    }
    if (!order?.lines || order.lines.length === 0) {
      await this.connection.getRepository(ctx, OrderPromotionResult).update({order: {id}}, {promResult: null});
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(id, ctx.channelId));
    }
    if (isApplyPriceAdjustments) {
      return this.applyPriceAdjustments(ctx, order);
    }
    return order;
  }

  async addPurchasePremiumToOrder(
    ctx: RequestContext,
    productVariantId: ID,
    orderId: ID,
    promInstanceId: ID,
    order?: Order,
  ) {
    if (!order) {
      order = await this.orderService.findOne(ctx, orderId);
    }
    if (!order) {
      throw new Error('order not exist');
    }
    const orderLine = order.lines.find(line => line.productVariant.id === productVariantId);
    let promInstanceIds: number[] = [];
    if (orderLine) {
      promInstanceIds = ((orderLine?.customFields as OrderLineCustomFields)?.promInstanceIds as number[]) || [];
      if (!promInstanceIds.includes(Number(promInstanceId))) {
        promInstanceIds.push(Number(promInstanceId));
      }
    } else {
      promInstanceIds = [Number(promInstanceId)];
    }
    return this.addItemToOrder(
      ctx,
      orderId,
      productVariantId,
      1,
      {
        purchasePattern: PurchasePattern.PurchasePremium,
        promInstanceIds: promInstanceIds,
      },
      order,
    );
  }

  async removePurchasePremiumToOrder(
    ctx: RequestContext,
    lineId: ID,
    productVariantId: ID,
    orderId: ID,
    promInstanceId: ID,
    order?: Order,
  ) {
    if (!order) {
      order = await this.orderService.findOne(ctx, orderId);
    }
    if (!order) {
      throw new Error('order not exist');
    }
    const orderLine = order.lines.find(
      line =>
        line.productVariant.id === productVariantId &&
        (line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium,
    );
    let promInstanceIds: number[] = [];
    //如果加价购商品数量大于1，减少加价购商品数量
    if (orderLine) {
      if (orderLine.quantity > 1) {
        promInstanceIds = ((orderLine?.customFields as OrderLineCustomFields)?.promInstanceIds as number[]) || [];
        if (promInstanceIds.includes(Number(promInstanceId))) {
          const index = promInstanceIds.indexOf(Number(promInstanceId));
          promInstanceIds.splice(index, 1);
        }
        await this.adjustOrderLine(
          ctx,
          orderId,
          lineId,
          orderLine.quantity - 1,
          {
            promInstanceIds: promInstanceIds,
          },
          order,
        );
      } else {
        //直接删除加价购商品
        await this.removeItemFromOrder(ctx, orderId, lineId, order);
      }
    }
  }

  /**
   * 修改订单项数量 不需要重新计算优惠
   * @param ctx
   * @param orderId
   * @param orderLineId
   * @param quantity
   * @param customFields
   * @returns
   */
  async adjustOrderLine(
    ctx: RequestContext,
    orderId: ID,
    orderLineId: ID,
    quantity: number,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    customFields?: {[key: string]: any},
    order?: Order,
  ) {
    if (!order) {
      order = await this.getOrderOrThrow(ctx, orderId);
    }
    const orderLine = this.getOrderLineOrThrow(order, orderLineId);
    const validationError =
      this.assertAddingItemsState(order) ||
      this.assertQuantityIsPositive(quantity) ||
      this.assertNotOverOrderItemsLimit(order, quantity - orderLine.quantity) ||
      this.assertNotOverOrderLineItemsLimit(orderLine, quantity - orderLine.quantity);
    if (validationError) {
      return validationError;
    }
    if (customFields != null) {
      orderLine.customFields = customFields;
      await this.customFieldRelationService.updateRelations(ctx, OrderLine, {customFields}, orderLine);
    }
    const correctedQuantity = await this.constrainQuantityToSaleable(ctx, orderLine.productVariant, quantity);
    // let updatedOrderLines = [orderLine];
    if (correctedQuantity === 0) {
      order.lines = order.lines.filter(l => !idsAreEqual(l.id, orderLine.id));
      await this.connection.getRepository(ctx, OrderLine).remove(orderLine);
      this.eventBus.publish(new OrderLineEvent(ctx, order, orderLine, 'deleted'));
      // updatedOrderLines = [];
    } else {
      await this.updateOrderLineQuantity(ctx, orderLine, correctedQuantity, order);
    }
    // const quantityWasAdjustedDown = correctedQuantity < quantity;
    const updatedOrder = await assertFound(this.orderService.findOne(ctx, order.id));
    return updatedOrder;
    // if (quantityWasAdjustedDown) {
    //   return new InsufficientStockError({quantityAvailable: correctedQuantity, order: updatedOrder});
    // } else {
    //   return updatedOrder;
    // }
  }

  /**
   * 从订单中移除商品 不需要重新计算优惠
   * @param ctx
   * @param orderId
   * @param orderLineId
   * @returns
   */
  async removeItemFromOrder(ctx: RequestContext, orderId: ID, orderLineId: ID, order?: Order) {
    if (!order) {
      order = await this.getOrderOrThrow(ctx, orderId);
    }
    if (order.state !== 'AddingItems' && order.state !== 'Draft') {
      return new OrderModificationError();
    }
    const orderLine = this.getOrderLineOrThrow(order, orderLineId);
    order.lines = order.lines.filter(line => !idsAreEqual(line.id, orderLineId));
    await this.connection.getRepository(ctx, OrderLine).remove(orderLine);
    this.eventBus.publish(new OrderLineEvent(ctx, order, orderLine, 'deleted'));
    return order;
  }

  /**
   * 添加商品到订单 不需要重新计算优惠
   * @param ctx
   * @param orderId
   * @param productVariantId
   * @param quantity
   * @param customFields
   * @returns
   */
  async addItemToOrder(
    ctx: RequestContext,
    orderId: ID,
    productVariantId: ID,
    quantity: number,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    customFields?: {[key: string]: any},
    order?: Order,
  ) {
    if (!order) {
      order = await this.getOrderOrThrow(ctx, orderId);
    }
    // orderLine的customFields没有使用关联 故不会多次查询
    const existingOrderLine = await this.orderModifier.getExistingOrderLine(ctx, order, productVariantId, customFields);
    const validationError =
      this.assertQuantityIsPositive(quantity) ||
      this.assertAddingItemsState(order) ||
      this.assertNotOverOrderItemsLimit(order, quantity) ||
      this.assertNotOverOrderLineItemsLimit(existingOrderLine, quantity);
    if (validationError) {
      return validationError;
    }
    const variant = await this.customerProductVariantService.getVariantByVariantId(ctx, productVariantId);
    if (!variant) {
      throw new OperationError('商品已失效或已下架');
    }
    if (variant.product.enabled === false) {
      throw new EntityNotFoundError('ProductVariant', productVariantId);
    }
    const correctedQuantity = await this.constrainQuantityToSaleable(
      ctx,
      variant,
      quantity,
      existingOrderLine?.quantity,
    );
    if (correctedQuantity === 0) {
      // throw new InsufficientStockError({quantityAvailable: correctedQuantity, order});
      throw new OperationError('商品已售尽,请重新选择商品');
    }
    // 实际的数量
    let actualQuantity = correctedQuantity;
    if (correctedQuantity < quantity) {
      const newQuantity = (existingOrderLine ? existingOrderLine?.quantity : 0) + correctedQuantity;
      actualQuantity = newQuantity;
    } else {
      actualQuantity = correctedQuantity;
    }
    if (actualQuantity === 0) {
      throw new OperationError('商品已售尽,请重新选择商品');
    }
    await this.getOrCreateOrderLine(ctx, order, productVariantId, actualQuantity, customFields, existingOrderLine);
    // const quantityWasAdjustedDown = correctedQuantity < quantity;
    const updatedOrder = await assertFound(this.orderService.findOne(ctx, order.id));
    return updatedOrder;
    // if (quantityWasAdjustedDown) {
    //   return new InsufficientStockError({quantityAvailable: correctedQuantity, order: updatedOrder});
    // } else {
    //   return updatedOrder;
    // }
  }

  async updateOrderLineQuantity(
    ctx: RequestContext,
    orderLine: OrderLine,
    quantity: number,
    order: Order,
  ): Promise<OrderLine> {
    // const currentQuantity = orderLine.quantity;
    orderLine.quantity = quantity;
    // 非活跃状态的订单才会进行多次查询 当前不需要
    // if (currentQuantity < quantity) {
    //   if (!order.active && order.state !== 'Draft') {
    //     await this.stockMovementService.createAllocationsForOrderLines(ctx, [
    //       {
    //         orderLineId: orderLine.id,
    //         quantity: quantity - currentQuantity,
    //       },
    //     ]);
    //   }
    // } else if (quantity < currentQuantity) {
    //   if (!order.active && order.state !== 'Draft') {
    //     await this.stockMovementService.createCancellationsForOrderLines(ctx, [{orderLineId: orderLine.id, quantity}]);
    //     await this.stockMovementService.createReleasesForOrderLines(ctx, [{orderLineId: orderLine.id, quantity}]);
    //   }
    // }
    await this.connection.getRepository(ctx, OrderLine).save(orderLine);
    this.eventBus.publish(new OrderLineEvent(ctx, order, orderLine, 'updated'));
    return orderLine;
  }

  async getOrCreateOrderLine(
    ctx: RequestContext,
    order: Order,
    productVariantId: ID,
    quantity: number,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    customFields?: {[key: string]: any},
    existingOrderLine?: OrderLine,
  ) {
    // const existingOrderLine = await this.orderModifier.getExistingOrderLine(ctx, order, productVariantId, customFields);
    if (existingOrderLine) {
      existingOrderLine.quantity = quantity;
      await this.connection.getRepository(ctx, OrderLine).update(existingOrderLine.id, {quantity: quantity});
      return existingOrderLine;
    }
    const productVariant = await this.customerProductVariantService.getVariantWithPriceAndTax(ctx, productVariantId);
    const orderLine = await this.connection.getRepository(ctx, OrderLine).save(
      new OrderLine({
        productVariant,
        taxCategory: productVariant.taxCategory,
        featuredAsset: productVariant.featuredAsset ?? productVariant.product.featuredAsset,
        listPrice: productVariant.listPrice,
        listPriceIncludesTax: false,
        adjustments: [],
        taxLines: [],
        customFields: customFields,
        quantity: quantity,
      }),
    );
    // const lineWithRelations = await this.connection.getEntityOrThrow(ctx, OrderLine, orderLine.id, {
    //   relations: ['taxCategory', 'productVariant', 'productVariant.productVariantPrices', 'productVariant.taxCategory'],
    // });
    // lineWithRelations.productVariant = this.translator.translate(
    //   await this.productVariantService.applyChannelPriceAndTax(lineWithRelations.productVariant, ctx, order),
    //   ctx,
    // );
    order.lines.push(orderLine);
    await this.connection.getRepository(ctx, Order).save(order, {reload: false});
    this.eventBus.publish(new OrderLineEvent(ctx, order, orderLine, 'created'));
    return orderLine;
  }

  async constrainQuantityToSaleable(
    ctx: RequestContext,
    variant: ProductVariant,
    quantity: number,
    existingQuantity = 0,
  ) {
    let correctedQuantity = quantity + existingQuantity;
    const saleableStockLevel = await this.customerProductVariantService.getSaleableStockLevel(ctx, variant);
    if (saleableStockLevel < correctedQuantity) {
      correctedQuantity = Math.max(saleableStockLevel - existingQuantity, 0);
    }
    return correctedQuantity;
  }

  getOrderLineOrThrow(order: Order, orderLineId: ID): OrderLine {
    const orderLine = order.lines.find(line => idsAreEqual(line.id, orderLineId));
    if (!orderLine) {
      throw new UserInputError(`error.order-does-not-contain-line-with-id`, {id: orderLineId});
    }
    return orderLine;
  }
  assertQuantityIsPositive(quantity: number) {
    if (quantity < 0) {
      return new NegativeQuantityError();
    }
  }

  assertAddingItemsState(order: Order) {
    if (order.state !== 'AddingItems' && order.state !== 'Draft') {
      return new OrderModificationError();
    }
  }

  /**
   * Throws if adding the given quantity would take the total order items over the
   * maximum limit specified in the config.
   */
  assertNotOverOrderItemsLimit(order: Order, quantityToAdd: number) {
    const currentItemsCount = summate(order.lines, 'quantity');
    const {orderItemsLimit} = this.configService.orderOptions;
    if (orderItemsLimit < currentItemsCount + quantityToAdd) {
      return new OrderLimitError({maxItems: orderItemsLimit});
    }
  }

  /**
   * Throws if adding the given quantity would exceed the maximum allowed
   * quantity for one order line.
   */
  assertNotOverOrderLineItemsLimit(orderLine: OrderLine | undefined, quantityToAdd: number) {
    const currentQuantity = orderLine?.quantity || 0;
    const {orderLineItemsLimit} = this.configService.orderOptions;
    if (orderLineItemsLimit < currentQuantity + quantityToAdd) {
      return new OrderLimitError({maxItems: orderLineItemsLimit});
    }
  }

  async getOrderOrThrow(ctx: RequestContext, orderId: ID): Promise<Order> {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new EntityNotFoundError('Order', orderId);
    }
    return order;
  }

  async merchantVoluntaryRefundByOrderLineId(ctx: RequestContext, orderLineId: ID) {
    const {sum} = await this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund')
      .select('SUM(merchantVoluntaryRefund.price)', 'sum')
      .andWhere('merchantVoluntaryRefund.orderLineId = :orderLineId', {orderLineId})
      .getRawOne();
    return sum || 0;
  }

  async applyPriceAdjustments(
    ctx: RequestContext,
    order: Order,
    updatedOrderLines?: OrderLine[],
    oldOrderPromotionResult?: OrderPromotionResult,
  ): Promise<Order> {
    if (order.orderPlacedAt || order.state === 'Cancelled') {
      return order;
    }
    this.calculateOrderTotals(order, 0);
    let adjustmentAmount = 0;
    if (order.lines.length > 0) {
      adjustmentAmount = await this.automaticActionService.getDiscountAmount(ctx, order, oldOrderPromotionResult);
    } else {
      const orderPromotionResult = await this.getResultByOrderId(ctx, order.id);
      if (orderPromotionResult?.promResult) {
        await this.connection
          .getRepository(ctx, OrderPromotionResult)
          .createQueryBuilder('orderPromotionResult')
          .update()
          .set({promResult: null})
          .where('orderId = :orderId', {orderId: order.id})
          .execute();
        await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
      }
    }
    this.calculateOrderTotals(order, adjustmentAmount);
    Logger.debug(`adjustmentAmount: ${adjustmentAmount}`);
    const isUpdateShipping = await this.applyShipping(ctx, order);
    this.calculateOrderTotals(order, adjustmentAmount);
    if (isUpdateShipping) {
      await this.updateShippingPromResult(ctx, order);
    }
    await this.connection
      .getRepository(ctx, Order)
      .save(omit(order, ['shippingAddress', 'billingAddress']), {reload: false});
    return assertFound(this.orderService.findOne(ctx, order.id));
  }
  async updateShippingPromResult(ctx: RequestContext, order: Order) {
    const orderPromResult = await this.getResultByOrderId(ctx, order.id);
    if (orderPromResult?.promResult) {
      let promResult = orderPromResult?.promResult;
      if (promResult) {
        promResult = await this.updatePromResult(ctx, promResult, order);
        orderPromResult.promResult = promResult;
        await this.connection.getRepository(ctx, OrderPromotionResult).save(orderPromResult);
      }
    }
  }

  public calculateOrderTotals(order: Order, adjustmentAmount: number) {
    let totalPrice = 0;
    let totalPriceWithTax = 0;

    for (const line of order.lines) {
      totalPrice += line.proratedLinePrice;
      totalPriceWithTax += line.proratedLinePriceWithTax;
    }
    totalPrice -= adjustmentAmount;
    totalPriceWithTax -= adjustmentAmount;
    for (const surcharge of order.surcharges) {
      totalPrice += surcharge.price;
      totalPriceWithTax += surcharge.priceWithTax;
    }

    order.subTotal = totalPrice;
    order.subTotalWithTax = totalPriceWithTax;

    let shippingPrice = 0;
    let shippingPriceWithTax = 0;
    for (const shippingLine of order.shippingLines) {
      shippingPrice += shippingLine.discountedPrice;
      shippingPriceWithTax += shippingLine.discountedPriceWithTax;
    }

    order.shipping = shippingPrice;
    order.shippingWithTax = shippingPriceWithTax;
  }

  private async applyShipping(ctx: RequestContext, order: Order) {
    const shippingLine = order.shippingLines[0];
    let isUpdateShipping = false;
    if (shippingLine) {
      // 是否需要重新计算运费
      const results = await this.calculatorGetEligibleShippingMethods(ctx, order);
      if (results?.length && order.lines?.length > 0) {
        const cheapest = results[0];
        if (NP.strip(shippingLine.listPrice) !== NP.strip(cheapest.result.price)) {
          isUpdateShipping = true;
        }
        shippingLine.listPrice = cheapest.result.price;
        shippingLine.listPriceIncludesTax = cheapest.result.priceIncludesTax;
        shippingLine.shippingMethod = cheapest.method;
        shippingLine.shippingMethodId = cheapest.method.id;
        shippingLine.taxLines = [
          {
            description: 'shipping tax',
            taxRate: cheapest.result.taxRate,
          },
        ];
        if (isUpdateShipping) {
          await this.connection.getRepository(ctx, ShippingLine).save(shippingLine);
        }
      } else {
        order.shippingLines = order.shippingLines.filter(sl => sl !== shippingLine);
        isUpdateShipping = true;
      }
    }
    return isUpdateShipping;
  }

  async calculatorGetEligibleShippingMethods(ctx: RequestContext, order: Order) {
    let eligibleMethods = await this.shippingCalculator.getEligibleShippingMethods(ctx, order);
    if (eligibleMethods.length > 0) {
      // priority存在的情况下按照优先级排序 不存在则按照运费价格排序 a存在b不存在 a优先级大于b
      eligibleMethods = eligibleMethods.sort((a, b) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const aPriority = (a.method.customFields as any).priority;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const bPriority = (b.method.customFields as any).priority;
        if (aPriority && bPriority) {
          return aPriority - bPriority;
        } else if (aPriority && !bPriority) {
          return -1;
        } else if (!aPriority && bPriority) {
          return 1;
        } else {
          return a.result.price - b.result.price;
        }
      });
    }
    return eligibleMethods;
  }

  async getEligibleShippingMethods(ctx: RequestContext, orderId: ID, order?: Order): Promise<ShippingMethodQuote[]> {
    if (!order) {
      order = await this.orderService.findOne(ctx, orderId);
      if (!order) {
        throw new Error('order empty');
      }
    }
    await this.updateOrderShippingType(ctx, order);
    let eligibleMethods = await this.calculatorGetEligibleShippingMethods(ctx, order);
    // 只返回一个配送方式
    eligibleMethods = eligibleMethods[0] ? [eligibleMethods[0]] : [];
    return eligibleMethods.map(eligible => {
      const {price, taxRate, priceIncludesTax, metadata} = eligible.result;
      return {
        id: eligible.method.id,
        price: priceIncludesTax ? netPriceOf(price, taxRate) : price,
        priceWithTax: priceIncludesTax ? price : grossPriceOf(price, taxRate),
        description: eligible.method.description,
        name: eligible.method.name,
        code: eligible.method.code,
        metadata,
        customFields: eligible.method.customFields,
      };
    });
  }
  async updateOrderShippingType(ctx: RequestContext, order: Order) {
    let shippingType = ShippingFeeType.Order;
    const orderPromResult = (order.customFields as OrderCustomFields).orderPromotionResult;
    const promResult = orderPromResult?.promResult;
    const blindBoxPromResult = promResult?.promLineResults?.find(prom => prom?.type === PromotionType.BlindBox);
    if (blindBoxPromResult) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const orderLineBlindBoxMap = (blindBoxPromResult as any)?.orderLineBlindBoxMap as {
        orderLineId: ID;
        blindBoxOrderBuyId: ID;
        blindBoxOpenRecordId: ID;
        blindBoxItemId: ID;
      }[];
      if (orderLineBlindBoxMap && orderLineBlindBoxMap.length >= 1) {
        // 是否走盲盒运费
        let isBlindBoxShipping = true;
        // 只要有一个orderLine不在活动内，就是普通运费
        for (const line of order.lines) {
          if (!orderLineBlindBoxMap.find(item => idsAreEqual(item.orderLineId, line.id))) {
            shippingType = ShippingFeeType.Order;
            isBlindBoxShipping = false;
            break;
          }
          // 如果order.lines的数量和orderLineBlindBoxMap的数量不一致，说明有商品不在活动内
          const orderLineCount = line.quantity;
          const orderLineBlindBoxMapCount = orderLineBlindBoxMap.filter(item =>
            idsAreEqual(item.orderLineId, line.id),
          ).length;
          if (orderLineCount !== orderLineBlindBoxMapCount) {
            shippingType = ShippingFeeType.Order;
            isBlindBoxShipping = false;
            break;
          }
        }
        if (isBlindBoxShipping) {
          const blindBoxOrderBuyIds = orderLineBlindBoxMap.map(item => item.blindBoxOrderBuyId);
          const blindBoxOrderBuys = await this.connection
            .getRepository(ctx, BlindBoxBuy)
            .createQueryBuilder('blindBoxBuy')
            .leftJoinAndSelect('blindBoxBuy.blindBoxActivity', 'blindBoxActivity')
            .andWhere('blindBoxBuy.id IN (:...blindBoxOrderBuyIds)', {blindBoxOrderBuyIds})
            .getMany();
          if (blindBoxOrderBuys && blindBoxOrderBuys.length >= 1) {
            for (const blindBoxOrderBuy of blindBoxOrderBuys) {
              const blindBoxActivity = blindBoxOrderBuy.blindBoxActivity;
              if (blindBoxActivity?.isFreeShipping) {
                const freeShippingThreshold = blindBoxActivity.freeShippingThreshold ?? 0;
                if (freeShippingThreshold <= 0) {
                  shippingType = ShippingFeeType.BlindBoxPayShipping;
                  break;
                } else {
                  // 找到对应的orderLine
                  const blindBoxOrderBuyId = blindBoxOrderBuy.id;
                  const orderLineBlindBoxMapItem = orderLineBlindBoxMap.find(item =>
                    idsAreEqual(item.blindBoxOrderBuyId, blindBoxOrderBuyId),
                  );
                  const orderLineId = orderLineBlindBoxMapItem?.orderLineId;
                  const orderLine = order.lines.find(line => idsAreEqual(line.id, orderLineId)) as OrderLine;
                  if (orderLine.proratedLinePrice > freeShippingThreshold) {
                    shippingType = ShippingFeeType.BlindBoxPayShipping;
                    break;
                  } else {
                    shippingType = ShippingFeeType.BlindBoxFreeShipping;
                  }
                }
              } else {
                shippingType = ShippingFeeType.BlindBoxPayShipping;
                // 结束循环
                break;
              }
            }
          }
        }
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (order.customFields as any).shippingFeeType = shippingType;
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      await this.connection.getRepository(ctx, Order).update(order.id, {
        customFields: {shippingFeeType: shippingType},
      });
    } catch (error) {
      Logger.error(`update order subscription error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async setShippingMethod(
    ctx: RequestContext,
    orderId: ID,
    shippingMethodIds: ID[],
    order?: Order,
  ): Promise<ErrorResultUnion<SetOrderShippingMethodResult, Order>> {
    if (!order) {
      order = await this.getOrderOrThrow(ctx, orderId);
    }
    const validationError = this.assertAddingItemsState(order);
    if (validationError) {
      return validationError;
    }
    const shippingMethodId = shippingMethodIds[0];
    const shippingMethod = await this.shippingCalculator.getMethodIfEligible(ctx, order, shippingMethodId);
    if (!shippingMethod) {
      return new IneligibleShippingMethodError();
    }
    let shippingLine: ShippingLine | undefined = order.shippingLines[0];
    if (shippingLine) {
      shippingLine.shippingMethod = shippingMethod;
    } else {
      shippingLine = await this.connection.getRepository(ctx, ShippingLine).save(
        new ShippingLine({
          shippingMethod,
          order,
          adjustments: [],
          listPrice: 0,
          listPriceIncludesTax: ctx.channel.pricesIncludeTax,
          taxLines: [],
        }),
      );
      order.shippingLines = [shippingLine];
    }
    // remove any now-unused ShippingLines
    if (shippingMethodIds.length < order.shippingLines.length) {
      const shippingLinesToDelete = order.shippingLines.splice(shippingMethodIds.length - 1);
      await this.connection.getRepository(ctx, ShippingLine).remove(shippingLinesToDelete);
    }
    // assign the ShippingLines to the OrderLines
    await this.connection
      .getRepository(ctx, OrderLine)
      .createQueryBuilder('line')
      .update({shippingLineId: shippingLine.id})
      .whereInIds(order.lines.map(l => l.id))
      .execute();
    return this.applyPriceAdjustments(ctx, order);
  }

  async createFulfillment(
    ctx: RequestContext,
    input: FulfillOrderInput,
  ): Promise<ErrorResultUnion<AddFulfillmentToOrderResult, Fulfillment>> {
    if (!input.lines || input.lines.length === 0 || summate(input.lines, 'quantity') === 0) {
      return new EmptyOrderLineSelectionError();
    }
    const orders = await getOrdersFromLines(ctx, this.connection, input.lines);

    if (await this.requestedFulfillmentQuantityExceedsLineQuantity(ctx, input)) {
      return new ItemsAlreadyFulfilledError();
    }
    // 检查库存
    // const stockCheckResult = await this.ensureSufficientStockForFulfillment(ctx, input);
    // if (isGraphQlErrorResult(stockCheckResult)) {
    //   return stockCheckResult;
    // }

    const fulfillment = await this.fulfillmentService.create(ctx, orders, input.lines, input.handler);
    if (isGraphQlErrorResult(fulfillment)) {
      return fulfillment;
    }

    await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder()
      .relation('fulfillments')
      .of(orders)
      .add(fulfillment);

    for (const order of orders) {
      await this.historyService.createHistoryEntryForOrder({
        ctx,
        orderId: order.id,
        type: HistoryEntryType.ORDER_FULFILLMENT,
        data: {
          fulfillmentId: fulfillment.id,
        },
      });
    }
    const result = await this.fulfillmentService.transitionToState(ctx, fulfillment.id, 'Pending');
    if (isGraphQlErrorResult(result)) {
      return result;
    }
    return result.fulfillment;
  }

  private async ensureSufficientStockForFulfillment(
    ctx: RequestContext,
    input: FulfillOrderInput,
  ): Promise<InsufficientStockOnHandError | undefined> {
    const lines = await this.connection.getRepository(ctx, OrderLine).find({
      where: {
        id: In(input.lines.map(l => l.orderLineId)),
      },
      relations: ['productVariant'],
    });

    for (const line of lines) {
      const lineInput = input.lines.find(l => idsAreEqual(l.orderLineId, line.id))!;

      const fulfillableStockLevel = await this.productVariantService.getFulfillableStockLevel(ctx, line.productVariant);
      if (fulfillableStockLevel < lineInput.quantity) {
        const {stockOnHand} = await this.stockLevelService.getAvailableStock(ctx, line.productVariant.id);
        const productVariant = this.translator.translate(line.productVariant, ctx);
        return new InsufficientStockOnHandError({
          productVariantId: productVariant.id as string,
          productVariantName: productVariant.name,
          stockOnHand,
        });
      }
    }
  }

  private async requestedFulfillmentQuantityExceedsLineQuantity(ctx: RequestContext, input: FulfillOrderInput) {
    const existingFulfillmentLines = await this.connection
      .getRepository(ctx, FulfillmentLine)
      .createQueryBuilder('fulfillmentLine')
      .leftJoinAndSelect('fulfillmentLine.orderLine', 'orderLine')
      .leftJoinAndSelect('fulfillmentLine.fulfillment', 'fulfillment')
      .where('fulfillmentLine.orderLineId IN (:...orderLineIds)', {
        orderLineIds: input.lines.map(l => l.orderLineId),
      })
      .andWhere('fulfillment.state != :state', {state: 'Cancelled'})
      .getMany();

    for (const inputLine of input.lines) {
      const existingFulfillmentLine = existingFulfillmentLines.find(l =>
        idsAreEqual(l.orderLineId, inputLine.orderLineId),
      );
      if (existingFulfillmentLine) {
        const unfulfilledQuantity = existingFulfillmentLine.orderLine.quantity - existingFulfillmentLine.quantity;
        if (unfulfilledQuantity < inputLine.quantity) {
          return true;
        }
      } else {
        const orderLine = await this.connection.getEntityOrThrow(ctx, OrderLine, inputLine.orderLineId);
        if (orderLine.quantity < inputLine.quantity) {
          return true;
        }
      }
    }
    return false;
  }

  idsContainsItem(ids: ID[], currentId: ID): boolean {
    return !!ids.find(id => idsAreEqual(id, currentId));
  }

  // 验证加价购商品是否符合条件
  // 1.订单中的加价购商品不符合条件需移除
  // 2.加价购勾选的商品是否存在于订单 如果不存在则添加
  // 3.如果修改了订单则需要重新计算价格
  async checkPurchasePremium(ctx: RequestContext, orderId: ID, order?: Order) {
    if (!order) {
      order = await this.getOrderOrThrow(ctx, orderId);
    }
    const lines = order.lines;
    const orderPromotionResult = await this.getResultByOrderId(ctx, orderId);
    // 1.订单中的加价购商品不符合条件需移除
    // 获取订单中全部的加价购商品
    const purchasePremiumLines = lines.filter(
      line => (line.customFields as OrderLineCustomFields)?.purchasePattern === PurchasePattern.PurchasePremium,
    );
    // 获取当前的加价购全部商品
    const markUpGifts = orderPromotionResult?.promResult?.gifts?.filter(
      gift => gift?.giftType === GiftType.MarkUp && gift?.items?.length,
    );
    // 是否变动
    let isUpdateOrder = false;
    // 获取当前选择的加价购商品markUpGifts中的items中selected为true的skuId
    const selectedMarkUps = markUpGifts
      ?.filter(gift => gift?.items?.find(item => item?.selected))
      ?.map(gift => gift?.items?.find(item => item?.selected));
    const selectedMarkUpSkuIds = selectedMarkUps?.map(item => item?.skuId as ID) as ID[];
    if (purchasePremiumLines?.length > 0) {
      for (const line of purchasePremiumLines) {
        const productVariantId = line.productVariant.id;
        const productVariant = await this.customerProductVariantService.getVariantByVariantId(ctx, productVariantId);
        if (!productVariant) {
          await this.removeItemFromOrder(ctx, orderId, line.id, order);
          isUpdateOrder = true;
          //需要使用idsAreEqual
        } else if (!this.idsContainsItem(selectedMarkUpSkuIds, productVariantId)) {
          await this.removePurchasePremiumToOrder(ctx, line.id, productVariantId, orderId, '', order);
          isUpdateOrder = true;
        }
      }
    }
    // 如果选择的加价购商品不在订单中则添加
    if (markUpGifts?.length) {
      for (const markUpGift of markUpGifts) {
        const items = markUpGift?.items;
        // 获取选择的SkuId
        const selectedSkuId = items?.find(item => item?.selected)?.skuId as ID;
        if (selectedSkuId) {
          // 判断purchasePremiumLines中是否存在
          const purchasePremiumLine = purchasePremiumLines.find(line =>
            idsAreEqual(line.productVariant.id, selectedSkuId),
          );
          // 判断订单项的promInstanceIds是否包含当前的promInstanceId
          if (!purchasePremiumLine) {
            await this.addPurchasePremiumToOrder(ctx, selectedSkuId, orderId, markUpGift?.promInstanceId as ID, order);
            isUpdateOrder = true;
          } else {
            const promInstanceIds =
              ((purchasePremiumLine?.customFields as OrderLineCustomFields)?.promInstanceIds as number[]) || [];
            if (!this.idsContainsItem(promInstanceIds, markUpGift?.promInstanceId as ID)) {
              promInstanceIds.push(Number(markUpGift?.promInstanceId));
              await this.addItemToOrder(
                ctx,
                orderId,
                selectedSkuId,
                1,
                {
                  purchasePattern: PurchasePattern.PurchasePremium,
                  promInstanceIds: promInstanceIds,
                },
                order,
              );
              isUpdateOrder = true;
            }
          }
        }
      }
    }

    if (isUpdateOrder) {
      return this.applyPriceAdjustments(ctx, order);
    }
    return order;
  }
}
