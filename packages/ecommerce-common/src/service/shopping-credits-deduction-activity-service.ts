import {Injectable, Logger} from '@nestjs/common';
import {CacheService, KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {VirtualCurrencyCode, VirtualCurrencySourceType} from '../generated-admin-types';
import {AbstractShoppingCredits, WeChatPaymentService} from '@scmally/wechat';
import {
  ChannelService,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Order,
  ProductService,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {ShoppingCreditsDeductionActivity} from '../entities';
import {OperationError} from '../error.type';
import {
  ActivityStatus,
  ApplicableProduct,
  DeletionResult,
  ProgramLinkInput,
  PromotionType,
  ShoppingCreditsDeductionActivityInput,
} from '../generated-admin-types';
import {ApplicableType, SymbolType} from '../generated-shop-types';
import {ActivityProduct, ActivityUtils} from '../utils';
import {CommonService} from './common.service';
import {CustomPromotionService} from './custom-promotion.service';
import {OrderPromotionResultService} from './order-promotion-result.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
@Injectable()
export class ShoppingCreditsDeductionActivityService extends AbstractShoppingCredits {
  async getShoppingCreditsDeductionLink(ctx: RequestContext, input: ProgramLinkInput) {
    const shoppingCreditsDeductionActivityId = input?.id;
    const shoppingCreditsDeductionActivity = await this.findOne(ctx, shoppingCreditsDeductionActivityId);
    if (!shoppingCreditsDeductionActivity) {
      throw new Error('活动不存在');
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(shoppingCreditsDeductionActivity.promotionId),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
    private listQueryBuilder: ListQueryBuilder,
    private virtualCurrencyService: VirtualCurrencyService,
    private redLockService: RedLockService,
    private promotionResultDetailService: PromotionResultDetailService,
    private customPromotionService: CustomPromotionService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private promotionService: PromotionService,
    private productService: ProductService,
    private channelService: ChannelService,
    private wechatPaymentService: WeChatPaymentService,
    private orderPromotionResultService: OrderPromotionResultService,
    private productCustomService: ProductCustomService,
    private commonService: CommonService,
  ) {
    super(wechatPaymentService);
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<ShoppingCreditsDeductionActivity>,
    relations?: RelationPaths<ShoppingCreditsDeductionActivity>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(ShoppingCreditsDeductionActivity, options, {
      relations: relations ?? ['promotion'],
      channelId: ctx.channelId,
      ctx,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          PromotionType.ShoppingCreditsClaim,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }

  @cacheableAccess({
    cacheKeyFn: (
      ctx: RequestContext,
      shoppingCreditsDeductionActivityId: ID,
      options?: ListQueryOptions<ShoppingCreditsDeductionActivity>,
      relations?: RelationPaths<ShoppingCreditsDeductionActivity>,
    ) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.shoppingCreditsDeductionActivity(
          shoppingCreditsDeductionActivityId,
          ctx.channelId,
        );
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    shoppingCreditsDeductionActivityId: ID,
    options?: ListQueryOptions<ShoppingCreditsDeductionActivity>,
    relations?: RelationPaths<ShoppingCreditsDeductionActivity>,
  ) {
    let shoppingCreditsDeductionActivity: ShoppingCreditsDeductionActivity[] | undefined;
    const memoryStorageCacheKey = CacheKeyManagerService.shoppingCreditsDeductionActivity(
      shoppingCreditsDeductionActivityId,
      ctx.channelId,
    );
    if (ctx.apiType === 'shop') {
      shoppingCreditsDeductionActivity = await this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!shoppingCreditsDeductionActivity) {
      const qb = this.listQueryBuilder.build(ShoppingCreditsDeductionActivity, options, {
        relations: [],
        channelId: ctx.channelId,
        ctx,
      });
      qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
      qb.andWhere(`${qb.alias}.id = :id`, {id: shoppingCreditsDeductionActivityId});
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      shoppingCreditsDeductionActivity = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, shoppingCreditsDeductionActivity);
      }
      if (shoppingCreditsDeductionActivity?.length > 0) {
        const shoppingCreditsDeductionActivityItem = shoppingCreditsDeductionActivity[0];
        const promotionId = shoppingCreditsDeductionActivityItem.promotionId;
        if (promotionId) {
          const promotion = await this.customPromotionService.getPromotionByPromotionId(ctx, promotionId);
          shoppingCreditsDeductionActivityItem.promotion = promotion as Promotion;
        }
        return shoppingCreditsDeductionActivityItem;
      }
    }
    return shoppingCreditsDeductionActivity[0];
  }

  async failureShoppingCreditsDeductionActivity(ctx: RequestContext, shoppingCreditsDeductionActivityId: ID) {
    const shoppingCreditsDeductionActivity = await this.findOne(ctx, shoppingCreditsDeductionActivityId);
    if (!shoppingCreditsDeductionActivity) {
      throw new Error('活动不存在');
    }
    if (shoppingCreditsDeductionActivity.status === ActivityStatus.Failure) {
      throw new Error('活动已失效');
    }
    shoppingCreditsDeductionActivity.status = ActivityStatus.Failure;
    const promotionId = shoppingCreditsDeductionActivity.promotionId;
    if (promotionId) {
      await this.connection.getRepository(ctx, Promotion).update(promotionId, {
        enabled: false,
      });
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsDeductionActivity).save(shoppingCreditsDeductionActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.shoppingCreditsDeductionActivity(shoppingCreditsDeductionActivityId, ctx.channelId),
      CacheKeyManagerService.promotion(promotionId as ID, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(promotionId as ID, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(
      ctx,
      shoppingCreditsDeductionActivity.applicableProduct,
    );
  }

  async deleteShoppingCreditsDeductionActivity(ctx: RequestContext, shoppingCreditsDeductionActivityId: ID) {
    const shoppingCreditsDeductionActivity = await this.findOne(ctx, shoppingCreditsDeductionActivityId);
    if (!shoppingCreditsDeductionActivity) {
      throw new Error('活动不存在');
    }
    shoppingCreditsDeductionActivity.deletedAt = new Date();
    shoppingCreditsDeductionActivity.status = ActivityStatus.Failure;
    const promotionId = shoppingCreditsDeductionActivity.promotionId;
    if (promotionId) {
      await this.connection.getRepository(ctx, Promotion).update(promotionId, {
        enabled: false,
      });
      await this.promotionService.softDeletePromotion(ctx, promotionId);
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsDeductionActivity).save(shoppingCreditsDeductionActivity);
    await this.cacheService.removeCache([
      CacheKeyManagerService.shoppingCreditsDeductionActivity(shoppingCreditsDeductionActivityId, ctx.channelId),
      CacheKeyManagerService.promotion(promotionId as ID, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(promotionId as ID, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(
      ctx,
      shoppingCreditsDeductionActivity.applicableProduct,
    );
    return {
      result: DeletionResult.Deleted,
      message: '删除成功',
    };
  }

  async upsertShoppingCreditsDeductionActivity(ctx: RequestContext, input: ShoppingCreditsDeductionActivityInput) {
    await this.validate(ctx, input);
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const shoppingCreditsDeductionActivity = await this.findOne(ctx, input.id);
      if (!shoppingCreditsDeductionActivity) {
        throw new Error('活动不存在');
      }
      oldApplicableProduct = shoppingCreditsDeductionActivity.applicableProduct;
    }
    let status = ActivityStatus.Normal;
    if (new Date() < new Date(input.startTime)) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date() > new Date(input.endTime)) {
      status = ActivityStatus.HaveEnded;
    }
    let shoppingCreditsDeductionActivity = new ShoppingCreditsDeductionActivity({
      ...(input as ShoppingCreditsDeductionActivity),
      status,
    });
    shoppingCreditsDeductionActivity = await this.channelService.assignToCurrentChannel(
      shoppingCreditsDeductionActivity,
      ctx,
    );
    shoppingCreditsDeductionActivity = await this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .save(shoppingCreditsDeductionActivity);
    const promotion = await this.upsertPromotion(ctx, shoppingCreditsDeductionActivity);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      input.applicableProduct as ApplicableProduct,
      oldApplicableProduct,
    );
    shoppingCreditsDeductionActivity.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsDeductionActivity).save(shoppingCreditsDeductionActivity);
    if (input.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.shoppingCreditsDeductionActivity(input.id, ctx.channelId),
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return this.findOne(ctx, shoppingCreditsDeductionActivity.id);
  }
  async upsertPromotion(ctx: RequestContext, shoppingCreditsDeductionActivity: ShoppingCreditsDeductionActivity) {
    const type = PromotionType.ShoppingCreditsDeduction;
    const promotionInput = {
      couponCode: shoppingCreditsDeductionActivity.promotion
        ? shoppingCreditsDeductionActivity.promotion.couponCode
        : generatePublicId(),
      name: shoppingCreditsDeductionActivity.displayName,
      startsAt: shoppingCreditsDeductionActivity.startTime,
      endsAt: shoppingCreditsDeductionActivity.endTime,
      enabled:
        shoppingCreditsDeductionActivity.status === ActivityStatus.Normal ||
        shoppingCreditsDeductionActivity.status === ActivityStatus.NotStarted,
      conditions: [],
      actions: [],
      customFields: {
        type: type,
        isAutomatic: false,
        activityName: shoppingCreditsDeductionActivity.displayName,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: shoppingCreditsDeductionActivity.displayName,
        },
      ],
    };
    shoppingCreditsDeductionActivity = (await this.findOne(
      ctx,
      shoppingCreditsDeductionActivity.id,
    )) as ShoppingCreditsDeductionActivity;
    if (shoppingCreditsDeductionActivity.promotionId) {
      const updatePromotion = {
        ...promotionInput,
        id: shoppingCreditsDeductionActivity.promotionId,
        customFields: {
          type: type,
          isAutomatic: false,
          stackingDiscountSwitch: shoppingCreditsDeductionActivity.stackingDiscountSwitch,
          stackingPromotionTypes: shoppingCreditsDeductionActivity.stackingPromotionTypes,
          activityName: shoppingCreditsDeductionActivity.displayName,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: type,
          isAutomatic: false,
          stackingDiscountSwitch: shoppingCreditsDeductionActivity.stackingDiscountSwitch,
          stackingPromotionTypes: shoppingCreditsDeductionActivity.stackingPromotionTypes,
          activityName: shoppingCreditsDeductionActivity.displayName,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: ShoppingCreditsDeductionActivityInput) {
    const {startTime, endTime} = input;
    if (!startTime || !endTime) {
      throw new Error('开始时间和结束时间不能为空');
    }
    if (startTime && endTime && startTime > endTime) {
      throw new Error('开始时间不能大于结束时间');
    }
    if ((input?.minimum ?? 0) < 0) {
      throw new Error('门槛不能小于0');
    }
    if ((input.deductionRate ?? 0) < 0 || (input.deductionRate ?? 0) > 100) {
      throw new Error('抵扣比例不能小于等于0或大于100');
    }
    const applicableProduct = input.applicableProduct;
    if (applicableProduct?.applicableType !== ApplicableType.All) {
      if (!applicableProduct?.productIds || applicableProduct.productIds.length === 0) {
        throw new Error('请选择商品');
      }
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      applicableType: input.applicableProduct?.applicableType as ApplicableType,
      productIds: input.applicableProduct?.productIds as ID[],
    };
    await this.verifyProductIdsAndShoppingCreditsDeductionActivity(ctx, inputProduct, input.id as ID);
  }
  async verifyProductIdsAndShoppingCreditsDeductionActivity(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    shoppingCreditsDeductionId: ID,
  ) {
    const shoppingCreditsDeductionActivities = await this.getNotFailureShoppingCreditsDeductionActivity(
      ctx,
      shoppingCreditsDeductionId,
    );
    for (const item of shoppingCreditsDeductionActivities) {
      const shoppingCreditsDeductionActivityProduct: ActivityProduct = {
        startTime: item.startTime,
        endTime: item.endTime,
        applicableType: item.applicableProduct.applicableType,
        productIds: item.applicableProduct.productIds as ID[],
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(
        shoppingCreditsDeductionActivityProduct,
        inputProduct,
      );
      if (
        productId &&
        // 判断活动时间是否冲突
        ActivityUtils.isTimeOverlappingWithOtherActivity(shoppingCreditsDeductionActivityProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和购物金抵扣活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和购物金抵扣活动商品冲突`);
      }
    }
  }
  async getNotFailureShoppingCreditsDeductionActivity(ctx: RequestContext, shoppingCreditsDeductionId: ID) {
    const qb = this.listQueryBuilder.build(
      ShoppingCreditsDeductionActivity,
      {},
      {
        relations: ['promotion'],
        channelId: ctx.channelId,
        ctx,
      },
    );
    if (shoppingCreditsDeductionId) {
      qb.andWhere(`${qb.alias}.id != :id`, {id: shoppingCreditsDeductionId});
    }
    // 活动状态不能为结束和失效
    qb.andWhere(`${qb.alias}.status not in (:status)`, {
      status: [ActivityStatus.HaveEnded, ActivityStatus.Failure],
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const shoppingCreditsDeductionActivity = await qb.getMany();
    return shoppingCreditsDeductionActivity;
  }

  async checkAndDeductShoppingCredits(ctx: RequestContext, order: Order, isAgain: boolean): Promise<boolean> {
    const lock = await this.redLockService.lockResource(
      `Customer—VirtualCurrency-${VirtualCurrencyCode.ShoppingCredits}-${order.id}-${ctx.channelId}`,
    );
    try {
      const customer = order.customer;
      if (!customer) {
        throw new Error('订单没有关联用户');
      }
      if (isAgain) {
        return true;
      }
      const orderPromResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
      if (!orderPromResult) {
        return true;
      }

      // 判断是否禁用购物金
      const isDisableShoppingCredits = orderPromResult.promResult?.disableShoppingCredits;
      if (isDisableShoppingCredits) {
        return true;
      }
      // 判断购物金抵扣数量
      const shoppingCreditsDeductionCount = orderPromResult.promResult?.shoppingCreditsDeduction;
      if (!shoppingCreditsDeductionCount || shoppingCreditsDeductionCount <= 0) {
        return true;
      }
      // 获取用户购物金
      const customerId = customer.id;
      const customerShoppingCredits = await this.virtualCurrencyService.getBalance(
        ctx,
        VirtualCurrencyCode.ShoppingCredits,
        customerId,
      );
      if (customerShoppingCredits <= 0 || customerShoppingCredits < shoppingCreditsDeductionCount) {
        throw new OperationError('购物金不足');
      }
      // 扣除购物金
      await this.virtualCurrencyService.subtractCurrency(
        ctx,
        VirtualCurrencyCode.ShoppingCredits,
        shoppingCreditsDeductionCount,
        '购物金抵扣-订单',
        VirtualCurrencySourceType.OrderDeduction,
        order.id as string,
      );
      return true;
    } catch (error) {
      Logger.error('购物金抵扣失败', error);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async cancelOrderShoppingCreditsDeductionActivity(ctx: RequestContext, order: Order) {
    const orderId = order.id;
    const lock = await this.redLockService.lockResource(
      `Customer—VirtualCurrency-${VirtualCurrencyCode.ShoppingCredits}-${orderId}-${ctx.channelId}`,
    );
    try {
      const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
      if (!promotionResult) {
        return;
      }
      const shoppingCreditsDeductionCount = promotionResult.promResult?.shoppingCreditsDeduction;
      if (!shoppingCreditsDeductionCount || shoppingCreditsDeductionCount <= 0) {
        return;
      }
      // 获取购物金发放记录
      const history = await this.virtualCurrencyService.queryHistoryExist(
        ctx,
        SymbolType.Out,
        VirtualCurrencyCode.ShoppingCredits,
        VirtualCurrencySourceType.OrderDeduction,
        orderId as string,
        order.customerId as string,
      );
      if (!history) {
        return;
      }
      // 购物金回退
      await this.virtualCurrencyService.updateCurrency(
        ctx,
        VirtualCurrencyCode.ShoppingCredits,
        shoppingCreditsDeductionCount,
        SymbolType.In,
        '购物金抵扣退回-订单取消',
        VirtualCurrencySourceType.Returned,
        orderId as string,
        order.customerId as string,
      );
    } catch (error) {
      Logger.error('取消订单回退抵扣购物金失败', error);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  // 获取每个订单项的购物金抵扣金额
  async getOrderItemShoppingCreditsDeduction(ctx: RequestContext, orderId: ID, merchantVoluntaryRefundId?: ID) {
    const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    if (!promotionResult) {
      return [];
    }
    const orderLinePromResults = promotionResult?.promResult?.orderLinePromResults;
    if (!orderLinePromResults) {
      return [];
    }
    // 先判断是否已经存在购物金抵扣
    const deductionHistory = await this.virtualCurrencyService.queryHistoryExist(
      ctx,
      SymbolType.Out,
      VirtualCurrencyCode.ShoppingCredits,
      VirtualCurrencySourceType.OrderDeduction,
      orderId as string,
      promotionResult.order.customerId as string,
    );
    if (!deductionHistory) {
      return [];
    }
    const orderItemShoppingCreditsDeduction = orderLinePromResults.map(item => {
      const orderLineId = item?.orderLineId;
      const discountDetails = item?.discountDetails;
      const shoppingCreditsDeduction = discountDetails?.find(
        detail => detail?.type === PromotionType.ShoppingCreditsDeduction,
      );
      const shoppingCreditsDeductionAmount = shoppingCreditsDeduction?.discountAmount;
      return {
        orderLineId,
        shoppingCreditsDeductionAmount,
      };
    });
    //获取已经售后成功或者已经主动退款的订单项id
    const orderLineIds = await this.orderPromotionResultService.getRefundedOrderLineIds(
      ctx,
      orderId,
      merchantVoluntaryRefundId,
    );
    const orderLineIdSet = new Set(orderLineIds.map(id => id?.toString()));
    const shoppingCreditsDeductionActivitiesFilter = orderItemShoppingCreditsDeduction.filter(
      item => !orderLineIdSet.has(item.orderLineId?.toString() ?? ''),
    );
    return shoppingCreditsDeductionActivitiesFilter;
  }

  mergeShoppingCredits(
    claimData: {
      orderLineId: string | null | undefined;
      shoppingCreditsClaim: number | null | undefined;
    }[],
    deductionData: {
      orderLineId: string | undefined | null;
      shoppingCreditsDeductionAmount: number | undefined | null;
    }[],
  ): {
    orderLineId: string;
    shoppingCreditsClaimAmount: number;
    shoppingCreditsDeductionAmount: number;
  }[] {
    const mergedMap = new Map<
      string,
      {
        orderLineId: string;
        shoppingCreditsClaimAmount: number;
        shoppingCreditsDeductionAmount: number;
      }
    >();
    // 处理 claimData
    claimData.forEach(({orderLineId, shoppingCreditsClaim}) => {
      if (orderLineId) {
        mergedMap.set(orderLineId, {
          orderLineId,
          shoppingCreditsClaimAmount: shoppingCreditsClaim ?? 0,
          shoppingCreditsDeductionAmount: 0, // 默认值为 0
        });
      }
    });
    // 处理 deductionData
    deductionData.forEach(({orderLineId, shoppingCreditsDeductionAmount}) => {
      if (orderLineId) {
        if (mergedMap.has(orderLineId)) {
          // 如果已存在该orderLineId，更新 deductionAmount
          const existing = mergedMap.get(orderLineId)!;
          existing.shoppingCreditsDeductionAmount = shoppingCreditsDeductionAmount ?? 0;
        } else {
          // 如果该 orderLineId 不在 mergedMap 中，创建新项
          mergedMap.set(orderLineId, {
            orderLineId,
            shoppingCreditsClaimAmount: 0, // 默认值为 0
            shoppingCreditsDeductionAmount: shoppingCreditsDeductionAmount ?? 0,
          });
        }
      }
    });
    // 返回合并后的数据
    return Array.from(mergedMap.values());
  }

  async activitySynopsis(ctx: RequestContext, shoppingCreditsDeductionActivity: ShoppingCreditsDeductionActivity) {
    if (shoppingCreditsDeductionActivity.minimum > 0) {
      return `满${shoppingCreditsDeductionActivity.minimum / 100}元可使用购物金抵扣百分之${
        shoppingCreditsDeductionActivity.deductionRate
      }的金额`;
    } else {
      return `购物金最高抵扣百分之${shoppingCreditsDeductionActivity.deductionRate}的金额`;
    }
  }
  async activityContent(ctx: RequestContext, shoppingCreditsDeductionActivity: ShoppingCreditsDeductionActivity) {
    if (shoppingCreditsDeductionActivity.minimum > 0) {
      return [
        `满${shoppingCreditsDeductionActivity.minimum / 100}元可使用购物金抵扣百分之${
          shoppingCreditsDeductionActivity.deductionRate
        }的金额`,
      ];
    } else {
      return [`购物金最高抵扣百分之${shoppingCreditsDeductionActivity.deductionRate}的金额`];
    }
  }

  async shoppingCreditExchangeStateChangeAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.shoppingCreditsDeductionStateChange(ctx);
    }
  }
  async shoppingCreditsDeductionStateChange(ctx: RequestContext) {
    const now = new Date();
    const notStarted = await this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .createQueryBuilder('shoppingCreditsDeductionActivity')
      .leftJoinAndSelect('shoppingCreditsDeductionActivity.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('shoppingCreditsDeductionActivity.deletedAt IS NULL')
      .andWhere('shoppingCreditsDeductionActivity.startTime <= :now', {now})
      .andWhere('shoppingCreditsDeductionActivity.status = :status', {
        status: ActivityStatus.NotStarted,
      })
      .getMany();
    if (notStarted.length > 0) {
      const notStartedIds = notStarted.map(item => item.id);
      const notStartedPromotionIds = notStarted.map(item => item.promotionId);
      if (notStartedIds && notStartedIds.length > 0) {
        await this.commonService.activeOrderTime(ctx, [ctx.channelId]);
        await this.connection
          .getRepository(ctx, ShoppingCreditsDeductionActivity)
          .update({id: In(notStartedIds)}, {status: ActivityStatus.Normal});
        const notStartedKey = notStarted.map(item =>
          CacheKeyManagerService.shoppingCreditsDeductionActivity(item.id, ctx.channelId),
        );
        await this.cacheService.removeCache(notStartedKey);
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        notStarted.forEach(async item => {
          await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
        });
      }
      if (notStartedPromotionIds && notStartedPromotionIds.length > 0) {
        await this.connection.getRepository(ctx, Promotion).update({id: In(notStartedPromotionIds)}, {enabled: true});
        const notStartedPromotionKey = [
          ...notStartedPromotionIds.map(item => CacheKeyManagerService.promotion(item as ID, ctx.channelId)),
          ...notStartedPromotionIds.map(item =>
            CacheKeyManagerService.promotionIncludeFailure(item as ID, ctx.channelId),
          ),
        ];
        await this.cacheService.removeCache(notStartedPromotionKey);
      }
    }
    const haveEnded = await this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .createQueryBuilder('shoppingCreditsDeductionActivity')
      .leftJoinAndSelect('shoppingCreditsDeductionActivity.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('shoppingCreditsDeductionActivity.deletedAt IS NULL')
      .andWhere('shoppingCreditsDeductionActivity.endTime <= :now', {now})
      .andWhere('shoppingCreditsDeductionActivity.status = :status', {
        status: ActivityStatus.Normal,
      })
      .getMany();
    if (haveEnded.length > 0) {
      const haveEndedIds = haveEnded.map(item => item.id);
      const haveEndedPromotionIds = haveEnded.map(item => item.promotionId);
      if (haveEndedIds && haveEndedIds.length > 0) {
        await this.commonService.activeOrderTime(ctx, [ctx.channelId]);
        await this.connection
          .getRepository(ctx, ShoppingCreditsDeductionActivity)
          .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
        const haveEndedKey = haveEnded.map(item =>
          CacheKeyManagerService.shoppingCreditsDeductionActivity(item.id, ctx.channelId),
        );
        await this.cacheService.removeCache(haveEndedKey);
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        haveEnded.forEach(async item => {
          await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
        });
      }
      if (haveEndedPromotionIds && haveEndedPromotionIds.length > 0) {
        await this.connection.getRepository(ctx, Promotion).update({id: In(haveEndedPromotionIds)}, {enabled: false});
        const haveEndedPromotionKey = [
          ...haveEndedPromotionIds.map(item => CacheKeyManagerService.promotion(item as ID, ctx.channelId)),
          ...haveEndedPromotionIds.map(item =>
            CacheKeyManagerService.promotionIncludeFailure(item as ID, ctx.channelId),
          ),
        ];
        await this.cacheService.removeCache(haveEndedPromotionKey);
      }
    }
  }
}
