import {Injectable} from '@nestjs/common';
import {cacheableAccess, KvsService, MemoryStorageService, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Product,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PackageDiscount} from '../entities';
import {
  ActivityStatus,
  ApplicableProduct,
  ApplicableType,
  DeletionResult,
  PackageDiscountInput,
  ProductCustomFields,
  ProgramLinkInput,
  PromotionType,
} from '../generated-admin-types';
import {packageDiscountAction} from '../promotion/action';
import {customerGroupList, packageDiscountConditions} from '../promotion/conditions';
import {ActivityProduct} from '../utils';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {FullDiscountPresentService} from './full-discount-present.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
@Injectable()
export class PackageDiscountService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private fullDiscountPresentService: FullDiscountPresentService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private kvsService: KvsService,
    private promotionService: PromotionService,
    private promotionResultDetailService: PromotionResultDetailService,
    private commonService: CommonService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
  ) {}

  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<PackageDiscount>,
    relations: RelationPaths<PackageDiscount>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(PackageDiscount, options, {
      relations: (relations ?? []).concat(['promotion']),
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere('packageDiscount.deletedAt is null');
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          PromotionType.PackageDiscount,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }
  @cacheableAccess({
    cacheKeyFn: (ctx: RequestContext, id: ID) => {
      return CacheKeyManagerService.packageDiscount(id, ctx.channelId);
    },
  })
  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<PackageDiscount>,
    relations?: RelationPaths<PackageDiscount>,
  ) {
    const memoryStorageCacheKey = CacheKeyManagerService.packageDiscount(id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData as PackageDiscount;
      }
    }
    const qb = this.listQueryBuilder.build(PackageDiscount, options, {
      relations: [], //relations,
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere('packageDiscount.id = :id', {id});
    qb.andWhere('packageDiscount.deletedAt is null');
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const packageDiscounts = await qb.getMany();
    if (packageDiscounts.length === 0) return null;
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, packageDiscounts[0]);
    }
    return packageDiscounts[0];
  }

  async failurePackageDiscount(ctx: RequestContext, id: ID) {
    const packageDiscount = await this.findOne(ctx, id);
    if (!packageDiscount) {
      throw new Error('未找到该活动');
    }
    if (packageDiscount.status === ActivityStatus.Failure) return new Error('该活动已失效');
    packageDiscount.status = ActivityStatus.Failure;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PackageDiscount).save(packageDiscount);
    await this.connection.getRepository(ctx, Promotion).update(packageDiscount.promotion.id, {
      enabled: false,
    });
    await this.cacheService.removeCache([
      CacheKeyManagerService.packageDiscount(id, ctx.channelId),
      CacheKeyManagerService.promotion(packageDiscount?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(packageDiscount?.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, {
      applicableType: ApplicableType.AvailableGoods,
      productIds: packageDiscount.productIds as string[],
    });
    return this.findOne(ctx, id);
  }

  async deletePackageDiscount(ctx: RequestContext, id: ID) {
    const packageDiscount = await this.findOne(ctx, id);
    if (!packageDiscount) {
      throw new Error('未找到该活动');
    }
    packageDiscount.deletedAt = new Date();
    packageDiscount.status = ActivityStatus.Failure;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PackageDiscount).save(packageDiscount);
    await this.connection.getRepository(ctx, Promotion).update(packageDiscount.promotion.id, {
      enabled: false,
    });
    await this.promotionService.softDeletePromotion(ctx, packageDiscount.promotion.id);
    await this.cacheService.removeCache([
      CacheKeyManagerService.packageDiscount(id, ctx.channelId),
      CacheKeyManagerService.promotion(packageDiscount?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(packageDiscount?.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, {
      applicableType: ApplicableType.AvailableGoods,
      productIds: packageDiscount.productIds as string[],
    });
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  async upsertPackageDiscount(ctx: RequestContext, input: PackageDiscountInput) {
    await this.validate(ctx, input);
    let status = ActivityStatus.Normal;
    if (input.startTime > new Date()) {
      status = ActivityStatus.NotStarted;
    }
    if (input.endTime < new Date()) {
      status = ActivityStatus.HaveEnded;
    }
    // 修改前的可用商品
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const packageDiscount = await this.findOne(ctx, input.id);
      if (!packageDiscount) {
        throw new EntityNotFoundError('活动不存在', input.id);
      }
      oldApplicableProduct = {
        applicableType: ApplicableType.AvailableGoods,
        productIds: packageDiscount.productIds as string[],
      };
    }
    let packageDiscount = new PackageDiscount({
      ...(input as PackageDiscount),
      status,
    });
    packageDiscount = await this.channelService.assignToCurrentChannel(packageDiscount, ctx);
    packageDiscount = await this.connection.getRepository(ctx, PackageDiscount).save(packageDiscount);
    const promotion = await this.upsertPromotion(ctx, packageDiscount);
    packageDiscount.promotion = promotion;
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      {
        applicableType: ApplicableType.AvailableGoods,
        productIds: packageDiscount.productIds as string[],
      },
      oldApplicableProduct,
    );
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PackageDiscount).save(packageDiscount);
    if (input.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.packageDiscount(packageDiscount.id, ctx.channelId),
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return packageDiscount;
  }
  async upsertPromotion(ctx: RequestContext, packageDiscount: PackageDiscount): Promise<Promotion> {
    const promotionInput = {
      couponCode: packageDiscount.promotion ? packageDiscount.promotion.couponCode : generatePublicId(),
      name: packageDiscount.name,
      enabled: packageDiscount.status === ActivityStatus.Normal || packageDiscount.status === ActivityStatus.NotStarted,
      startsAt: packageDiscount.startTime,
      endsAt: packageDiscount.endTime,
      conditions: [],
      actions: [],
      customFields: {
        type: PromotionType.PackageDiscount,
        isAutomatic: true,
        activityName: packageDiscount.displayName,
        stackingDiscountSwitch: packageDiscount.stackingDiscountSwitch,
        stackingPromotionTypes: packageDiscount.stackingPromotionTypes,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: packageDiscount.displayName,
        },
      ],
    };
    promotionInput.conditions.push({
      code: packageDiscountConditions.code,
      arguments: [
        {
          name: 'productIds',
          value: packageDiscount.productIds,
        },
        {
          name: 'quantity',
          value: packageDiscount.selectCount,
        },
      ],
    } as never);
    promotionInput.actions.push({
      code: packageDiscountAction.code,
      arguments: [
        {
          name: 'price',
          value: packageDiscount.price,
        },
        {
          name: 'quantity',
          value: packageDiscount.selectCount,
        },
        {
          name: 'productIds',
          value: packageDiscount.productIds,
        },
      ],
    } as never);
    if (packageDiscount.whetherRestrictUsers) {
      //是否限制参与用户
      promotionInput.conditions.push({
        code: customerGroupList.code,
        arguments: [
          {name: 'isOpen', value: packageDiscount.whetherRestrictUsers},
          {name: 'groupType', value: packageDiscount.groupType},
          {name: 'customerGroupIds', value: packageDiscount.memberPlanIds},
        ],
      } as never);
    }
    packageDiscount = (await this.findOne(ctx, packageDiscount.id as ID, {}, ['promotion'])) as PackageDiscount;
    if (packageDiscount?.promotion) {
      const updatePromotion = {
        ...promotionInput,
        id: packageDiscount.promotion.id,
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: PromotionType.PackageDiscount,
          isAutomatic: true,
          activityName: packageDiscount.displayName,
          stackingDiscountSwitch: packageDiscount.stackingDiscountSwitch,
          stackingPromotionTypes: packageDiscount.stackingPromotionTypes,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: PackageDiscountInput) {
    if (input.startTime > input.endTime) {
      throw new Error('开始时间不能大于结束时间');
    }
    if (input.price < 0) {
      throw new Error('打包一口价金额不能小于0');
    }
    if (input.selectCount < 0) {
      throw new Error('可挑选商品数量不能小于0');
    }
    if (input.price < input.selectCount) {
      throw new Error('打包一口价金额不能小于可挑选商品数量');
    }
    if (input.productIds.length === 0) {
      throw new Error('请选择活动商品');
    }
    if (input.stackingDiscountSwitch && input.stackingPromotionTypes?.length === 0) {
      throw new Error('请选择叠加活动');
    }
    const products = await this.connection.getRepository(ctx, Product).find({
      where: {
        id: In(input.productIds),
      },
    });
    if (products.length !== input.productIds.length) {
      throw new Error('商品不存在');
    }
    for (const product of products) {
      if (product.deletedAt) {
        throw new Error('商品已删除');
      }
      const productPrice = (product.customFields as ProductCustomFields).price ?? 0;
      if (productPrice * input.selectCount < input.price) {
        throw new Error('打包一口价金额不能大于可挑选商品最低价之和');
      }
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      productIds: input.productIds,
      applicableType: ApplicableType.AvailableGoods,
    };
    //TODO 验证活动商品和满减活动商品是否有重复 2024-01-17 产品要求去除打包一口价和满减送的天然互斥
    // await this.fullDiscountPresentService.verifyProductIdsAndFullDiscountConflict(ctx, inputProduct);
    //TODO 验证活动商品和打包一口价活动商品是否有重复
    await this.fullDiscountPresentService.verifyProductIdsAndPackageDiscountConflict(ctx, inputProduct, input.id as ID);
    //TODO 验证活动商品和限时折扣活动商品是否有重复
    await this.fullDiscountPresentService.verifyProductIdsAndDiscountActivityConflict(ctx, inputProduct);
  }
  activitySynopsis(ctx: RequestContext, packageDiscount: PackageDiscount) {
    return `以下商品${packageDiscount.price / 100}元任选${packageDiscount.selectCount}件`;
  }
  activityContent(ctx: RequestContext, packageDiscount: PackageDiscount) {
    return [`活动指定商品内,任选${packageDiscount.selectCount}件仅需${packageDiscount.price / 100}元`];
  }

  async getPackageDiscountLink(ctx: RequestContext, input: ProgramLinkInput) {
    const packageDiscountId = input.id;
    const packageDiscount = await this.findOne(ctx, packageDiscountId);
    if (!packageDiscount) {
      throw new Error('未找到该活动');
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(packageDiscount.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }

  async getPackageDiscountQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const packageDiscountId = input.id;
    const packageDiscount = await this.findOne(ctx, packageDiscountId);
    if (!packageDiscount) {
      throw new Error('未找到该活动');
    }
    if (packageDiscount.smallProgramQRCodeLink) {
      return packageDiscount.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(packageDiscount.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    packageDiscount.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, PackageDiscount).save(packageDiscount);
    await this.cacheService.removeCache([
      CacheKeyManagerService.packageDiscount(packageDiscount.id, ctx.channelId),
      CacheKeyManagerService.promotion(packageDiscount.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(packageDiscount.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }
}
