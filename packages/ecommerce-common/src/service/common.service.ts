import {Injectable} from '@nestjs/common';
import {KvsService, CacheKeyManagerService} from '@scmally/kvs';
import {
  GiftCardOrder,
  GiftCardService,
  Member,
  MemberService,
  MembershipOrder,
  MembershipPlan,
  MembershipPlanService,
} from '@scmally/member';
import {MinioService} from '@scmally/minio';
import {AbstractCommon, WeChatAuthService, WeChatConfigService, WeChatPaymentService} from '@scmally/wechat';
import {getValidFormat} from '@vendure/asset-server-plugin/lib/src/common';
import {transformImage} from '@vendure/asset-server-plugin/lib/src/transform-image';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  Asset,
  Channel,
  ConfigService,
  Customer,
  EntityNotFoundError,
  ID,
  JobQueueService,
  LanguageCode,
  Logger,
  Order,
  Payment,
  Product,
  ProductService,
  ProductVariant,
  RequestContext,
  RequestContextCacheService,
  RequestContextService,
  TransactionalConnection,
  UnauthorizedError,
  UserInputError,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {JobRecord} from '@vendure/core/dist/plugin/default-job-queue-plugin/job-record.entity';
import {createHash} from 'crypto';
import {fromBuffer} from 'file-type';
import {DateTime} from 'luxon';
import NP from 'number-precision';
import path from 'path';
import QRCode from 'qrcode';
import request from 'request';
import {Brackets, IsNull, LessThan, Not} from 'typeorm';
import {
  CustomPage,
  DistributorRecord,
  NanoDate,
  OrderLinePromotionDetail,
  OrderTracking,
  ShoppingCreditsClaimActivity,
  ShoppingCreditsDeductionActivity,
  SystemDailyStats,
  SystemHourlyStats,
  TemplateConfig,
} from '../entities';
import {
  AssetCustomFields,
  CustomPageStatistics,
  DateTimeType,
  MemberCardDiscountLimitSourceType,
  ProductStatistics,
  ProductVariantStatistics,
  ProgramLinkInput,
  ShareData,
  TrendSource,
  TrendType,
} from '../generated-admin-types';
import {InterfaceAfterSale} from './abstract-after-sale';
import {MatomoService} from './matomo.service';
import {OrderTrackingService} from './order-tracking.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
// import sharp from 'sharp';
// const sharp = require('sharp');
// const sizeOf = require('image-size');
import {ForumTag} from '@scmally/forum';
import {ImageTransformPreset} from '@vendure/asset-server-plugin/lib/src/types';
import fs from 'fs';
import sizeOf from 'image-size';
import {OperationError} from '../error.type';
import {
  LocationData,
  PaymentOrderType,
  ProductVariantCustomFields,
  TemplateType,
  VirtualTargetType,
} from '../generated-shop-types';
import {CacheService} from './cache.service';
import {SettingService} from './setting.service';
@Injectable()
export class CommonService extends AbstractCommon {
  interfaceAfterSale: InterfaceAfterSale;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  constructor(
    private connection: TransactionalConnection,
    public weChatAuthService: WeChatAuthService,
    private weChatConfigService: WeChatConfigService,
    private minioService: MinioService,
    private membershipPlanService: MembershipPlanService,
    private productService: ProductService,
    private requestContextService: RequestContextService,
    private requestContextCacheService: RequestContextCacheService,
    private promotionResultDetailService: PromotionResultDetailService,
    private kvsService: KvsService,
    private giftCardService: GiftCardService,
    public weChatPaymentService: WeChatPaymentService,
    private orderTrackingService: OrderTrackingService,
    private matomoService: MatomoService,
    private configService: ConfigService,
    private jobQueueService: JobQueueService,
    private memberService: MemberService,
    private cacheService: CacheService,
    private settingService: SettingService,
  ) {
    super(weChatPaymentService, weChatAuthService);
    this.data = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../../../../apps/server/static/regions.json'), 'utf8'),
    );
  }

  readonly presets: ImageTransformPreset[] = [
    {name: 'tiny', width: 50, height: 50, mode: 'crop'},
    {name: 'thumb', width: 150, height: 150, mode: 'crop'},
    {name: 'small', width: 300, height: 300, mode: 'resize'},
    {name: 'medium', width: 500, height: 500, mode: 'resize'},
    {name: 'large', width: 800, height: 800, mode: 'resize'},
  ];
  readonly cacheDir = 'cache';

  async getRegionsJsonFromArray(
    values: string[],
    level = 0,
    data: LocationData[] = this.data,
  ): Promise<LocationData[]> {
    // 限制购买的省份
    const limitProvince = ['台湾省', '香港特别行政区', '澳门特别行政区'];
    // 如果层级超出了 values 数组长度，则返回空数组
    if (level >= values.length) {
      return data
        .filter(({text}) => !limitProvince.includes(text as string))
        .map(({value, text}) => ({value, text, children: []}));
    }
    // 查找当前层级对应的节点
    const currentValue = values[level];
    const currentNode = data.find(({text}) => text === currentValue);
    if (!currentNode) {
      // 如果当前节点不存在，则返回该层级的数据（例如返回省列表）
      return data
        .filter(({text}) => !limitProvince.includes(text as string))
        .map(({value, text}) => ({value, text, children: []}));
    }

    // 如果当前节点是台湾省，直接返回空数组
    if (limitProvince.includes(currentNode.text as string)) {
      return [];
    }

    // 构建当前层级的返回结果，递归获取下一层级的数据
    const children = await this.getRegionsJsonFromArray(
      values,
      level + 1,
      (currentNode.children || []) as LocationData[],
    );
    return data
      .filter(({text}) => !limitProvince.includes(text as string))
      .map(({value, text}) => ({
        value,
        text,
        children: value === currentNode.value ? children : [],
      }));
  }

  async clearTaskRecord() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    await this.connection.getRepository(adminCtx, JobRecord).delete({
      isSettled: true,
    });
  }
  async checkWhetherTheUserIsAMember(ctx: RequestContext, order: Order): Promise<boolean> {
    const orderLines = order.lines;
    for (const orderLine of orderLines) {
      const productVariant = orderLine.productVariant;
      const productVariantCustomFields = productVariant?.customFields as ProductVariantCustomFields;
      if (productVariantCustomFields.virtualTargetType === VirtualTargetType.MemberCard) {
        const membershipPlanId = productVariantCustomFields.virtualTargetId;
        if (!membershipPlanId) {
          throw new OperationError('会员卡不存在');
        }
        const isMember = await this.memberService.checkWhetherTheUserIsAMember(ctx, membershipPlanId);
        if (isMember) {
          throw new OperationError('您已经是其他会员或存在会员未激活');
        }
      }
    }
    return true;
  }

  async bindingCartOrderToOutrightPurchase(ctx: RequestContext, orderId: ID): Promise<boolean> {
    return this.orderTrackingService.bindingCartOrderToOutrightPurchase(ctx, orderId);
  }
  async generateH5LinkByCustomPage(ctx: RequestContext, input: ProgramLinkInput) {
    const customPage = await this.connection.getRepository(ctx, CustomPage).findOne({
      where: {id: input.id},
      relations: ['channels'],
    });
    if (!customPage) {
      throw new Error('CustomPage not found');
    }
    let h5Link = customPage.h5Link;
    let h5QRCode = customPage.h5QRCode;
    if (h5Link && h5QRCode) {
      return {
        h5Link: customPage.h5Link,
        h5QRCode: customPage.h5QRCode,
      };
    }
    if (!h5Link) {
      h5Link = await this.generateH5Link(ctx, input, input.path ?? '');
    }
    if (!h5QRCode) {
      const buffer = await QRCode.toBuffer(h5Link);
      const fileTypeResult = await fromBuffer(buffer);
      if (!fileTypeResult) {
        throw new Error('Incorrect buffer type ');
      }
      h5QRCode = await this.minioService.uploadByStream(
        process.env.SHOP_BUCKET || '',
        generatePublicId(),
        buffer,
        fileTypeResult.mime,
      );
    }
    await this.connection.getRepository(ctx, CustomPage).save({
      id: input.id,
      h5Link,
      h5QRCode,
    });
    await this.cacheService.removeCache(CacheKeyManagerService.customPage(input.id, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.customPageComponent(input.id, ctx.channelId));
    return {
      h5Link,
      h5QRCode: h5QRCode,
    };
  }
  async generateH5Link(ctx: RequestContext, input: ProgramLinkInput, pathUrl?: string, distributorId?: ID) {
    const h5Address = process.env.H5_ADDRESS || '';
    if (h5Address === '') {
      throw new Error('H5_ADDRESS is empty');
    }
    const shareData: ShareData = {
      shareType: input.type,
      shareValue: input.id,
      distributorId: String(distributorId ?? ''),
      path: pathUrl,
    };
    const params = await this.generateShareDataStr(ctx, shareData);
    return `${h5Address}/?scene=${params}`;
  }

  registerAfterSale(interfaceAfterSale: InterfaceAfterSale) {
    this.interfaceAfterSale = interfaceAfterSale;
  }

  //获取当前天数时间戳
  getTimestampDays(currentDate = new Date()) {
    if (!currentDate) {
      currentDate = new Date();
    }
    const mouth = currentDate.getMonth() + 1;
    const mouthStr = mouth < 10 ? '0' + mouth : mouth;

    const day = currentDate.getDate();
    const dayStr = day < 10 ? '0' + day : day;
    return `${currentDate.getFullYear()}${mouthStr}${dayStr}`;
  }
  //获取当前小时时间戳
  getTimestampHourly(currentDate = new Date()) {
    if (!currentDate) {
      currentDate = new Date();
    }
    const mouth = currentDate.getMonth() + 1;
    const mouthStr = mouth < 10 ? '0' + mouth : mouth;

    const day = currentDate.getDate();
    const dayStr = day < 10 ? '0' + day : day;

    const h = currentDate.getHours();
    const hstr = h < 10 ? '0' + h : h;
    return `${currentDate.getFullYear()}${mouthStr}${dayStr}${hstr}`;
  }

  //用户点击
  async userClick(ctx: RequestContext) {
    const channelId = ctx.channelId;
    const userId = ctx.activeUserId;
    if (!userId || !channelId) {
      throw new UnauthorizedError();
    }
    const dayStart = this.getTimestampDays();
    const dayKey = `${channelId}_${userId}_${dayStart}`;
    const statisticsKey = `${channelId}_${dayStart}`;
    const userDayRecord = await this.kvsService.visitantDailyRecord.get(dayKey);
    const statisticsDay = await this.kvsService.statistics.get(statisticsKey);
    if (statisticsDay) {
      // 今天不存在访问记录 则访客数+1
      if (!userDayRecord) {
        await this.kvsService.visitantDailyRecord.set(dayKey, dayStart);
        statisticsDay.visitorsCount++;
      }
      statisticsDay.pageViews++;
      await this.kvsService.statistics.set(statisticsKey, statisticsDay);
    } else {
      await this.kvsService.statistics.set(statisticsKey, {visitorsCount: 1, pageViews: 1});
    }
    const dayHourly = this.getTimestampHourly();
    const dayHourlyKey = `${userId}_${dayHourly}`;
    const statisticsHourlyKey = `${channelId}_${dayHourly}`;
    const userHourlyRecord = await this.kvsService.visitantDailyRecord.get(dayHourlyKey);
    const statisticsHourly = await this.kvsService.statistics.get(statisticsHourlyKey);
    if (statisticsHourly) {
      // 当前小时不存在访问记录 则访客数+1
      if (!userHourlyRecord) {
        await this.kvsService.visitantDailyRecord.set(dayHourlyKey, dayHourly);
        statisticsHourly.visitorsCount++;
      }
      statisticsHourly.pageViews++;
      await this.kvsService.statistics.set(statisticsHourlyKey, statisticsHourly);
    } else {
      await this.kvsService.statistics.set(statisticsHourlyKey, {visitorsCount: 1, pageViews: 1});
    }
    return true;
  }

  async getCtxByCustomerAndChannels(channels: Channel[], customer: Customer) {
    const user = customer.user;
    let channelOrToken;
    if (channels.length > 1) {
      for (const channel of channels) {
        if (channel.code !== DEFAULT_CHANNEL_CODE) {
          channelOrToken = channel;
        }
      }
    } else {
      channelOrToken = channels[0];
    }
    if (!channelOrToken) {
      return;
    }
    let ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    channelOrToken = await this.connection
      .getRepository(ctx, Channel)
      .findOne({where: {id: channelOrToken.id}, relations: ['defaultTaxZone']});
    if (!channelOrToken) {
      return;
    }
    ctx = await this.requestContextService.create({
      apiType: 'admin',
      channelOrToken: channelOrToken,
      user: user,
      languageCode: LanguageCode.zh_Hans,
    });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (ctx as any).session['customer'] = customer;
    this.requestContextCacheService.set(ctx, 'activeTaxZone', channelOrToken!.defaultTaxZone);

    return ctx;
  }

  async generateSmallProgramQRCodeLink(
    ctx: RequestContext,
    input: ProgramLinkInput,
    pathUrl?: string,
    distributorId?: ID,
  ) {
    const qrCodeBuffer = await this.generateSmallProgramQRCode(ctx, input, pathUrl, distributorId);
    const fileTypeResult = await fromBuffer(qrCodeBuffer);
    if (!fileTypeResult) {
      throw new Error('Incorrect buffer type ');
    }
    const qRCodeLink = await this.minioService.uploadByStream(
      process.env.SHOP_BUCKET || '',
      generatePublicId(),
      qrCodeBuffer,
      fileTypeResult.mime,
    );
    return qRCodeLink;
  }

  async generateSmallProgramQRCode(
    ctx: RequestContext,
    input: ProgramLinkInput,
    pathUrl = 'pages/home/<USER>',
    distributorId?: ID,
  ): Promise<Buffer> {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    const token = await this.weChatAuthService.getAccessToken(ctx);
    if (!token) {
      throw new Error('wechat token not exist');
    }
    const shareData: ShareData = {
      shareType: input.type,
      shareValue: input.id,
      distributorId: String(distributorId ?? ''),
      isPreview: input.isPreview,
    };
    const scene = await this.generateShareDataStr(ctx, shareData);
    const getUnlimitedQRCode = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${token}`;
    return new Promise((resolve, rejects) => {
      request(
        getUnlimitedQRCode,
        {
          method: 'post',
          json: true,
          encoding: null,
          body: {
            scene: scene,
            page: pathUrl,
            // eslint-disable-next-line
            check_path: false,
            // eslint-disable-next-line
            env_version: process.env.MINI_PROGRAM_VERSION || 'release',
          },
        },
        function (_err, _res, data) {
          if (_err) {
            rejects(_err);
          }
          resolve(data);
        },
      );
    });
  }
  async generateShareDataStr(ctx: RequestContext, shareData: ShareData) {
    // const nanoid = generatePublicId();
    const nanoid = this.md5(JSON.stringify(shareData));
    await this.kvsService.nanoidData.set(nanoid, shareData);
    const nanoDate = await this.connection.getRepository(ctx, NanoDate).findOne({where: {nanoid: nanoid}});
    if (!nanoDate) {
      await this.connection.getRepository(ctx, NanoDate).save({
        nanoid: nanoid,
        shareData: shareData,
      });
    } else {
      nanoDate.shareData = shareData;
      await this.connection.getRepository(ctx, NanoDate).save(nanoDate);
    }
    return nanoid;
  }

  async analysisShareDataByNanoid(ctx: RequestContext, nanoid: string): Promise<ShareData | undefined> {
    let shareData = await this.kvsService.nanoidData.get(nanoid);
    if (!shareData) {
      const nanoData = await this.connection.getRepository(ctx, NanoDate).findOne({where: {nanoid: nanoid}});
      if (!nanoData?.shareData) {
        return;
      }
      shareData = nanoData.shareData;
      await this.kvsService.nanoidData.set(nanoid, shareData);
    }
    return shareData;
  }

  async generateSchemeLink(ctx: RequestContext, input: ProgramLinkInput, pathUrl?: string, distributorId?: ID) {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    const token = await this.weChatAuthService.getAccessToken(ctx);
    if (!token) {
      throw new Error('wechat token not exist');
    }
    const shareData: ShareData = {
      shareType: input.type,
      shareValue: input.id,
      distributorId: String(distributorId ?? ''),
    };
    const query = await this.generateShareDataStr(ctx, shareData);
    const schemeRequestScheme = `https://api.weixin.qq.com/wxa/generatescheme?access_token=${token}`;
    return new Promise((resolve, rejects) => {
      request(
        schemeRequestScheme,
        {
          method: 'post',
          json: true,
          headers: {
            'content-type': 'application/json',
          },
          body: {
            // eslint-disable-next-line
            jump_wxa: {
              path: pathUrl ?? `pages/home/<USER>
              query: `scene=${query}`,
              // eslint-disable-next-line
              env_version: process.env.MINI_PROGRAM_VERSION || 'release',
            },
          },
        },
        function (_err, _res, data) {
          if (_err) {
            rejects(_err);
          }
          if (data?.errcode === 0) {
            if (data?.openlink) {
              resolve(data.openlink);
            }
            rejects();
          }
          rejects(data.errmsg);
        },
      );
    });
  }

  async generateSmallProgramLinkByPath(ctx: RequestContext, pathUrl: string) {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    const token = await this.weChatAuthService.getAccessToken(ctx);
    if (!token) {
      throw new Error('wechat token not exist');
    }
    const linkRequestLink = `https://api.weixin.qq.com/wxa/genwxashortlink?access_token=${token}`;
    return new Promise((resolve, rejects) => {
      request(
        linkRequestLink,
        {
          method: 'post',
          json: true,
          headers: {
            'content-type': 'application/json',
          },
          body: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            page_url: pathUrl,
          },
        },
        function (_err, _res, data) {
          if (_err) {
            rejects(_err);
          }
          if (data?.errcode === 0) {
            if (data?.link) {
              resolve(data.link);
            }
            rejects();
          }
          rejects(data.errmsg);
        },
      );
    });
  }

  async generateSmallProgramLink(
    ctx: RequestContext,
    input: ProgramLinkInput,
    pathUrl?: string,
    distributorId?: ID,
  ): Promise<string> {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    const token = await this.weChatAuthService.getAccessToken(ctx);
    if (!token) {
      throw new Error('wechat token not exist');
    }
    const shareData: ShareData = {
      shareType: input.type,
      shareValue: input.id,
      distributorId: String(distributorId ?? ''),
    };
    const query = await this.generateShareDataStr(ctx, shareData);
    const linkRequestLink = `https://api.weixin.qq.com/wxa/genwxashortlink?access_token=${token}`;
    return new Promise((resolve, rejects) => {
      request(
        linkRequestLink,
        {
          method: 'post',
          json: true,
          headers: {
            'content-type': 'application/json',
          },
          body: {
            // eslint-disable-next-line
            page_url: `${pathUrl ?? 'pages/home/<USER>'}` + `?scene=${query}`,
          },
        },
        function (_err, _res, data) {
          if (_err) {
            rejects(_err);
          }
          if (data?.errcode === 0) {
            if (data?.link) {
              resolve(data.link);
            }
            rejects();
          }
          rejects(data.errmsg);
        },
      );
    });
  }

  async getGiftCardQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const giftCardId = input.id;
    const giftCard = await this.giftCardService.findOne(ctx, giftCardId);
    if (!giftCard) {
      throw new EntityNotFoundError('GiftCard', giftCardId);
    }
    if (giftCard.smallProgramQRCodeLink) {
      return giftCard.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    giftCard.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, Product).save(giftCard);
    return qrCodeLink;
  }

  async getMemberShipPlanQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const membershipPlanId = input.id;
    const membershipPlan = await this.membershipPlanService.findOne(ctx, membershipPlanId);
    if (!membershipPlan) {
      throw new EntityNotFoundError('MembershipPlan', membershipPlanId);
    }
    if (membershipPlan.smallProgramQRCodeLink) {
      return membershipPlan.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    membershipPlan.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, MembershipPlan).save(membershipPlan);
    await this.cacheService.removeCache([
      CacheKeyManagerService.membershipPlan(membershipPlanId, ctx.channelId),
      CacheKeyManagerService.promotion(membershipPlan?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(membershipPlan?.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }

  async getProductQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const productId = input.id;
    const product = await this.productService.findOne(ctx, productId);
    if (!product) {
      throw new EntityNotFoundError('Product', productId);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let qrCodeLink = (product.customFields as any).smallProgramQRCodeLink;
    if (qrCodeLink) {
      return qrCodeLink;
    }
    qrCodeLink = await this.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');

    await this.connection
      .getRepository(ctx, Product)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .update(productId, {customFields: {smallProgramQRCodeLink: qrCodeLink}} as any);
    await this.cacheService.removeCache(CacheKeyManagerService.product(productId, ctx.channelId));
    return qrCodeLink;
  }

  async getShoppingCreditsDeductionQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const shoppingCreditsDeductionActivityId = input.id;
    const shoppingCreditsDeductionActivity = await this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .findOne({
        where: {
          id: shoppingCreditsDeductionActivityId,
          deletedAt: IsNull(),
        },
      });
    if (!shoppingCreditsDeductionActivity) {
      throw new EntityNotFoundError('ShoppingCreditsDeductionActivity', shoppingCreditsDeductionActivityId);
    }
    if (shoppingCreditsDeductionActivity.smallProgramQRCodeLink) {
      return shoppingCreditsDeductionActivity.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(shoppingCreditsDeductionActivity.promotionId),
        type: input.type,
      },
      input.path ?? '',
    );
    await this.connection.getRepository(ctx, ShoppingCreditsDeductionActivity).update(
      {
        id: shoppingCreditsDeductionActivityId,
      },
      {
        smallProgramQRCodeLink: qrCodeLink,
      },
    );
    return qrCodeLink;
  }

  async getShoppingCreditsClaimQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const shoppingCreditsClaimActivityId = input.id;
    const shoppingCreditsClaimActivity = await this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .findOne({
        where: {
          id: shoppingCreditsClaimActivityId,
          deletedAt: IsNull(),
        },
      });
    if (!shoppingCreditsClaimActivity) {
      throw new EntityNotFoundError('ShoppingCreditsClaimActivity', shoppingCreditsClaimActivityId);
    }
    if (shoppingCreditsClaimActivity.smallProgramQRCodeLink) {
      return shoppingCreditsClaimActivity.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(shoppingCreditsClaimActivity.promotionId),
        type: input.type,
      },
      input.path ?? '',
    );
    await this.connection.getRepository(ctx, ShoppingCreditsClaimActivity).update(
      {
        id: shoppingCreditsClaimActivityId,
      },
      {
        smallProgramQRCodeLink: qrCodeLink,
      },
    );
    return qrCodeLink;
  }

  async getForumTagQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const forumTagId = input.id;
    const forumTag = await this.connection.getRepository(ctx, ForumTag).findOne({
      where: {
        id: forumTagId,
        deletedAt: IsNull(),
      },
    });
    if (!forumTag) {
      throw new EntityNotFoundError('ForumTag', forumTagId);
    }
    if (forumTag.smallProgramQRCodeLink) {
      return forumTag.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    await this.connection.getRepository(ctx, ForumTag).update(
      {
        id: forumTagId,
      },
      {
        smallProgramQRCodeLink: qrCodeLink,
      },
    );
    return qrCodeLink;
  }

  async conversionAnalysis(ctx: RequestContext, startDate: Date = new Date(), endDate: Date = new Date()) {
    // 是否需要比对数据
    let isNeedToCompare = true;
    // 如果开始时间和结束时间不是同一天则不需要比对数据 使用luxon对比
    if (!DateTime.fromJSDate(startDate).hasSame(DateTime.fromJSDate(endDate), 'day')) {
      isNeedToCompare = false;
    }
    //昨日支付订单数
    const curDate = new Date(startDate);
    curDate.setDate(curDate.getDate() - 1);
    // 今日系统访客和浏览量
    const systemDailyStats = await this.promotionResultDetailService.getSystemDailyStatsByDateTime(
      ctx,
      startDate,
      endDate,
    );
    // 今日访客人数
    const visitorsCount = Number(systemDailyStats ? systemDailyStats?.visitorsCount : 0);
    // 今日浏览量
    const pageViews = Number(systemDailyStats ? systemDailyStats?.pageViews : 0);
    //咋日系统访客
    const systemDailyStatsYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getSystemDailyStatsByDateTime(ctx, curDate)
      : 0;
    //昨日访客人数
    const visitorsCountYesterdayNumber = Number(
      systemDailyStatsYesterday ? systemDailyStatsYesterday?.visitorsCount : 0,
    );
    //昨日浏览量
    const pageViewsYesterday = Number(systemDailyStatsYesterday ? systemDailyStatsYesterday?.pageViews : 0);
    //访客人数比例
    const proportionOfVisitors = isNeedToCompare
      ? visitorsCountYesterdayNumber
        ? ((visitorsCount - visitorsCountYesterdayNumber) / visitorsCountYesterdayNumber) * 100
        : 0
      : 0;
    //访客人数与昨天相比
    const visitorsCountCompareYesterday = isNeedToCompare
      ? visitorsCountYesterdayNumber
        ? (visitorsCount / visitorsCountYesterdayNumber) * 100
        : 0
      : 0;

    // 浏览量比例
    const proportionOfPageViews = isNeedToCompare
      ? pageViewsYesterday
        ? ((pageViews - pageViewsYesterday) / pageViewsYesterday) * 100
        : 0
      : 0;
    // 浏览量与昨天相比
    const pageViewsCompareYesterday = isNeedToCompare
      ? pageViewsYesterday
        ? (pageViews / pageViewsYesterday) * 100
        : 0
      : 0;
    //今日销售额
    const salesToday = await this.promotionResultDetailService.getSalesByDateTime(ctx, startDate, endDate);
    //昨日销售额
    const salesYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getSalesByDateTime(ctx, curDate)
      : 0;
    //销售额比例
    const proportionOfSales = isNeedToCompare
      ? salesYesterday
        ? ((salesToday - salesYesterday) / salesYesterday) * 100
        : 0
      : 0;
    //销售额与昨天相比
    const salesCompareYesterday = isNeedToCompare ? (salesYesterday ? (salesToday / salesYesterday) * 100 : 0) : 0;
    //今日支付人数
    const numberOfPayersToday = await this.promotionResultDetailService.getNumberOfPayersByDateTime(
      ctx,
      startDate,
      endDate,
    );
    //昨日支付人数
    const numberOfPayersYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getNumberOfPayersByDateTime(ctx, curDate)
      : 0;
    //今日成功退款金额-售后
    const refundAfterSaleAmountToday = await this.interfaceAfterSale.getRefundAmountByDateTime(ctx, startDate, endDate);
    //今日主动退款金额
    const activeRefundAmountToday = await this.promotionResultDetailService.getActiveRefundAmountByDateTime(
      ctx,
      startDate,
      endDate,
    );
    // 今日退款金额 = 售后金额 + 主动退款金额 2024-07-23 产品要求修改
    const refundAmountToday = NP.plus(refundAfterSaleAmountToday, activeRefundAmountToday);
    // 昨日成功退款金额
    const refundAfterSaleAmountYesterday = isNeedToCompare
      ? await this.interfaceAfterSale.getRefundAmountByDateTime(ctx, curDate)
      : 0;
    // 昨日主动退款金额
    const activeRefundAmountYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getActiveRefundAmountByDateTime(ctx, curDate)
      : 0;
    // 昨日成功退款金额 = 售后金额 + 主动退款金额 2024-07-23 产品要求修改
    const refundAmountYesterday = isNeedToCompare
      ? NP.plus(refundAfterSaleAmountYesterday, activeRefundAmountYesterday)
      : 0;
    //退款金额与昨天相比
    const refundAmountCompareYesterday = isNeedToCompare
      ? refundAmountYesterday
        ? (refundAmountToday / refundAmountYesterday) * 100
        : 0
      : 0;
    //今日营业额
    const salesVolumeToday = salesToday - refundAmountToday;
    //昨日营业额
    const salesVolumeYesterday = isNeedToCompare ? salesYesterday - refundAmountYesterday : 0;
    //营业额与昨天相比
    const salesVolumeCompareYesterday = isNeedToCompare
      ? salesVolumeYesterday
        ? (salesVolumeToday / salesVolumeYesterday) * 100
        : 0
      : 0;
    //今日客单价
    const customerUnitPriceToday = numberOfPayersToday ? salesToday / numberOfPayersToday : 0;
    //昨日客单价
    const customerUnitPriceYesterday = isNeedToCompare
      ? numberOfPayersYesterday
        ? salesYesterday / numberOfPayersYesterday
        : 0
      : 0;
    //客单价与昨天相比
    const customerUnitPriceCompareYesterday = isNeedToCompare
      ? customerUnitPriceYesterday
        ? (customerUnitPriceToday / customerUnitPriceYesterday) * 100
        : 0
      : 0;
    //今日支付订单数
    const orderNumberPaidToday = await this.promotionResultDetailService.getOrdersByDateTime(ctx, startDate, endDate);
    //昨日支付订单数
    const ordersPaidYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getOrdersByDateTime(ctx, curDate)
      : 0;
    // 订单数比例
    const proportionOfOrders = isNeedToCompare
      ? ordersPaidYesterday
        ? ((orderNumberPaidToday - ordersPaidYesterday) / ordersPaidYesterday) * 100
        : 0
      : 0;
    //支付订单于昨日相比
    const orderNumberPaidCompareYesterday = isNeedToCompare
      ? ordersPaidYesterday
        ? (orderNumberPaidToday / ordersPaidYesterday) * 100
        : 0
      : 0;
    // 今日访问-支付转换率
    const conversionRateOfVisitsToPaymentsToday =
      numberOfPayersToday && visitorsCount ? (numberOfPayersToday / visitorsCount) * 100 : 0;
    // 昨日访问-支付转换率
    const conversionRateOfVisitsToPaymentsYesterday = isNeedToCompare
      ? numberOfPayersYesterday && visitorsCountYesterdayNumber
        ? (numberOfPayersYesterday / visitorsCountYesterdayNumber) * 100
        : 0
      : 0;
    //访问-支付转换率与昨天相比
    const conversionRateOfVisitsToPaymentsCompareYesterday = isNeedToCompare
      ? conversionRateOfVisitsToPaymentsYesterday
        ? (conversionRateOfVisitsToPaymentsToday / conversionRateOfVisitsToPaymentsYesterday) * 100
        : 0
      : 0;
    //今日新增发卡数量
    const newCardNumberToday = await this.promotionResultDetailService.getNewCardNumberByDateTime(
      ctx,
      startDate,
      endDate,
    );
    //昨日新增发卡数量
    const newCardNumberYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getNewCardNumberByDateTime(ctx, curDate)
      : 0;
    //新增发卡数量与昨天相比
    const newCardNumberCompareYesterday = isNeedToCompare
      ? newCardNumberYesterday
        ? (newCardNumberToday / newCardNumberYesterday) * 100
        : 0
      : 0;
    //累积持卡人数
    const cumulativeCardholders = await this.promotionResultDetailService.getCumulativeCardholders(ctx, startDate);
    //昨日持卡人数
    const cumulativeCardholdersYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getCumulativeCardholders(ctx, curDate)
      : 0;
    //持卡人数与昨天相比
    const cumulativeCardholdersCompareYesterday = isNeedToCompare
      ? cumulativeCardholdersYesterday
        ? (cumulativeCardholders / cumulativeCardholdersYesterday) * 100
        : 0
      : 0;
    // 会员卡支付总人数
    const membershipCardPaymentFrequency = await this.promotionResultDetailService.getMembershipCardPaymentFrequency(
      ctx,
      startDate,
      endDate,
    );
    // 昨日会员卡支付总人数
    const membershipCardPaymentFrequencyYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getMembershipCardPaymentFrequency(ctx, curDate)
      : 0;
    // 会员卡支付总人数与昨天相比
    const membershipCardPaymentFrequencyCompareYesterday = isNeedToCompare
      ? membershipCardPaymentFrequencyYesterday
        ? (membershipCardPaymentFrequency / membershipCardPaymentFrequencyYesterday) * 100
        : 0
      : 0;
    // 会员卡支付总订单数
    const membershipCardPaymentOrderNumber =
      await this.promotionResultDetailService.getMembershipCardPaymentOrderNumber(ctx, startDate, endDate);
    // 昨日会员卡支付总订单数
    const membershipCardPaymentOrderNumberYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getMembershipCardPaymentOrderNumber(ctx, curDate)
      : 0;
    // 会员卡支付总订单数与昨天相比
    const membershipCardPaymentOrderNumberCompareYesterday = isNeedToCompare
      ? membershipCardPaymentOrderNumberYesterday
        ? (membershipCardPaymentOrderNumber / membershipCardPaymentOrderNumberYesterday) * 100
        : 0
      : 0;
    // 会员卡支付总金额
    const membershipCardPaymentAmount = await this.promotionResultDetailService.getMembershipCardPaymentAmount(
      ctx,
      startDate,
      endDate,
    );
    // 昨日会员卡支付总金额
    const membershipCardPaymentAmountYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getMembershipCardPaymentAmount(ctx, curDate)
      : 0;
    // 会员卡支付总金额与昨天相比
    const membershipCardPaymentAmountCompareYesterday = isNeedToCompare
      ? membershipCardPaymentAmountYesterday
        ? (membershipCardPaymentAmount / membershipCardPaymentAmountYesterday) * 100
        : 0
      : 0;
    // 会员卡支付总金额与今日销售额比例
    const membershipCardPaymentAmountCompareSales = salesToday ? (membershipCardPaymentAmount / salesToday) * 100 : 0;

    // 盲盒访客
    const blindBoxVisitorAndView = await this.matomoService.getBlindBoxActivityPageDataStatistics(
      ctx,
      startDate,
      endDate,
    );
    // 论坛首页访客
    const forumVisitorAndView = await this.matomoService.getBlindBoxActivityPageDataStatistics(
      ctx,
      startDate,
      endDate,
      'ForumNotes',
    );
    return {
      // 盲盒访客
      blindBoxVisitor: blindBoxVisitorAndView?.visits,
      // 盲盒浏览量
      blindBoxView: blindBoxVisitorAndView?.hits,
      // 论坛访客
      forumVisitor: forumVisitorAndView?.visits,
      // 论坛浏览量
      forumView: forumVisitorAndView?.hits,
      // 访客人数
      visitorsCount,
      // 今日浏览量
      pageViews,
      // 昨日访客人数
      visitorsCountYesterdayNumber,
      // 昨日浏览量
      pageViewsYesterday,
      // 访客人数比例
      proportionOfVisitors,
      // 访客人数与昨天相比
      visitorsCountCompareYesterday,
      // 浏览量比例
      proportionOfPageViews,
      // 浏览量与昨天相比
      pageViewsCompareYesterday,
      // 今日销售额
      salesToday,
      // 昨日销售额
      salesYesterday,
      // 销售额比例
      proportionOfSales,
      // 销售额与昨天相比
      salesCompareYesterday,
      // 今日成功退款金额
      refundAmountToday,
      // 昨日成功退款金额
      refundAmountYesterday,
      // 退款金额与昨天相比
      refundAmountCompareYesterday,
      // 今日营业额
      salesVolumeToday,
      // 昨日营业额
      salesVolumeYesterday,
      // 营业额与昨天相比
      salesVolumeCompareYesterday,
      // 今日客单价
      customerUnitPriceToday,
      // 昨日客单价
      customerUnitPriceYesterday,
      // 客单价与昨天相比
      customerUnitPriceCompareYesterday,
      // 今日支付订单数
      orderNumberPaidToday,
      // 昨日支付订单数
      ordersPaidYesterday,
      // 订单数比例
      proportionOfOrders,
      // 支付订单于昨日相比
      orderNumberPaidCompareYesterday,
      // 今日访问-支付转换率
      conversionRateOfVisitsToPaymentsToday,
      // 昨日访问-支付转换率
      conversionRateOfVisitsToPaymentsYesterday,
      // 访问-支付转换率与昨天相比
      conversionRateOfVisitsToPaymentsCompareYesterday,
      // 今日新增发卡数量
      newCardNumberToday,
      // 昨日新增发卡数量
      newCardNumberYesterday,
      // 新增发卡数量与昨天相比
      newCardNumberCompareYesterday,
      // 累积持卡人数
      cumulativeCardholders,
      // 昨日持卡人数
      cumulativeCardholdersYesterday,
      // 持卡人数与昨天相比
      cumulativeCardholdersCompareYesterday,
      // 会员卡支付总人数
      membershipCardPaymentFrequency,
      // 昨日会员卡支付总人数
      membershipCardPaymentFrequencyYesterday,
      // 会员卡支付总人数与昨天相比
      membershipCardPaymentFrequencyCompareYesterday,
      // 会员卡支付总订单数
      membershipCardPaymentOrderNumber,
      // 昨日会员卡支付总订单数
      membershipCardPaymentOrderNumberYesterday,
      // 会员卡支付总订单数与昨天相比
      membershipCardPaymentOrderNumberCompareYesterday,
      // 会员卡支付总金额
      membershipCardPaymentAmount,
      // 昨日会员卡支付总金额
      membershipCardPaymentAmountYesterday,
      // 会员卡支付总金额与昨天相比
      membershipCardPaymentAmountCompareYesterday,
      // 会员卡支付总金额与今日销售额比例
      membershipCardPaymentAmountCompareSales,
    };
  }
  async realTimeStatistics(ctx: RequestContext, startDate: Date = new Date(), endDate: Date = new Date()) {
    // 是否需要比对数据
    let isNeedToCompare = true;
    // 如果开始时间和结束时间不是同一天则不需要比对数据 使用luxon对比
    if (!DateTime.fromJSDate(startDate).hasSame(DateTime.fromJSDate(endDate), 'day')) {
      isNeedToCompare = false;
    }
    // 待发货订单数
    const orderNumberToBeShipped = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoin('order.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('state=:state', {state: 'PaymentSettled'})
      .getCount();
    //待处理退款数
    const numberOfPendingRefunds = await this.interfaceAfterSale.getNumberOfPendingRefunds(ctx);
    //本月销售额
    const salesThisMonth = await this.promotionResultDetailService.monthlySalesVolume(
      ctx,
      new Date(),
      DateTimeType.Month,
    );
    //今日支付订单数
    const orderNumberPaidToday = await this.promotionResultDetailService.getOrdersByDateTime(ctx, startDate, endDate);
    //昨日支付订单数
    const curDate = new Date(startDate);
    curDate.setDate(curDate.getDate() - 1);
    const ordersPaidYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getOrdersByDateTime(ctx, curDate)
      : 0;
    // 订单数比例
    const proportionOfOrders = isNeedToCompare
      ? ordersPaidYesterday
        ? ((orderNumberPaidToday - ordersPaidYesterday) / ordersPaidYesterday) * 100
        : 0
      : 0;
    //支付订单于昨日相比
    const orderNumberPaidCompareYesterday = isNeedToCompare
      ? ordersPaidYesterday
        ? (orderNumberPaidToday / ordersPaidYesterday) * 100
        : 0
      : 0;
    //今日销售额
    const salesToday = await this.promotionResultDetailService.getSalesByDateTime(ctx, startDate, endDate);
    //昨日销售额
    const salesYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getSalesByDateTime(ctx, curDate)
      : 0;
    //销售额比例
    const proportionOfSales = isNeedToCompare
      ? salesYesterday
        ? ((salesToday - salesYesterday) / salesYesterday) * 100
        : 0
      : 0;
    //销售额与昨天相比
    const salesCompareYesterday = isNeedToCompare ? (salesYesterday ? (salesToday / salesYesterday) * 100 : 0) : 0;
    //今日支付人数
    const numberOfPayersToday = await this.promotionResultDetailService.getNumberOfPayersByDateTime(
      ctx,
      startDate,
      endDate,
    );
    //昨日支付人数
    const numberOfPayersYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getNumberOfPayersByDateTime(ctx, curDate)
      : 0;
    //支付人数比例
    const proportionOfPayers = isNeedToCompare
      ? numberOfPayersYesterday
        ? ((numberOfPayersToday - numberOfPayersYesterday) / numberOfPayersYesterday) * 100
        : 0
      : 0;
    // 今日系统访客和浏览量
    const systemDailyStats = await this.promotionResultDetailService.getSystemDailyStatsByDateTime(
      ctx,
      startDate,
      endDate,
    );
    // 今日访客人数
    const visitorsCount = Number(systemDailyStats ? systemDailyStats?.visitorsCount : 0);
    //咋日系统访客
    const systemDailyStatsYesterday = isNeedToCompare
      ? await this.promotionResultDetailService.getSystemDailyStatsByDateTime(ctx, curDate)
      : 0;
    //昨日访客人数
    const visitorsCountYesterdayNumber = Number(
      systemDailyStatsYesterday ? systemDailyStatsYesterday?.visitorsCount : 0,
    );
    //昨日浏览量
    // const pageViewsYesterday = Number(systemDailyStatsYesterday ? systemDailyStatsYesterday?.pageViews : 0);
    //访客人数比例
    const proportionOfVisitors = isNeedToCompare
      ? visitorsCountYesterdayNumber
        ? ((visitorsCount - visitorsCountYesterdayNumber) / visitorsCountYesterdayNumber) * 100
        : 0
      : 0;
    //访客人数与昨天相比
    const visitorsCountCompareYesterday = isNeedToCompare
      ? visitorsCountYesterdayNumber
        ? (visitorsCount / visitorsCountYesterdayNumber) * 100
        : 0
      : 0;
    return {
      // 待发货订单数
      orderNumberToBeShipped,
      //待处理退款数
      numberOfPendingRefunds,
      // 本月销售额
      salesThisMonth,
      // 今日支付订单数
      orderNumberPaidToday,
      // 昨日支付订单数
      ordersPaidYesterday,
      // 订单数比例
      proportionOfOrders,
      // 支付订单于昨日相比
      orderNumberPaidCompareYesterday,
      // 今日销售额
      salesToday,
      // 昨日销售额
      salesYesterday,
      // 销售额比例
      proportionOfSales,
      // 销售额与昨天相比
      salesCompareYesterday,
      // 今日支付人数
      numberOfPayersToday,
      // 昨日支付人数
      numberOfPayersYesterday,
      // 支付人数比例
      proportionOfPayers,
      // 访客人数
      visitorsCount,
      // 昨日访客人数
      visitorsCountYesterdayNumber,
      // 访客人数比例
      proportionOfVisitors,
      // 访客人数与昨天相比
      visitorsCountCompareYesterday,
    };
  }

  async salesStatistics(ctx: RequestContext, start: Date = new Date(), end: Date = new Date()) {
    // 开始时间和结束时间没有则默认查询全部
    if (!start) {
      start = new Date(0);
    }
    if (!end) {
      end = new Date();
    }
    // 销售额
    const salesPrice = await this.promotionResultDetailService.getSalesByDateTime(ctx, start, end, true);
    // 售后金额
    const afterSalePrice = await this.interfaceAfterSale.getRefundAmountByDateTime(ctx, start, end, true);
    // 主动退款金额
    const activeRefundAmount = await this.promotionResultDetailService.getActiveRefundAmountByDateTime(
      ctx,
      start,
      end,
      true,
    );
    // 退款金额 = 退款金额 + 主动退款金额  2024-07-23 产品要求更改逻辑 退款金额需要包含主动退款金额
    const refundAmount = NP.plus(afterSalePrice, activeRefundAmount);
    // 营业额
    const salesVolume = salesPrice - refundAmount;
    return {
      salesPrice,
      refundAmount,
      salesVolume,
    };
  }

  //实时获取当前数据看版
  async dataStatistics(ctx: RequestContext, start?: Date, end?: Date) {
    // 开始时间和结束时间没有则默认查询全部
    if (!start) {
      start = new Date(0);
    }
    if (!end) {
      end = new Date();
    }
    // 销售额
    const salesPrice = await this.promotionResultDetailService.getSalesByDateTime(ctx, start, end);
    // 售后金额
    const afterSalePrice = await this.interfaceAfterSale.getRefundAmountByDateTime(ctx, start, end);
    // 主动退款金额
    const activeRefundAmount = await this.promotionResultDetailService.getActiveRefundAmountByDateTime(ctx, start, end);
    // 退款金额 = 退款金额 + 主动退款金额  2024-07-23 产品要求更改逻辑 退款金额需要包含主动退款金额
    const refundAmount = NP.plus(afterSalePrice, activeRefundAmount);
    // 营业额
    const salesVolume = salesPrice - refundAmount;

    // 待发货订单数
    const orderNumberToBeShipped = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoin('order.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('state=:state', {state: 'PaymentSettled'})
      .getCount();
    //待处理退款数
    const numberOfPendingRefunds = await this.interfaceAfterSale.getNumberOfPendingRefunds(ctx);
    //本月销售额
    const salesThisMonth = await this.promotionResultDetailService.monthlySalesVolume(
      ctx,
      new Date(),
      DateTimeType.Month,
    );
    //今日支付订单数
    const orderNumberPaidToday = await this.promotionResultDetailService.getOrdersByDateTime(ctx);
    //昨日支付订单数
    const curDate = new Date();
    curDate.setDate(curDate.getDate() - 1);
    const ordersPaidYesterday = await this.promotionResultDetailService.getOrdersByDateTime(ctx, curDate);
    // 订单数比例
    const proportionOfOrders = ordersPaidYesterday
      ? ((orderNumberPaidToday - ordersPaidYesterday) / ordersPaidYesterday) * 100
      : 0;
    //支付订单于昨日相比
    const orderNumberPaidCompareYesterday = ordersPaidYesterday
      ? (orderNumberPaidToday / ordersPaidYesterday) * 100
      : 0;
    //今日销售额
    const salesToday = await this.promotionResultDetailService.getSalesByDateTime(ctx);
    //昨日销售额
    const salesYesterday = await this.promotionResultDetailService.getSalesByDateTime(ctx, curDate);
    //销售额比例
    const proportionOfSales = salesYesterday ? ((salesToday - salesYesterday) / salesYesterday) * 100 : 0;
    //销售额与昨天相比
    const salesCompareYesterday = salesYesterday ? (salesToday / salesYesterday) * 100 : 0;
    //今日支付人数
    const numberOfPayersToday = await this.promotionResultDetailService.getNumberOfPayersByDateTime(ctx);
    //昨日支付人数
    const numberOfPayersYesterday = await this.promotionResultDetailService.getNumberOfPayersByDateTime(ctx, curDate);
    //支付人数比例
    const proportionOfPayers = numberOfPayersYesterday
      ? ((numberOfPayersToday - numberOfPayersYesterday) / numberOfPayersYesterday) * 100
      : 0;
    // 今日系统访客和浏览量
    const systemDailyStats = await this.promotionResultDetailService.getSystemDailyStatsByDateTime(ctx);
    // 今日访客人数
    const visitorsCount = Number(systemDailyStats ? systemDailyStats?.visitorsCount : 0);
    // 今日浏览量
    const pageViews = Number(systemDailyStats ? systemDailyStats?.pageViews : 0);
    //咋日系统访客
    const systemDailyStatsYesterday = await this.promotionResultDetailService.getSystemDailyStatsByDateTime(
      ctx,
      curDate,
    );
    //昨日访客人数
    const visitorsCountYesterdayNumber = Number(
      systemDailyStatsYesterday ? systemDailyStatsYesterday?.visitorsCount : 0,
    );
    //昨日浏览量
    const pageViewsYesterday = Number(systemDailyStatsYesterday ? systemDailyStatsYesterday?.pageViews : 0);
    //访客人数比例
    const proportionOfVisitors = visitorsCountYesterdayNumber
      ? ((visitorsCount - visitorsCountYesterdayNumber) / visitorsCountYesterdayNumber) * 100
      : 0;
    //访客人数与昨天相比
    const visitorsCountCompareYesterday = visitorsCountYesterdayNumber
      ? (visitorsCount / visitorsCountYesterdayNumber) * 100
      : 0;

    // 浏览量比例
    const proportionOfPageViews = pageViewsYesterday
      ? ((pageViews - pageViewsYesterday) / pageViewsYesterday) * 100
      : 0;
    // 浏览量与昨天相比
    const pageViewsCompareYesterday = pageViewsYesterday ? (pageViews / pageViewsYesterday) * 100 : 0;
    //今日成功退款金额-售后
    const refundAfterSaleAmountToday = await this.interfaceAfterSale.getRefundAmountByDateTime(ctx);
    //今日主动退款金额
    const activeRefundAmountToday = await this.promotionResultDetailService.getActiveRefundAmountByDateTime(ctx);
    // 今日退款金额 = 售后金额 + 主动退款金额 2024-07-23 产品要求修改
    const refundAmountToday = NP.plus(refundAfterSaleAmountToday, activeRefundAmountToday);
    // 昨日成功退款金额
    const refundAfterSaleAmountYesterday = await this.interfaceAfterSale.getRefundAmountByDateTime(ctx, curDate);
    // 昨日主动退款金额
    const activeRefundAmountYesterday = await this.promotionResultDetailService.getActiveRefundAmountByDateTime(
      ctx,
      curDate,
    );
    // 昨日成功退款金额 = 售后金额 + 主动退款金额 2024-07-23 产品要求修改
    const refundAmountYesterday = NP.plus(refundAfterSaleAmountYesterday, activeRefundAmountYesterday);
    //退款金额与昨天相比
    const refundAmountCompareYesterday = refundAmountYesterday ? (refundAmountToday / refundAmountYesterday) * 100 : 0;
    //今日营业额
    const salesVolumeToday = salesToday - refundAmountToday;
    //昨日营业额
    const salesVolumeYesterday = salesYesterday - refundAmountYesterday;
    //营业额与昨天相比
    const salesVolumeCompareYesterday = salesVolumeYesterday ? (salesVolumeToday / salesVolumeYesterday) * 100 : 0;
    //今日客单价
    const customerUnitPriceToday = numberOfPayersToday ? salesToday / numberOfPayersToday : 0;
    //昨日客单价
    const customerUnitPriceYesterday = numberOfPayersYesterday ? salesYesterday / numberOfPayersYesterday : 0;
    //客单价与昨天相比
    const customerUnitPriceCompareYesterday = customerUnitPriceYesterday
      ? (customerUnitPriceToday / customerUnitPriceYesterday) * 100
      : 0;
    // 今日访问-支付转换率
    const conversionRateOfVisitsToPaymentsToday =
      numberOfPayersToday && visitorsCount ? (numberOfPayersToday / visitorsCount) * 100 : 0;
    // 昨日访问-支付转换率
    const conversionRateOfVisitsToPaymentsYesterday =
      numberOfPayersYesterday && visitorsCountYesterdayNumber
        ? (numberOfPayersYesterday / visitorsCountYesterdayNumber) * 100
        : 0;
    //访问-支付转换率与昨天相比
    const conversionRateOfVisitsToPaymentsCompareYesterday = conversionRateOfVisitsToPaymentsYesterday
      ? (conversionRateOfVisitsToPaymentsToday / conversionRateOfVisitsToPaymentsYesterday) * 100
      : 0;
    //今日新增发卡数量
    const newCardNumberToday = await this.promotionResultDetailService.getNewCardNumberByDateTime(ctx);
    //昨日新增发卡数量
    const newCardNumberYesterday = await this.promotionResultDetailService.getNewCardNumberByDateTime(ctx, curDate);
    //新增发卡数量与昨天相比
    const newCardNumberCompareYesterday = newCardNumberYesterday
      ? (newCardNumberToday / newCardNumberYesterday) * 100
      : 0;
    //累积持卡人数
    const cumulativeCardholders = await this.promotionResultDetailService.getCumulativeCardholders(ctx);
    //昨日持卡人数
    const cumulativeCardholdersYesterday = await this.promotionResultDetailService.getCumulativeCardholders(
      ctx,
      curDate,
    );
    //持卡人数与昨天相比
    const cumulativeCardholdersCompareYesterday = cumulativeCardholdersYesterday
      ? (cumulativeCardholders / cumulativeCardholdersYesterday) * 100
      : 0;
    // 会员卡支付总人数
    const membershipCardPaymentFrequency = await this.promotionResultDetailService.getMembershipCardPaymentFrequency(
      ctx,
    );
    // 昨日会员卡支付总人数
    const membershipCardPaymentFrequencyYesterday =
      await this.promotionResultDetailService.getMembershipCardPaymentFrequency(ctx, curDate);
    // 会员卡支付总人数与昨天相比
    const membershipCardPaymentFrequencyCompareYesterday = membershipCardPaymentFrequencyYesterday
      ? (membershipCardPaymentFrequency / membershipCardPaymentFrequencyYesterday) * 100
      : 0;
    // 会员卡支付总订单数
    const membershipCardPaymentOrderNumber =
      await this.promotionResultDetailService.getMembershipCardPaymentOrderNumber(ctx);
    // 昨日会员卡支付总订单数
    const membershipCardPaymentOrderNumberYesterday =
      await this.promotionResultDetailService.getMembershipCardPaymentOrderNumber(ctx, curDate);
    // 会员卡支付总订单数与昨天相比
    const membershipCardPaymentOrderNumberCompareYesterday = membershipCardPaymentOrderNumberYesterday
      ? (membershipCardPaymentOrderNumber / membershipCardPaymentOrderNumberYesterday) * 100
      : 0;
    // 会员卡支付总金额
    const membershipCardPaymentAmount = await this.promotionResultDetailService.getMembershipCardPaymentAmount(ctx);
    // 昨日会员卡支付总金额
    const membershipCardPaymentAmountYesterday = await this.promotionResultDetailService.getMembershipCardPaymentAmount(
      ctx,
      curDate,
    );
    // 会员卡支付总金额与昨天相比
    const membershipCardPaymentAmountCompareYesterday = membershipCardPaymentAmountYesterday
      ? (membershipCardPaymentAmount / membershipCardPaymentAmountYesterday) * 100
      : 0;
    // 会员卡支付总金额与今日销售额比例
    const membershipCardPaymentAmountCompareSales = salesToday ? (membershipCardPaymentAmount / salesToday) * 100 : 0;
    return {
      salesPrice,
      refundAmount,
      salesVolume,
      orderNumberToBeShipped,
      numberOfPendingRefunds,
      salesThisMonth,
      orderNumberPaidToday,
      ordersPaidYesterday,
      proportionOfOrders,
      orderNumberPaidCompareYesterday,
      salesToday,
      salesYesterday,
      proportionOfSales,
      salesCompareYesterday,
      numberOfPayersToday,
      numberOfPayersYesterday,
      proportionOfPayers,
      visitorsCount,
      pageViews,
      visitorsCountYesterdayNumber,
      pageViewsYesterday,
      pageViewsCompareYesterday,
      proportionOfVisitors,
      visitorsCountCompareYesterday,
      proportionOfPageViews,
      refundAmountToday,
      refundAmountYesterday,
      refundAmountCompareYesterday,
      salesVolumeToday,
      salesVolumeYesterday,
      salesVolumeCompareYesterday,
      customerUnitPriceToday,
      customerUnitPriceYesterday,
      customerUnitPriceCompareYesterday,
      conversionRateOfVisitsToPaymentsToday,
      conversionRateOfVisitsToPaymentsYesterday,
      conversionRateOfVisitsToPaymentsCompareYesterday,
      newCardNumberToday,
      newCardNumberYesterday,
      newCardNumberCompareYesterday,
      cumulativeCardholders,
      cumulativeCardholdersYesterday,
      cumulativeCardholdersCompareYesterday,
      membershipCardPaymentFrequency,
      membershipCardPaymentFrequencyYesterday,
      membershipCardPaymentFrequencyCompareYesterday,
      membershipCardPaymentOrderNumber,
      membershipCardPaymentOrderNumberYesterday,
      membershipCardPaymentOrderNumberCompareYesterday,
      membershipCardPaymentAmount,
      membershipCardPaymentAmountYesterday,
      membershipCardPaymentAmountCompareYesterday,
      membershipCardPaymentAmountCompareSales,
    };
  }
  //获取趋势数据
  async trendStatistics(ctx: RequestContext, dateTime: Date, trendSource: TrendSource, type: TrendType) {
    const currentDate = new Date();

    if (trendSource === TrendSource.PageViews) {
      const pageViewsTrend = await this.promotionResultDetailService.getPageViewsTrend(ctx, type, dateTime);
      const pageViewsTrendToday = await this.promotionResultDetailService.getPageViewsTrend(ctx, type, currentDate);
      return {
        toDay: pageViewsTrendToday,
        yesterday: pageViewsTrend,
      };
    } else if (trendSource === TrendSource.TotalPayment) {
      const totalPaymentTrend = await this.promotionResultDetailService.getTotalPaymentTrend(ctx, type, dateTime);
      const totalPaymentTrendToday = await this.promotionResultDetailService.getTotalPaymentTrend(
        ctx,
        type,
        currentDate,
      );
      return {
        toDay: totalPaymentTrendToday,
        yesterday: totalPaymentTrend,
      };
    } else if (trendSource === TrendSource.VisitorsCount) {
      const visitorsCountTrend = await this.promotionResultDetailService.getVisitorsCountTrend(ctx, type, dateTime);
      const visitorsCountTrendToday = await this.promotionResultDetailService.getVisitorsCountTrend(
        ctx,
        type,
        currentDate,
      );
      return {
        toDay: visitorsCountTrendToday,
        yesterday: visitorsCountTrend,
      };
    } else if (trendSource === TrendSource.PaymentCustomerCount) {
      const paymentCustomerCountTrend = await this.promotionResultDetailService.getPaymentCustomerCountTrend(
        ctx,
        type,
        dateTime,
      );
      const paymentCustomerCountTrendToday = await this.promotionResultDetailService.getPaymentCustomerCountTrend(
        ctx,
        type,
        currentDate,
      );
      return {
        toDay: paymentCustomerCountTrendToday,
        yesterday: paymentCustomerCountTrend,
      };
    }
  }

  async saveStatistics() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const channels = await this.connection.getRepository(ctx, Channel).find();
    const currentDate = new Date();
    // 获取五分钟前的时间
    const statisticsDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      currentDate.getDate(),
      currentDate.getHours(),
      currentDate.getMinutes() - 5,
    );
    const timestampDays = this.getTimestampDays(statisticsDate);
    const timestampHourly = this.getTimestampHourly(statisticsDate);
    for (const channel of channels) {
      const channelId = channel.id;
      // 保存每日统计数据 - 系统  保存五分钟前的数据
      await this.saveSystemStatisticsDaily(ctx, channelId, timestampDays, statisticsDate);
      // 保存每小时统计数据 - 系统  保存五分钟前的数据
      await this.saveSystemStatisticsHourly(ctx, channelId, timestampHourly, statisticsDate);
    }
  }

  async saveSystemStatisticsDaily(ctx: RequestContext, channelId: ID, timestampDays: string, statisticsDate: Date) {
    // 保存每日统计数据 - 系统  保存五分钟前的数据
    const statisticsDaysKey = `${channelId}_${timestampDays}`;
    /** 保存每日访客和浏览量 */
    const statisticsDay = await this.kvsService.statistics.get(statisticsDaysKey);
    if (statisticsDay) {
      const systemDailyStats = await this.connection
        .getRepository(ctx, SystemDailyStats)
        .findOne({where: {timestampDays: timestampDays, channelId: channelId}});
      if (systemDailyStats) {
        systemDailyStats.timestampDays = timestampDays;
        systemDailyStats.visitorsCount = statisticsDay.visitorsCount;
        systemDailyStats.pageViews = statisticsDay.pageViews;
        systemDailyStats.statisticsTime = statisticsDate;
        await this.connection.getRepository(ctx, SystemDailyStats).save(systemDailyStats);
      } else {
        await this.connection.getRepository(ctx, SystemDailyStats).save({
          timestampDays: timestampDays,
          visitorsCount: statisticsDay.visitorsCount,
          pageViews: statisticsDay.pageViews,
          channelId: channelId,
          statisticsTime: statisticsDate,
        });
      }
    }
  }

  async saveSystemStatisticsHourly(ctx: RequestContext, channelId: ID, timestampHourly: string, statisticsDate: Date) {
    const statisticsHourlyKey = `${channelId}_${timestampHourly}`;
    const statisticsHourly = await this.kvsService.statistics.get(statisticsHourlyKey);
    if (statisticsHourly) {
      const systemHourlyStats = await this.connection
        .getRepository(ctx, SystemHourlyStats)
        .findOne({where: {timestampHourly: timestampHourly, channelId: channelId}});
      if (systemHourlyStats) {
        systemHourlyStats.visitorsCount = statisticsHourly.visitorsCount;
        systemHourlyStats.pageViews = statisticsHourly.pageViews;
        systemHourlyStats.statisticsTime = statisticsDate;
        await this.connection.getRepository(ctx, SystemHourlyStats).save(systemHourlyStats);
      } else {
        await this.connection.getRepository(ctx, SystemHourlyStats).save({
          timestampHourly: timestampHourly,
          visitorsCount: statisticsHourly.visitorsCount,
          pageViews: statisticsHourly.pageViews,
          channelId: channelId,
          statisticsTime: statisticsDate,
        });
      }
    }
  }

  async productVariantDataStatistics(
    ctx: RequestContext,
    productId: ID,
    startTime = new Date(0),
    endTime = new Date(),
    take = 10,
    skip = 0,
    sortName = 'productVariantId',
    sortType = 'DESC',
  ) {
    const query = this.connection
      .getRepository(ctx, ProductVariant)
      .createQueryBuilder('productVariant')
      .leftJoin('productVariant.product', 'product')
      .leftJoin('productVariant.channels', 'channel')
      .leftJoinAndSelect('productVariant.translations', 'translations')
      .leftJoinAndSelect('productVariant.productVariantPrices', 'productVariantPrices')
      .select('productVariant.id', 'productVariantId')
      .addSelect('productVariant.deletedAt', 'deletedAt')
      .addSelect('productVariant.costPrice', 'costPrice')
      .addSelect('MAX(productVariantPrices.price)', 'price')
      .addSelect('MAX(translations.name)', 'productVariantName')
      .andWhere('product.id = :productId', {productId: productId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('productVariantPrices.channelId = :channelId', {channelId: ctx.channelId})
      .groupBy('productVariant.id');
    query.andWhere(
      new Brackets(qbSql => {
        qbSql
          .orWhere('productVariant.deletedAt IS NULL')
          .orWhere('productVariant.deletedAt > :startTime', {startTime: startTime});
      }),
    );
    const count = await query.getCount();
    let datas = (await query.getRawMany<{
      productVariantId: string;
      deletedAt: Date;
      costPrice: number;
      price: number;
      productVariantName: string;
    }>()) as ProductVariantStatistics[];
    const productVariantPayData = await this.getProductVariantPayDataStatistics(ctx, startTime, endTime, productId);
    const productVariantAddCartData = await this.matomoService.getProductVariantAddCartDataStatistics(
      ctx,
      startTime,
      endTime,
    );
    const productVariantOrderData = await this.matomoService.getProductVariantOrderDataStatistics(
      ctx,
      startTime,
      endTime,
    );
    datas = this.productVariantDataBinding(
      datas,
      productVariantPayData,
      productVariantAddCartData,
      productVariantOrderData,
    );
    // 根据sortName排序
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    datas = datas.sort((a: any, b: any) => {
      if (sortType === 'DESC') {
        return Number(b[sortName] ?? 0) - Number(a[sortName] ?? 0);
      } else {
        return Number(a[sortName] ?? 0) - Number(b[sortName] ?? 0);
      }
    });
    // 截取数据
    datas = datas.slice(skip, skip + take);
    return {
      totalItems: count,
      items: datas,
    };
  }

  async getProductVariantPayDataStatistics(ctx: RequestContext, startTime: Date, endTime: Date, productId: ID) {
    const productVariantPayData = await this.connection
      .getRepository(ctx, OrderLinePromotionDetail)
      .createQueryBuilder('orderLinePromotionDetail')
      .leftJoinAndSelect('orderLinePromotionDetail.orderLine', 'orderLine')
      .leftJoinAndSelect('orderLinePromotionDetail.product', 'product')
      .leftJoinAndSelect('orderLinePromotionDetail.customer', 'customer')
      .leftJoinAndSelect('orderLine.productVariant', 'productVariant')
      .andWhere('product.id = :productId', {productId: productId})
      .leftJoinAndSelect('orderLinePromotionDetail.channels', 'channel')
      .andWhere('orderLinePromotionDetail.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .select([
        'SUM(orderLinePromotionDetail.count) AS quantity',
        'SUM(orderLinePromotionDetail.price) AS price',
        'productVariant.id AS productVariantId',
        'COUNT(DISTINCT customer.id) AS customerCount',
      ])
      .groupBy('productVariant.id')
      .getRawMany<{
        productVariantId: ID;
        quantity: number;
        price: number;
        customerCount: number;
      }>();
    return productVariantPayData;
  }

  async customPageDataStatistics(
    ctx: RequestContext,
    startTime = new Date(0),
    endTime = new Date(),
    take = 10,
    skip = 0,
    sortName = 'customPageId',
    sortType = 'DESC',
    pageName?: string,
  ) {
    // 获取页面的访问
    const customPageDataStatistics = await this.matomoService.getCustomPageDataStatistics(ctx, startTime, endTime);
    // 获取页面的点击
    const customPageDataClickStatistics = await this.matomoService.getCustomPageDataClickStatistics(
      ctx,
      startTime,
      endTime,
    );
    // 查询下单金额和下单人数
    const orderStats = await this.connection
      .getRepository(ctx, OrderTracking)
      .createQueryBuilder('orderTracking')
      .select([
        'SUM(orderTracking.orderAmount) AS totalOrderAmount',
        'COUNT(DISTINCT orderTracking.customerId) AS totalOrderCount',
        'orderTracking.sourceType AS sourceType',
        'orderTracking.sourceId AS sourceId',
      ])
      .andWhere('orderTracking.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .groupBy('orderTracking.sourceType, orderTracking.sourceId')
      .getRawMany();

    // 查询支付金额和支付人数
    const paymentStats = await this.connection
      .getRepository(ctx, OrderTracking)
      .createQueryBuilder('orderTracking')
      .select([
        'SUM(orderTracking.paymentAmount) AS totalPaymentAmount',
        'COUNT(DISTINCT orderTracking.customerId) AS totalPaymentCount',
        'orderTracking.sourceType AS sourceType',
        'orderTracking.sourceId AS sourceId',
      ])
      .andWhere('orderTracking.paymentAmount > 0')
      .andWhere('orderTracking.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .groupBy('orderTracking.sourceType, orderTracking.sourceId')
      .getRawMany();
    const qb = this.connection
      .getRepository(ctx, CustomPage)
      .createQueryBuilder('customPage')
      .leftJoinAndSelect('customPage.channels', 'channel')
      .select(['customPage.id AS customPageId', 'customPage.title AS customPageTitle'])
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (pageName) {
      qb.andWhere('customPage.title = :pageName', {pageName});
    }
    const customPageData = await qb.getRawMany<{
      customPageId: string;
      customPageTitle: string;
    }>();
    const customerPageTracking = this.customPageDataBinding(
      customPageDataStatistics,
      customPageDataClickStatistics,
      orderStats,
      paymentStats,
      customPageData,
    );
    // 根据sortName排序
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    customerPageTracking.sort((a: any, b: any) => {
      if (sortType === 'DESC') {
        return Number(b[sortName] ?? 0) - Number(a[sortName] ?? 0);
      } else {
        return Number(a[sortName] ?? 0) - Number(b[sortName] ?? 0);
      }
    });
    // 截取数据
    const result = customerPageTracking.slice(skip, skip + take);
    return {
      totalItems: customPageData.length,
      items: result,
    };
  }
  customPageDataBinding(
    customPageDataStatistics: {pageId: ID; visits: number; hits: number}[],
    customPageDataClickStatistics: {
      customPageId: ID;
      sumEventValue: number;
      //获取当前天数时间戳
      visits: number;
    }[],
    orderStats: {
      totalOrderAmount: number;
      totalOrderCount: number;
      sourceType: string;
      sourceId: ID;
    }[],
    paymentStats: {
      totalPaymentAmount: number;
      totalPaymentCount: number;
      sourceType: string;
      sourceId: ID;
    }[],
    customPageData: CustomPageStatistics[],
  ) {
    customPageData.forEach(data => {
      const customPageDataStatisticsData = customPageDataStatistics.find(
        item => String(item.pageId) === String(data.customPageId),
      );
      if (customPageDataStatisticsData) {
        data.pageViews = Number(customPageDataStatisticsData.hits);
        data.visitorsCount = Number(customPageDataStatisticsData.visits);
      } else {
        data.pageViews = 0;
        data.visitorsCount = 0;
      }
      const customPageClickData = customPageDataClickStatistics.find(
        item => String(item.customPageId) === String(data.customPageId),
      );
      if (customPageClickData) {
        data.clickCount = Number(customPageClickData.sumEventValue);
        data.clickPeopleCount = Number(customPageClickData.visits);
        data.clickRate = data.visitorsCount ? (data.clickPeopleCount / data.visitorsCount) * 100 : 0;
        data.jumpLossRate = 100 - data.clickRate;
      } else {
        data.clickCount = 0;
        data.clickPeopleCount = 0;
        data.clickRate = 0;
        data.jumpLossRate = 0;
      }
      const orderStat = orderStats.find(item => String(item.sourceId) === String(data.customPageId));
      if (orderStat) {
        data.orderAmount = Number(orderStat.totalOrderAmount);
        data.orderPeopleCount = Number(orderStat.totalOrderCount);
        data.orderConversionRate = data.visitorsCount ? (data.orderPeopleCount / data.visitorsCount) * 100 : 0;
      } else {
        data.orderAmount = 0;
        data.orderPeopleCount = 0;
        data.orderConversionRate = 0;
      }
      const paymentStat = paymentStats.find(item => String(item.sourceId) === String(data.customPageId));
      if (paymentStat) {
        data.paymentAmount = Number(paymentStat.totalPaymentAmount);
        data.paymentPeopleCount = Number(paymentStat.totalPaymentCount);
        data.paymentConversionRate = data.visitorsCount ? (data.paymentPeopleCount / data.visitorsCount) * 100 : 0;
      } else {
        data.paymentAmount = 0;
        data.paymentPeopleCount = 0;
        data.paymentConversionRate = 0;
      }
    });
    return customPageData;
  }

  productVariantDataBinding(
    datas: ProductVariantStatistics[],
    productVariantPayData: {productVariantId: ID; quantity: number; customerCount: number; price: number}[],
    productVariantAddCartDate: {productVariantId: ID; sumEventValue: number; visits: number}[],
    productVariantOrderDate: {productVariantId: ID; sumEventValue: number; visits: number}[],
  ): ProductVariantStatistics[] {
    datas.forEach(data => {
      const addCartData = productVariantAddCartDate.find(
        item => String(item.productVariantId) === String(data.productVariantId),
      );
      if (addCartData) {
        data.addCartPeopleCount = Number(addCartData.visits);
        data.addCartCount = Number(addCartData.sumEventValue);
      } else {
        data.addCartPeopleCount = 0;
        data.addCartCount = 0;
      }
      const orderData = productVariantOrderDate.find(
        item => String(item.productVariantId) === String(data.productVariantId),
      );
      if (orderData) {
        data.orderPeopleCount = Number(orderData.visits);
        data.orderCount = Number(orderData.sumEventValue);
      } else {
        data.orderPeopleCount = 0;
        data.orderCount = 0;
      }
      const payData = productVariantPayData.find(
        item => String(item.productVariantId) === String(data.productVariantId),
      );
      if (payData) {
        data.payPeopleCount = Number(payData.customerCount);
        data.payCount = Number(payData.quantity);
        data.payPrice = Number(payData.price);
      } else {
        data.payPeopleCount = 0;
        data.payCount = 0;
        data.price = 0;
      }
    });
    return datas;
  }

  async productDataStatistics(
    ctx: RequestContext,
    productName: string,
    startTime: Date,
    endTime: Date,
    collectionId: ID,
    take = 10,
    skip = 0,
    sortName = 'productId',
    sortType = 'DESC',
    // 是否分页
    isPage = true,
  ) {
    const productVisitorDate = await this.matomoService.getProductVisitorDataStatistics(ctx, startTime, endTime);
    const productAddCartDate = await this.matomoService.getProductAddCartDataStatistics(ctx, startTime, endTime);
    let sql =
      "SELECT Min(search_index_item.price) as price,Max(search_index_item.collectionIds) as collectionIds,search_index_item.productPreview, search_index_item.productId, MAX( search_index_item.productName) AS productName, Max( orderLinePromotionDetail.numberOfPayers ) AS numberOfPayers, Max( orderLinePromotionDetail.numberOfPaidItems ) AS numberOfPaidItems, Max( orderLinePromotionDetail.paymentAmount ) AS paymentAmount,Max( after_sale_combined.successfulRefundPrice)  AS numberOfSuccessfulRefundPrice, Max( after_sale_combined.quantity ) AS numberOfRefundItems, Max( after_sale_combined.successfulRefundQuantity ) AS numberOfSuccessfulRefunds, Max( after_sale_combined.successfulRefundOrders ) AS numberOfSuccessfulRefundOrders, Max( after_sale_combined.successfulRefundCustomers ) AS numberOfSuccessfulRefundPeople FROM search_index_item LEFT JOIN product ON product.id = search_index_item.productId LEFT JOIN( SELECT COUNT( DISTINCT customer.id ) AS numberOfPayers, SUM( orderLinePromotionDetail.count ) AS numberOfPaidItems, SUM( orderLinePromotionDetail.price ) AS paymentAmount, productId FROM order_line_promotion_detail AS orderLinePromotionDetail LEFT JOIN order_line_promotion_detail_channels_channel AS orderLinePromotionDetailChannels ON orderLinePromotionDetail.id = orderLinePromotionDetailChannels.orderLinePromotionDetailId LEFT JOIN channel ON orderLinePromotionDetailChannels.channelId = channel.id LEFT JOIN customer ON orderLinePromotionDetail.customerId = customer.id WHERE orderLinePromotionDetail.paymentTime BETWEEN ? AND ? AND channel.id = ? GROUP BY productId ) AS orderLinePromotionDetail ON orderLinePromotionDetail.productId = product.id LEFT JOIN ( SELECT SUM( CASE WHEN afterSale.state = 'successfulRefund' THEN afterSaleLines.price ELSE 0 END ) AS successfulRefundPrice,SUM( CASE WHEN afterSale.state != 'cancel' THEN afterSaleLines.quantity ELSE 0 END ) AS quantity, SUM( CASE WHEN afterSale.state = 'successfulRefund' THEN afterSaleLines.quantity ELSE 0 END ) AS successfulRefundQuantity, COUNT( DISTINCT CASE WHEN afterSale.state = 'successfulRefund' THEN orderLine.id END ) AS successfulRefundOrders, COUNT( DISTINCT CASE WHEN afterSale.state = 'successfulRefund' THEN customer.id END ) AS successfulRefundCustomers, productId FROM after_sale AS afterSale LEFT JOIN after_sale_channels_channel ON after_sale_channels_channel.afterSaleId = afterSale.id LEFT JOIN channel ON after_sale_channels_channel.channelId = channel.id LEFT JOIN after_sale_line AS afterSaleLines ON afterSale.id = afterSaleLines.afterSaleId LEFT JOIN order_line AS orderLine ON afterSaleLines.orderLineId = orderLine.id LEFT JOIN `order` ON `order`.id = orderLine.orderId LEFT JOIN customer AS customer ON `order`.customerId = customer.id LEFT JOIN product_variant AS productVariant ON orderLine.productVariantId = productVariant.id LEFT JOIN product AS product ON productVariant.productId = product.id WHERE afterSale.MODE != 'exchangeGoods' AND afterSale.createdAt BETWEEN ? AND ? AND channel.id = ? GROUP BY productId ) AS after_sale_combined ON after_sale_combined.productId = product.id WHERE product.customFieldsFreegift != TRUE AND search_index_item.channelId = ? {{collectionIds}} {{productName}} GROUP BY search_index_item.productId, search_index_item.productPreview";
    const params = [startTime, endTime, ctx.channelId, startTime, endTime, ctx.channelId, ctx.channelId];
    if (collectionId) {
      sql = sql.replace('{{collectionIds}}', ' AND FIND_IN_SET(?, search_index_item.collectionIds)');
      params.push(collectionId);
    } else {
      sql = sql.replace('{{collectionIds}}', '');
    }
    if (productName) {
      sql = sql.replace('{{productName}}', ' AND search_index_item.productName LIKE ?');
      params.push(`%${productName}%`);
    } else {
      sql = sql.replace('{{productName}}', '');
    }
    const countSql = `SELECT COUNT(*) AS total FROM (${sql}) AS total`;
    const countData = await this.connection.rawConnection.query(countSql, params);
    const totalItems = Number(countData[0].total ?? 0);
    let datas = await this.connection.rawConnection.query(sql, params);
    datas = this.productDataBinding(datas, productVisitorDate, productAddCartDate);
    // 根据sortName排序
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    datas = datas.sort((a: any, b: any) => {
      if (sortType === 'DESC') {
        return Number(b[sortName] ?? 0) - Number(a[sortName] ?? 0);
      } else {
        return Number(a[sortName] ?? 0) - Number(b[sortName] ?? 0);
      }
    });
    // 截取数据
    if (isPage) datas = datas.slice(skip, skip + take);
    //URL前缀
    const urlPrefix = `https://${process.env.MINIO_ENDPOINT_CDN}/${process.env.ADMIN_BUCKET}/`;
    datas.forEach((data: ProductStatistics) => {
      data.productPreview = urlPrefix + data.productPreview;
      data.conversionRateOfSingleProduct =
        Number(data.visitorsCount ?? 0) && Number(data.numberOfPayers ?? 0)
          ? (Number(data.numberOfPayers) / Number(data.visitorsCount)) * 100
          : 0;
    });
    return {
      totalItems,
      items: datas,
    };
  }
  productDataBinding(
    datas: ProductStatistics[],
    productVisitorDate: {productId: ID; visits: number; hits: number}[],
    productAddCartDate: {productId: ID; sumEventValue: number; visits: number}[],
  ): ProductStatistics[] {
    datas.forEach(data => {
      const visitorData = productVisitorDate.find(item => String(item.productId) === String(data.productId));
      if (visitorData) {
        data.visitorsCount = Number(visitorData.visits);
        data.pageViews = Number(visitorData.hits);
      } else {
        data.visitorsCount = 0;
        data.pageViews = 0;
      }
      const addCartData = productAddCartDate.find(item => String(item.productId) === String(data.productId));
      if (addCartData) {
        data.addCartPeopleCount = Number(addCartData.visits);
        data.addCartCount = Number(addCartData.sumEventValue);
      } else {
        data.addCartPeopleCount = 0;
        data.addCartCount = 0;
      }
    });
    return datas;
  }
  async customerStatistics(ctx: RequestContext, customerId: ID) {
    // 累积消费金额
    const cumulativeConsumptionAmount = await this.promotionResultDetailService.getCumulativeConsumptionAmount(
      ctx,
      customerId,
    );

    // 累积消费订单数
    const cumulativeConsumptionOrderNumber =
      await this.promotionResultDetailService.getCumulativeConsumptionOrderNumber(ctx, customerId);

    // 客单价
    const customerUnitPrice = cumulativeConsumptionOrderNumber
      ? cumulativeConsumptionAmount / cumulativeConsumptionOrderNumber
      : 0;
    //最近一次消费时间
    const lastConsumptionTime = await this.promotionResultDetailService.getLastConsumptionTime(ctx, customerId);
    //累积退款金额
    const cumulativeRefundAmount = await this.interfaceAfterSale.getCumulativeRefundAmount(ctx, customerId);
    //主动退款金额
    const activeRefundAmount = await this.promotionResultDetailService.getActiveRefundAmount(ctx, customerId);
    //累积退款订单数
    const cumulativeRefundOrderNumber = await this.interfaceAfterSale.getCumulativeRefundOrderNumber(ctx, customerId);
    return {
      cumulativeConsumptionAmount,
      cumulativeConsumptionOrderNumber,
      customerUnitPrice,
      lastConsumptionTime,
      cumulativeRefundAmount: Number(cumulativeRefundAmount) + Number(activeRefundAmount),
      cumulativeRefundOrderNumber,
    };
  }

  async timingPreprocessImage() {
    const imageSuffix = process.env.IMAGE_SUFFIX ?? '';
    const imageSuffixJpg = process.env.IMAGE_SUFFIX_JPG ?? '';
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const asset = await this.connection.getRepository(ctx, Asset).findOne({
      where: {
        customFields: {
          isNeedPreprocess: true,
        },
      },
      order: {id: 'DESC'},
    });
    if (!asset) {
      return '没有需要处理的图片';
    }
    if (!imageSuffix && !imageSuffixJpg) {
      return '没有设置图片后缀';
    }
    try {
      if (asset.mimeType !== 'image/gif') {
        await this.preprocessCompressImage(ctx, asset);
      }
    } catch (error) {
      Logger.error(`Failed to preprocess image: ${error.message}`, 'PreprocessCompressImage');
    } finally {
      await this.connection.getRepository(ctx, Asset).update(asset.id, {
        customFields: {
          isNeedPreprocess: false,
        },
      });
    }
  }
  async preprocessCompressImage(ctx: RequestContext, asset: Asset) {
    let oldSourcePath = asset.source;
    const oldSourceFileIdentifier = (asset.customFields as AssetCustomFields).oldSource;
    if (oldSourceFileIdentifier) {
      oldSourcePath = oldSourceFileIdentifier;
    }
    await this.fileProcessing(ctx, oldSourcePath, asset);
  }

  async timingCompressImage() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const asset = await this.connection.getRepository(ctx, Asset).findOne({
      where: {
        customFields: {
          // oldSource: IsNull(),
          isNeedHandle: true,
          retries: LessThan(5),
          // 修改回原图
          oldSource: Not(IsNull()),
        },
      },
      order: {id: 'DESC'},
    });
    if (!asset) {
      return '没有需要压缩的图片';
    }
    if (asset.mimeType === 'image/gif') {
      await this.connection.getRepository(ctx, Asset).save({
        id: asset.id,
        customFields: {
          oldSource: asset.source,
          isNeedHandle: false,
        },
      });
    } else {
      await this.compressLargeImage(ctx, asset);
    }
    return;
  }

  // private jobQueue: JobQueue<{
  //   ctx: SerializedRequestContext;
  //   assetId: ID;
  // }>;

  // async syncCompressImageJob(ctx: RequestContext, assetId: ID) {
  //   const job = await this.jobQueue.add({
  //     ctx: ctx.serialize(),
  //     assetId: assetId,
  //   });
  //   return job.name;
  // }

  // async onModuleInit() {
  //   this.jobQueue = await this.jobQueueService.createQueue({
  //     name: '压缩图片',
  //     process: async job => {
  //       const ctx = RequestContext.deserialize(job.data.ctx);
  //       const assetId = job.data.assetId;
  //       const rep = await this.compressImage(ctx, assetId);
  //       return rep;
  //     },
  //   });
  // }

  // async compressImage(ctx: RequestContext, assetId: ID) {
  //   const asset = await this.connection.getRepository(ctx, Asset).findOne({where: {id: assetId}});
  //   if (!asset) {
  //     return '图片不存在';
  //   }
  //   return this.compressLargeImage(ctx, asset);
  // }

  async compressLargeImage(ctx: RequestContext, asset: Asset) {
    const {assetOptions} = this.configService;
    const {assetStorageStrategy} = assetOptions;
    try {
      let sourceFileIdentifier = asset.source;
      const oldSourceFileIdentifier = (asset.customFields as AssetCustomFields).oldSource;
      let sourceFile = Buffer.from('');
      if (oldSourceFileIdentifier) {
        sourceFile = await assetStorageStrategy.readFileToBuffer(oldSourceFileIdentifier);
      } else {
        sourceFile = await assetStorageStrategy.readFileToBuffer(sourceFileIdentifier);
      }
      if (!oldSourceFileIdentifier) {
        const oleSourceFieIdentifier = await assetStorageStrategy.writeFileFromBuffer(
          'old/' + sourceFileIdentifier,
          sourceFile,
        );
        asset.customFields = {
          ...asset.customFields,
          oldSource: oleSourceFieIdentifier,
        };
      }
      // let newSourceFile = await this.processFile(sourceFile);
      let newSourceFile = sourceFile;
      sourceFileIdentifier = await assetStorageStrategy.writeFileFromBuffer(sourceFileIdentifier, newSourceFile);
      newSourceFile = await assetStorageStrategy.readFileToBuffer(sourceFileIdentifier);
      asset.fileSize = newSourceFile.byteLength;
      const {width, height} = sizeOf(newSourceFile);
      asset.width = width ?? 0;
      asset.height = height ?? 0;
      asset.customFields = {
        ...asset.customFields,
        isNeedHandle: false,
      };
      await this.connection.getRepository(ctx, Asset).save(asset);
      // 添加需更新的资源路径
      await this.updateAssetCache(ctx, asset);
      return '图片压缩成功';
    } catch (error) {
      const retries = ((asset.customFields as AssetCustomFields)?.retries ?? 0) + 1;
      await this.connection.getRepository(ctx, Asset).save({
        id: asset.id,
        customFields: {
          retries: retries,
        },
      });
      Logger.error(`更新图片大小失败: ${error.message}`);
      return '图片压缩失败';
    }
  }
  async fileProcessing(ctx: RequestContext, pathUrl: string, asset: Asset) {
    const imageSuffix = process.env.IMAGE_SUFFIX ?? '';
    const imageSuffixJpg = process.env.IMAGE_SUFFIX_JPG ?? '';
    if (imageSuffix) {
      try {
        await this.photoPretreatment(ctx, pathUrl, asset, this.parseQueryString(imageSuffix));
      } catch (error) {
        Logger.error(`Failed to transform image: ${error.message}`, 'PhotoPretreatment');
      }
    }
    if (imageSuffixJpg) {
      try {
        await this.photoPretreatment(ctx, pathUrl, asset, this.parseQueryString(imageSuffixJpg));
      } catch (error) {
        Logger.error(`Failed to transform image: ${error.message}`, 'PhotoPretreatment');
      }
    }
  }

  parseQueryString(queryString: string): Record<string, string> {
    // 去除开头的 '?'
    if (queryString.startsWith('?')) {
      queryString = queryString.substring(1);
    }
    // 拆分成各个键值对
    const params = queryString.split('&');
    const result: Record<string, string> = {};
    params.forEach(param => {
      const [key, value] = param.split('=');
      result[key] = value || '';
    });
    return result;
  }

  // 图片预处理
  async photoPretreatment(ctx: RequestContext, pathUrl: string, asset: Asset, queryParams: Record<string, string>) {
    const {assetOptions} = this.configService;
    const {assetStorageStrategy} = assetOptions;
    const decodedReqPath = decodeURIComponent(pathUrl);
    let file: Buffer;
    try {
      file = await assetStorageStrategy.readFileToBuffer(decodedReqPath);
    } catch (_err) {
      Logger.error(`Failed to read file: ${decodedReqPath}`, 'PhotoPretreatment');
      return;
    }
    const image = await transformImage(file, queryParams, this.presets || []);
    try {
      const imageBuffer = await image.toBuffer();
      const cachedFileName = this.getFileNameFromRequest(asset, queryParams);
      await assetStorageStrategy.writeFileFromBuffer(cachedFileName, imageBuffer);
      return;
    } catch (e) {
      Logger.error(`Failed to transform image: ${e.message}`, 'PhotoPretreatment');
      return;
    }
  }

  private getFileNameFromRequest(asset: Asset, query: Record<string, string>): string {
    const pathSource = asset.source;
    const {w, h, mode, preset, fpx, fpy, format} = query;
    const focalPoint = fpx && fpy ? `_fpx${fpx}_fpy${fpy}` : '';
    const imageFormat = getValidFormat(format);
    let imageParamHash: string | null = null;
    if (w || h) {
      const width = w || '';
      const height = h || '';
      imageParamHash = this.md5(`_transform_w${width}_h${height}_m${mode}${focalPoint}${imageFormat}`);
    } else if (preset) {
      if (this.presets && !!this.presets.find(p => p.name === preset)) {
        imageParamHash = this.md5(`_transform_pre_${preset}${focalPoint}${imageFormat}`);
      }
    } else if (imageFormat) {
      imageParamHash = this.md5(`_transform_${imageFormat}`);
    }
    /* eslint-enable @typescript-eslint/restrict-template-expressions */

    const decodedReqPath = decodeURIComponent(pathSource);
    if (imageParamHash) {
      return path.join(this.cacheDir, this.addSuffix(decodedReqPath, imageParamHash, imageFormat));
    } else {
      return decodedReqPath;
    }
  }

  public md5(input: string): string {
    return createHash('md5').update(input).digest('hex');
  }

  private addSuffix(fileName: string, suffix: string, ext?: string): string {
    const originalExt = path.extname(fileName);
    const effectiveExt = ext ? `.${ext}` : originalExt;
    const baseName = path.basename(fileName, originalExt);
    const dirName = path.dirname(fileName);
    return path.join(dirName, `${baseName}${suffix}${effectiveExt}`);
  }

  async updateAssetCache(ctx: RequestContext, asset: Asset) {
    const assetCacheKey = process.env.MINIO_ENDPOINT_CDN ?? '';
    const adminBucket = process.env.ADMIN_BUCKET ?? '';
    if (assetCacheKey) {
      const cacheKey = `${assetCacheKey}/${adminBucket}/${asset.source}`;
      await this.kvsService.cdnCacheRefresh.set(cacheKey, {isCompression: true});
      await this.kvsService.cdnCacheRefresh.set(asset.source, {isCompression: true});
    }
  }

  // async updateAssetSize(ctx: RequestContext, pageSize = 100, page = 1, isUpdateAll = false): Promise<string> {
  //   const assets = await this.connection.getRepository(ctx, Asset).find({
  //     skip: (page - 1) * pageSize,
  //     take: pageSize,
  //     // 图片宽度大于600
  //     where: {width: MoreThan(600), mimeType: In(['image/jpeg', 'image/png'])},
  //     order: {id: 'DESC'},
  //   });
  //   for (const asset of assets) {
  //     await this.compressLargeImage(ctx, asset);
  //   }
  //   if (isUpdateAll && assets.length === pageSize) {
  //     return this.updateAssetSize(ctx, pageSize, page + 1, isUpdateAll);
  //   }
  //   return '更新完成';
  // }

  // private async processFile(data: Buffer): Promise<Buffer> {
  //   // 转换为 WebP 格式，设置质量为 30
  //   const outputImageBuffer = await sharp(data).webp({quality: 30}).toBuffer();
  //   return outputImageBuffer;
  // }

  /**
   * 更新当前渠道的活动缓存时间
   * @param ctx
   * @param channelIds
   * @returns
   */
  async activeOrderTime(ctx: RequestContext, channelIds: ID[]) {
    if (channelIds.length <= 0) {
      return;
    }
    for (const channelId of channelIds) {
      await this.kvsService.activeTime.set(String(channelId), new Date());
    }
  }
  /**
   * 根据渠道数组过滤非默认渠道id
   * @param ctx
   * @param channels
   * @returns channelIds
   */
  filterDefaultChannel(channels: Channel[]) {
    let filteredChannels = channels;
    if (channels?.length > 1) {
      filteredChannels = channels.filter(channel => channel.code !== DEFAULT_CHANNEL_CODE);
    }
    return filteredChannels?.map(channel => channel.id) || [];
  }

  async getOrderTypeByPaymentCode(ctx: RequestContext, paymentCode: string) {
    const result = {
      orderType: PaymentOrderType.Order,
      orderId: '' as ID,
    };

    const findOrderType = async <T>(repository: new () => T, orderType: PaymentOrderType, idKey: string) => {
      const method = await this.connection
        .getRepository(ctx, repository)
        .createQueryBuilder('payment')
        .leftJoinAndSelect(`payment.${idKey}`, idKey)
        .select(`${idKey}.id`, 'id') // 使用动态的 idKey
        .where("payment.metadata ->> '$.transactionId' = :transactionId", {transactionId: paymentCode})
        .getRawOne<{
          id: ID;
        }>();

      if (method) {
        result.orderType = orderType;
        result.orderId = method.id ?? '';
        return true;
      }
      return false;
    };
    if (
      (await findOrderType(Payment, PaymentOrderType.Order, 'order')) ||
      (await findOrderType(GiftCardOrder, PaymentOrderType.GiftCardOrder, 'giftCard')) ||
      (await findOrderType(MembershipOrder, PaymentOrderType.MembershipOrder, 'membershipPlan'))
    ) {
      return result;
    }
    throw new UserInputError('支付单号不存在');
  }

  async reportedDistributorRecord(ctx: RequestContext, md5Str: string, sourceCode: string, distributionId?: string) {
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }
    const wechat = await this.weChatAuthService.getWeChatUserInfoByUserId(ctx, userId);
    if (!wechat?.openId) {
      throw new UserInputError('用户不存在');
    }
    return this.updateDistributorRecord(ctx, wechat.openId, md5Str, sourceCode, distributionId);
  }

  async updateDistributorRecord(
    ctx: RequestContext,
    openId: string,
    md5Str: string,
    sourceCode: string,
    distributionId?: string,
  ) {
    try {
      const isReported = await this.kvsService.reportedDistributorRecord.get(
        `${openId}_${md5Str}_${sourceCode}_${distributionId}`,
      );
      if (isReported) {
        return true;
      }
      await this.kvsService.reportedDistributorRecord.set(
        `${openId}_${md5Str}_${sourceCode}_${distributionId}`,
        60 * 60 * 24,
      );
      let shareData = await this.analysisShareDataByNanoid(ctx, md5Str);
      const isDistributor = shareData?.distributorId || distributionId ? true : false;
      const reportedDistributorRecord = await this.getReportedDistributorRecord(ctx, openId);
      if (reportedDistributorRecord) {
        if (isDistributor) {
          // 如果记录的分销员id为空或者与当前的分销员id不一致 则更新记录
          if (
            !reportedDistributorRecord.shareData?.distributorId ||
            (reportedDistributorRecord.shareData.distributorId !== shareData?.distributorId &&
              reportedDistributorRecord.shareData?.distributorId !== distributionId)
          ) {
            if (shareData?.distributorId) {
              reportedDistributorRecord.shareData = shareData as ShareData;
            } else {
              reportedDistributorRecord.shareData = {
                distributorId: distributionId,
              } as ShareData;
            }
            reportedDistributorRecord.md5Str = md5Str;
            reportedDistributorRecord.sourceCode = sourceCode;
            reportedDistributorRecord.lastReportTime = new Date();
            reportedDistributorRecord.lastDistributorReportTime = new Date();
          } else {
            reportedDistributorRecord.lastReportTime = new Date();
            reportedDistributorRecord.lastDistributorReportTime = new Date();
          }
        } else {
          reportedDistributorRecord.reportEmptyDistributorCount += 1;
          reportedDistributorRecord.lastNotDistributorReportTime = new Date();
        }
        await this.connection.getRepository(ctx, DistributorRecord).save(reportedDistributorRecord);
      } else {
        if (!shareData && distributionId) {
          shareData = {
            distributorId: distributionId,
          } as ShareData;
        }
        const newReportedDistributorRecord = new DistributorRecord({
          openId,
          shareData,
          md5Str,
          sourceCode,
          lastReportTime: new Date(),
          lastDistributorReportTime: isDistributor ? new Date() : null,
          lastNotDistributorReportTime: isDistributor ? null : new Date(),
          reportEmptyDistributorCount: isDistributor ? 0 : 1,
          channelId: ctx.channelId,
        });
        await this.connection.getRepository(ctx, DistributorRecord).save(newReportedDistributorRecord);
      }
      return true;
    } catch (error) {
      Logger.error(`Failed to update distributor record: ${error.message}`, 'updateDistributorRecord');
      return false;
    }
  }

  async getReportedDistributorRecord(ctx: RequestContext, openId: string) {
    const reportedDistributorRecord = await this.connection
      .getRepository(ctx, DistributorRecord)
      .createQueryBuilder('distributorRecord')
      .andWhere('distributorRecord.openId = :openId', {openId})
      .andWhere('distributorRecord.channelId = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    return reportedDistributorRecord;
  }

  async getSubscribeMessageTemplateId(ctx: RequestContext) {
    const templates = await this.connection
      .getRepository(ctx, TemplateConfig)
      .createQueryBuilder('templateConfig')
      .where('templateConfig.channelId = :channelId', {channelId: ctx.channelId})
      .getMany();

    const couponGrants = [];
    const blindBoxActivityBooking = [];
    for (const template of templates) {
      if (template.templateType === TemplateType.CouponGrants) {
        couponGrants.push(template.templateId);
      } else if (template.templateType === TemplateType.BlindBoxActivityBooking) {
        blindBoxActivityBooking.push(template.templateId);
      }
    }
    return {
      //优惠券发放成功通知
      couponGrants: couponGrants,
      blindBoxActivityBooking: blindBoxActivityBooking,
    };
  }

  // 获取会员卡限制折扣次数
  async getMemberPromotionCount(ctx: RequestContext, member: Member) {
    const memberRestrictedUse = member.restrictedUse || 0;
    const memberPlanRestrictedUse = member.membershipPlan.rightsDiscount.restrictedUse || 0;
    const discountLimitSourceType = member?.membershipPlan?.discountLimitSourceType;
    if (discountLimitSourceType === MemberCardDiscountLimitSourceType.CustomerMember) {
      return memberRestrictedUse || memberPlanRestrictedUse;
    }
    return memberPlanRestrictedUse;
  }
}
