import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {KvsService, CacheKeyManagerService} from '@scmally/kvs';
import {
  AbstractDistributor,
  GiftCardOrder,
  GiftCardReturn,
  Member,
  MemberReturnCard,
  MemberService,
  MembershipOrder,
} from '@scmally/member';
import {RedLockService} from '@scmally/red-lock';
import {WeChatAuthService} from '@scmally/wechat';
import {
  ChannelService,
  Customer,
  CustomerService,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderLine,
  Product,
  RelationPaths,
  RequestContext,
  RequestContextService,
  Transaction,
  TransactionalConnection,
  UnauthorizedError,
} from '@vendure/core';
import {DateTime} from 'luxon';
import {Brackets, In} from 'typeorm';
import {
  Distributor,
  DistributorBinding,
  DistributorCustomer,
  DistributorDetail,
  DistributorGroup,
  DistributorGroupBinding,
  DistributorOrder,
  DistributorProductRecord,
  MerchantVoluntaryRefund,
  OrderLinePromotionDetail,
} from '../entities';
import {CustomerBindingDistributor} from '../entities/customer-binding-distributor.entity';
import {
  DeletionResult,
  DistributorInput,
  DistributorSharingInput,
  ProgramLinkInput,
  SharingType,
} from '../generated-admin-types';
import {
  AfterSaleState,
  CustomerCustomFields,
  CustomerType,
  DeletionResponse,
  MemberSource,
  MemberState,
  MemberStateInput,
  OrderCustomFields,
  OrderLineCustomFields,
  ValidityPeriodType,
} from '../generated-shop-types';
import {InterfaceAfterSale} from './abstract-after-sale';
import {BlindBoxOrderService} from './blind-box';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {DistributorGroupService} from './distributor-group.service';
import {MatomoService} from './matomo.service';
@Injectable()
export class DistributorService extends AbstractDistributor {
  interfaceAfterSale: InterfaceAfterSale;
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private commonService: CommonService,
    private customerService: CustomerService,
    private requestContextService: RequestContextService,
    private kvsService: KvsService,
    private distributorGroupService: DistributorGroupService,
    public memberService: MemberService,
    private matomoService: MatomoService,
    private redLockService: RedLockService,
    private weChatAuthService: WeChatAuthService,
    private cacheService: CacheService,
    @Inject(forwardRef(() => BlindBoxOrderService))
    private blindBoxOrderService: BlindBoxOrderService,
  ) {
    super(memberService);
  }

  registerAfterSale(interfaceAfterSale: InterfaceAfterSale) {
    this.interfaceAfterSale = interfaceAfterSale;
  }
  distributorRelations: RelationPaths<Distributor> = [
    'distributorBindings',
    'distributorOrders',
    'distributorOrders.order',
    'distributorOrders.order.lines',
  ];
  async distributorOrderByOrderId(ctx: RequestContext, orderId: ID, relations: RelationPaths<DistributorOrder>) {
    const distributionOrder = await this.connection.getRepository(ctx, DistributorOrder).findOne({
      where: {order: {id: orderId}},
      relations,
    });
    return distributionOrder;
  }

  async accumulationAmount(ctx: RequestContext, distributor: Distributor) {
    let orderTotal = 0;
    if (!distributor.distributorBindings || !distributor.distributorOrders) {
      return 0;
    }
    for (const distributorOrder of distributor.distributorOrders) {
      const orderId = distributorOrder.order.id;
      if (distributorOrder.order.state !== 'Cancelled') {
        const promResult = (distributorOrder.order.customFields as OrderCustomFields)?.orderPromotionResult;
        const orderLinePromResults = promResult?.promResult?.orderLinePromResults;
        if (!orderLinePromResults) {
          continue;
        }
        // 排除售后成功的orderLine
        const orderLines: OrderLine[] = distributorOrder.order.lines.filter(line => {
          const aftersale = (line.customFields as OrderLineCustomFields)?.afterSale;
          if (!aftersale) {
            return true;
          }
          return (
            aftersale.state !== AfterSaleState.AgreeToRefund &&
            aftersale.state !== AfterSaleState.SuccessfulRefund &&
            aftersale.state !== AfterSaleState.Complete
          );
        });
        const orderLineIds = orderLines.map(line => line.id);
        for (const orderLinePromResult of orderLinePromResults) {
          if (!orderLinePromResult) {
            continue;
          }
          const orderLineId = orderLinePromResult?.orderLineId;
          if (!orderLineId) {
            continue;
          }
          if (orderLineIds.includes(orderLineId)) {
            const price = Number(orderLinePromResult?.price || 0);
            const discountAmount = Number(orderLinePromResult?.discountAmount || 0);
            let surchargeAmount = 0;
            // 计算surcharge 存在后台改价的情况
            if (promResult?.promResult?.surcharge) {
              promResult.promResult.surcharge?.details?.map(surchargeDetails => {
                if (orderLineId === surchargeDetails?.orderLineId) {
                  surchargeAmount += Number(surchargeDetails?.amount) || 0;
                }
              });
            }
            let merchantVoluntaryRefundAmount = 0;
            // 计算商家自主退款
            const merchantVoluntaryRefunds = await this.connection
              .getRepository(ctx, MerchantVoluntaryRefund)
              .createQueryBuilder('merchantVoluntaryRefund')
              .andWhere('merchantVoluntaryRefund.orderId = :orderId', {orderId})
              .andWhere('merchantVoluntaryRefund.orderLineId = :lineId', {lineId: orderLineId})
              .getMany();
            for (const merchantVoluntaryRefund of merchantVoluntaryRefunds) {
              merchantVoluntaryRefundAmount += Number(merchantVoluntaryRefund.price);
            }
            orderTotal += Number(price - discountAmount + surchargeAmount - merchantVoluntaryRefundAmount) || 0;
          }
        }
      }
    }
    return orderTotal;
  }

  async getDistributor(ctx: RequestContext, customerId?: ID) {
    if (!customerId) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId);
      if (!customer) {
        throw new Error('customer not exist');
      }
      customerId = customer.id;
    }
    const distributors = await this.connection
      .getRepository(ctx, Distributor)
      .createQueryBuilder('distributor')
      .leftJoin('distributor.channels', 'channel')
      .leftJoinAndSelect('distributor.distributorGroup', 'distributorGroup')
      .leftJoinAndSelect('distributor.customer', 'customer')
      .andWhere(`distributor.deletedAt IS NULL`)
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('customer.id =:customerId', {customerId: customerId})
      .getMany();
    if (distributors.length > 0) {
      return distributors[0];
    }
    return;
  }

  async softDeleteDistributor(ctx: RequestContext, distributorId: ID): Promise<DeletionResponse> {
    const distributor = await this.findOne(ctx, distributorId);
    if (!distributor) {
      throw new Error('distributor not exist');
    }
    distributor.deletedAt = new Date();
    await this.connection
      .getRepository(ctx, Customer)
      .update({customFields: {distributor: {id: distributor.id}}}, {customFields: {distributor: null}});
    await this.connection.getRepository(ctx, Distributor).save(distributor);
    return {
      result: DeletionResult.Deleted,
    };
  }

  @Transaction()
  async addDistributorToGiftCardOrder(ctx: RequestContext, giftCardOrderId: ID) {
    const giftCardOrder = await this.connection.getRepository(ctx, GiftCardOrder).findOne({
      where: {id: giftCardOrderId},
      relations: ['customer'],
    });
    if (!giftCardOrder) {
      throw new Error('giftCardOrder not exist');
    }
    const customer = giftCardOrder.customer;
    if (!customer) {
      throw new Error('customer not exist');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    let distributorId = customerBindingDistributor?.distributorId;
    if (!distributorId) {
      const newDistributorId = await this.distributorRebinding(ctx, customer.id);
      if (!newDistributorId) {
        Logger.error('The user is not bound to a distributor');
        return;
      }
      distributorId = newDistributorId;
      return;
    }
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      Logger.error('分销员不存在');
      return;
    }
    const distributorGroup = distributor.distributorGroup;
    const totalPrice = giftCardOrder.amount;
    const distributorOrder = new DistributorOrder({
      customer: customer,
      giftCardOrder: {id: giftCardOrderId},
      distributor: {id: distributorId},
      price: Number(totalPrice),
      distributorGroup: {id: distributorGroup.id},
    });
    await this.connection.getRepository(ctx, DistributorOrder).save(distributorOrder);
    await this.saveDistributorDetailByOrderPaymentSettled(ctx, distributor, totalPrice);
  }

  async getUserDistribution(ctx: RequestContext, customerId?: ID) {
    let distributorId: ID = 0;
    let distributionGroupId: ID = 0;
    if (!customerId) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId);
      if (!customer) {
        throw new Error('customer not exist');
      }
      customerId = customer.id;
    }
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer) {
      throw new Error('customer not exist');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customerId);
    distributorId = customerBindingDistributor?.distributorId as ID;
    if (distributorId) {
      const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
      if (distributor) {
        const distributorGroup = distributor.distributorGroup;
        distributionGroupId = distributorGroup?.id ?? 0;
      }
    }
    return {distributorId, distributionGroupId};
  }

  @Transaction()
  async addDistributorToBlindBoxOrder(ctx: RequestContext, blindBoxBuyId: ID) {
    const blindBoxBuy = await this.blindBoxOrderService.blindBoxBuy(ctx, blindBoxBuyId);
    if (!blindBoxBuy) {
      throw new Error('blindBoxBuy not exist');
    }
    const customer = blindBoxBuy.customer;
    if (!customer) {
      throw new Error('customer not exist');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    let distributorId = customerBindingDistributor?.distributorId;
    if (!distributorId) {
      const newDistributorId = await this.distributorRebinding(ctx, customer.id);
      if (!newDistributorId) {
        Logger.error('The user is not bound to a distributor');
        return;
      }
      distributorId = newDistributorId;
      return;
    }
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      Logger.error('分销员不存在');
      return;
    }
    const distributorGroup = distributor.distributorGroup;
    const totalPrice = blindBoxBuy.price;
    const distributorOrder = new DistributorOrder({
      customer: customer,
      blindBoxBuy: {id: blindBoxBuy.id},
      distributor: {id: distributorId},
      price: Number(totalPrice),
      distributorGroup: {id: distributorGroup.id},
    });
    await this.connection.getRepository(ctx, DistributorOrder).save(distributorOrder);
    await this.saveDistributorDetailByOrderPaymentSettled(ctx, distributor, totalPrice);
  }

  @Transaction()
  async addDistributorToMemberOrder(ctx: RequestContext, membershipOrderId: ID) {
    const membershipOrder = await this.connection.getRepository(ctx, MembershipOrder).findOne({
      where: {id: membershipOrderId},
      relations: ['customer'],
    });
    if (!membershipOrder) {
      throw new Error('membershipOrder not exist');
    }
    if (membershipOrder.source !== MemberSource.OwnMall && membershipOrder.source !== MemberSource.OrderBuy) {
      Logger.info('只有自营商城的会员订单才会产生分销订单');
      return;
    }
    const customer = membershipOrder.customer;
    if (!customer) {
      throw new Error('customer not exist');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    let distributorId = customerBindingDistributor?.distributorId;
    if (!distributorId) {
      const newDistributorId = await this.distributorRebinding(ctx, customer.id);
      if (!newDistributorId) {
        Logger.error('The user is not bound to a distributor');
        return;
      }
      distributorId = newDistributorId;
      return;
    }
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      Logger.error('分销员不存在');
      return;
    }
    const distributorGroup = distributor.distributorGroup;
    const totalPrice = membershipOrder.amount;
    const distributorOrder = new DistributorOrder({
      customer: customer,
      membershipOrder: {id: membershipOrderId},
      distributor: {id: distributorId},
      price: Number(totalPrice),
      distributorGroup: {id: distributorGroup.id},
    });
    await this.connection.getRepository(ctx, DistributorOrder).save(distributorOrder);
    if (membershipOrder.source === MemberSource.OwnMall) {
      await this.saveDistributorDetailByOrderPaymentSettled(ctx, distributor, totalPrice);
    }
  }

  @Transaction()
  async addDistributorToOrder(ctx: RequestContext, order: Order) {
    const newOrder = await this.connection.getRepository(ctx, Order).findOne({
      where: {id: order.id},
      relations: ['lines', 'lines.productVariant', 'lines.productVariant.product', 'customer'],
    });
    const customer = newOrder?.customer;
    if (!customer) {
      Logger.error('Payment upon completion of order ,customer not exist');
      return;
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    let distributorId = customerBindingDistributor?.distributorId;
    if (!distributorId) {
      Logger.debug('The user is not bound to a distributor');
      const newDistributorId = await this.distributorRebinding(ctx, customer.id);
      if (!newDistributorId) {
        Logger.error('The user is not bound to a distributor');
        return;
      }
      distributorId = newDistributorId;
    }
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      Logger.error('The distributor does not exist');
      return;
    }
    const distributorGroup = distributor.distributorGroup;
    const orderTotalPrice =
      (order.customFields as OrderCustomFields).orderPromotionResult?.promResult?.orderTotalPrice ?? 0;
    const distributorOrder = new DistributorOrder({
      customer: customer,
      order: order,
      distributor: {id: distributorId},
      price: Number(orderTotalPrice),
      distributorGroup: {id: distributorGroup.id},
    });
    await this.connection.getRepository(ctx, DistributorOrder).save(distributorOrder);
    await this.saveDistributorDetailByOrderPaymentSettled(ctx, distributor, orderTotalPrice);
  }

  // 分销重新绑定
  async distributorRebinding(ctx: RequestContext, customerId: ID): Promise<ID | null> {
    try {
      const distributorBinding = await this.getDistributorBinding(ctx, customerId);
      let distributorId = '' as ID;
      if (distributorBinding) {
        distributorId = distributorBinding.distributor.id;
      } else {
        const customer = await this.customerService.findOne(ctx, customerId);
        if (!customer) {
          Logger.error('customer not exist');
          return null;
        }
        const wechat = await this.weChatAuthService.getUserByPhone(ctx, customer.phoneNumber);
        if (!wechat) {
          Logger.error('wechat not exist');
          return null;
        }
        const openId = wechat.openId;
        if (openId) {
          const distributorRecord = await this.commonService.getReportedDistributorRecord(ctx, openId);
          if (distributorRecord?.shareData?.distributorId) {
            distributorId = distributorRecord?.shareData?.distributorId;
          } else {
            Logger.error('The distributorId does not exist');
            return null;
          }
        } else {
          Logger.error('The distributorBinding does not exist');
          return null;
        }
      }
      if (distributorId) {
        await this.switchoverDistributor(ctx, distributorId, customerId);
      }
      return distributorId;
    } catch (error) {
      Logger.error(`distributorRebinding error:${error}`);
      return null;
    }
  }

  // 查询用户最新的一条绑定记录
  async getDistributorBinding(ctx: RequestContext, customerId: ID) {
    const distributorBinding = await this.connection
      .getRepository(ctx, DistributorBinding)
      .createQueryBuilder('distributorBinding')
      .leftJoinAndSelect('distributorBinding.customer', 'customer')
      .leftJoinAndSelect('distributorBinding.distributor', 'distributor')
      .leftJoinAndSelect('distributorBinding.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('customer.id = :customerId', {customerId})
      .orderBy('distributorBinding.createdAt', 'DESC')
      .getMany();
    if (distributorBinding.length > 0) {
      return distributorBinding[0];
    }
    return null;
  }

  // 分销明细金额扣除 订单扣除
  async subtractDistributionDetailOrderAmount(
    ctx: RequestContext,
    distributorId: ID,
    distributorGroupId: ID,
    placedAt: Date,
    price: number,
  ) {
    const lock = await this.redLockService.lockResource(`Distributor:DistributorDetail:${distributorId}`);
    try {
      const distributorDetail = await this.getDistributionDetail(ctx, distributorId, placedAt, distributorGroupId);
      if (!distributorDetail) {
        return;
      }
      const effectiveOrderNum = distributorDetail.effectiveOrderNum - 1;
      const orderAccumulationAmount = Number(distributorDetail.orderAccumulationAmount) - Number(price);
      await this.connection.getRepository(ctx, DistributorDetail).update(distributorDetail.id, {
        effectiveOrderNum: effectiveOrderNum > 0 ? effectiveOrderNum : 0,
        orderAccumulationAmount: orderAccumulationAmount > 0 ? orderAccumulationAmount : 0,
      });
      return {effectiveOrderNum, orderAccumulationAmount};
    } catch (error) {
      Logger.error(`subtractDistributionDetailOrderAmount error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  @Transaction()
  async cancelBlindBoxOrderDistributorDetail(ctx: RequestContext, blindBoxBuyId: ID) {
    const blindBoxBuy = await this.blindBoxOrderService.blindBoxBuy(ctx, blindBoxBuyId);
    if (!blindBoxBuy) {
      throw new Error('blindBoxBuy not exist');
    }
    const placedAt = blindBoxBuy.paymentAt;
    if (!placedAt) {
      return;
    }
    const distributorOrder = await this.connection
      .getRepository(ctx, DistributorOrder)
      .findOne({where: {blindBoxBuy: {id: blindBoxBuy.id}}, relations: ['distributor', 'distributorGroup']});
    if (!distributorOrder) {
      return;
    }
    return this.subtractDistributionDetailOrderAmount(
      ctx,
      distributorOrder.distributor.id,
      distributorOrder.distributorGroup?.id,
      placedAt,
      blindBoxBuy.price,
    );
  }

  @Transaction()
  async cancelMemberOrderDistributorDetail(ctx: RequestContext, memberOrderId: ID, price: number) {
    const membershipOrder = await this.connection.getRepository(ctx, MembershipOrder).findOne({
      where: {id: memberOrderId},
      relations: ['customer'],
    });
    if (!membershipOrder) {
      throw new Error('membershipOrder not exist');
    }
    const memberOrderPlacedAt = membershipOrder.payTime;
    if (!memberOrderPlacedAt) {
      return;
    }
    const distributorOrder = await this.connection
      .getRepository(ctx, DistributorOrder)
      .findOne({where: {membershipOrder: {id: memberOrderId}}, relations: ['distributor', 'distributorGroup']});
    if (!distributorOrder) {
      return;
    }
    return this.subtractDistributionDetailOrderAmount(
      ctx,
      distributorOrder.distributor.id,
      distributorOrder.distributorGroup?.id,
      memberOrderPlacedAt,
      price,
    );
  }
  @Transaction()
  async cancelGiftCardOrderDistributorDetail(ctx: RequestContext, giftCardOrderId: ID, price: number) {
    const giftCardOrder = await this.connection.getRepository(ctx, GiftCardOrder).findOne({
      where: {id: giftCardOrderId},
      relations: ['customer'],
    });
    if (!giftCardOrder) {
      throw new Error('giftCardOrder not exist');
    }
    const placedAt = giftCardOrder.payTime;
    if (!placedAt) {
      return;
    }
    const distributorOrder = await this.connection
      .getRepository(ctx, DistributorOrder)
      .findOne({where: {giftCardOrder: {id: giftCardOrderId}}, relations: ['distributor', 'distributorGroup']});
    if (!distributorOrder) {
      return;
    }
    return this.subtractDistributionDetailOrderAmount(
      ctx,
      distributorOrder.distributor.id,
      distributorOrder.distributorGroup?.id,
      placedAt,
      price,
    );
  }

  @Transaction()
  async cancelOrderDistributorDetail(ctx: RequestContext, order: Order) {
    const newOrder = await this.connection.getRepository(ctx, Order).findOne({
      where: {id: order.id},
      relations: ['lines', 'lines.productVariant', 'lines.productVariant.product', 'customer'],
    });
    const orderPlacedAt = newOrder?.orderPlacedAt;
    if (!orderPlacedAt) {
      return;
    }
    const distributorOrder = await this.connection
      .getRepository(ctx, DistributorOrder)
      .findOne({where: {order: {id: order.id}}, relations: ['distributor', 'distributorGroup']});
    if (!distributorOrder) {
      return;
    }
    return this.subtractDistributionDetailOrderAmount(
      ctx,
      distributorOrder.distributor.id,
      distributorOrder.distributorGroup?.id,
      orderPlacedAt,
      0,
    );
  }

  @Transaction()
  async subtractDistributorDetailOrderAmount(ctx: RequestContext, order: Order, price: number) {
    const newOrder = await this.connection.getRepository(ctx, Order).findOne({
      where: {id: order.id},
      relations: ['lines', 'lines.productVariant', 'lines.productVariant.product', 'customer'],
    });
    const orderPlacedAt = newOrder?.orderPlacedAt;
    if (!orderPlacedAt) {
      return;
    }
    const distributorOrder = await this.connection
      .getRepository(ctx, DistributorOrder)
      .findOne({where: {order: {id: order.id}}, relations: ['distributor', 'distributorGroup']});
    if (!distributorOrder) {
      return;
    }
    const distributorId = distributorOrder.distributor.id;
    const distributorGroupId = distributorOrder.distributorGroup?.id;
    const lock = await this.redLockService.lockResource(`Distributor:DistributorDetail:${distributorId}`);
    try {
      const distributorDetail = await this.getDistributionDetail(ctx, distributorId, orderPlacedAt, distributorGroupId);
      if (!distributorDetail) {
        return;
      }
      const orderAccumulationAmount = distributorDetail.orderAccumulationAmount - price;
      Logger.info(`订单${order.id}退款扣除分销商订单金额,原订单金额:${distributorDetail.orderAccumulationAmount}`);
      await this.connection.getRepository(ctx, DistributorDetail).update(distributorDetail.id, {
        orderAccumulationAmount: orderAccumulationAmount,
      });
      Logger.info(`订单${order.id}退款扣除分销商订单金额成功,新的订单金额:${orderAccumulationAmount}`);
    } catch (error) {
      Logger.error(`subtractDistributorDetailOrderAmount error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async getDistributionDetail(ctx: RequestContext, distributorId: ID, date = new Date(), distributorGroupId?: ID) {
    const startDateTime = DateTime.fromJSDate(date).startOf('day').toJSDate();
    const endDateTime = DateTime.fromJSDate(date).endOf('day').toJSDate();
    const query = this.connection
      .getRepository(ctx, DistributorDetail)
      .createQueryBuilder('distributorDetail')
      .andWhere('distributorDetail.distributorId = :distributorId', {distributorId: distributorId})
      .andWhere('distributorDetail.detailTime BETWEEN :startDateTime AND :endDateTime', {startDateTime, endDateTime});
    if (distributorGroupId) {
      query.leftJoinAndSelect(`${query.alias}.distributorGroup`, 'distributorGroup');
      query.andWhere('distributorGroup.id = :distributorGroupId', {
        distributorGroupId: distributorGroupId,
      });
    }
    const distributorOrder = await query.take(1).getOne();
    return distributorOrder;
  }

  async saveDistributorDetailByOrderPaymentSettled(
    ctx: RequestContext,
    distributor: Distributor,
    orderTotalPrice: number,
    date?: Date,
  ) {
    if (!distributor) {
      return;
    }
    if (!date) {
      date = new Date();
    }
    const distributorGroup = distributor.distributorGroup;
    const lock = await this.redLockService.lockResource(`Distributor:DistributorDetail:${distributor.id}`);
    try {
      const distributionDetail = await this.getDistributionDetail(ctx, distributor.id, date, distributorGroup.id);
      if (!distributionDetail) {
        const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
          ctx,
          distributor.id,
          date,
          distributorGroup.id,
        );
        await this.connection.getRepository(ctx, DistributorDetail).save({
          distributor: {id: distributor.id},
          detailTime: date,
          effectiveCustomerNum,
          effectiveOrderNum: 1,
          customerTotal: customerCount,
          orderAccumulationAmount: orderTotalPrice,
          orderTotalAmount: orderTotalPrice,
          orderNum: 1,
          distributorGroup: {id: distributorGroup.id},
        });
      } else {
        await this.connection.getRepository(ctx, DistributorDetail).update(distributionDetail.id, {
          effectiveOrderNum: distributionDetail.effectiveOrderNum + 1,
          orderTotalAmount: distributionDetail.orderTotalAmount + orderTotalPrice,
          orderAccumulationAmount: distributionDetail.orderAccumulationAmount + orderTotalPrice,
          orderNum: distributionDetail.orderNum + 1,
        });
      }
    } catch (error) {
      Logger.error(`saveDistributorDetailByOrderPaymentSettled error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async distributorDetailStatistics(ctx: RequestContext) {
    const distributionDetails = await this.connection
      .getRepository(ctx, DistributorDetail)
      .createQueryBuilder('distributorDetail')
      .leftJoinAndSelect('distributorDetail.distributor', 'distributor')
      .leftJoinAndSelect('distributorDetail.distributorGroup', 'distributorGroup')
      .getMany();
    for (const distributionDetail of distributionDetails) {
      const distributor = await this.findOne(ctx, distributionDetail.distributor.id);
      if (!distributor) {
        continue;
      }
      const distributorId = distributor.id;
      const distributorGroupId = distributionDetail.distributorGroup?.id;
      if (!distributorGroupId) {
        continue;
      }
      const date = distributionDetail.detailTime;
      const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
        ctx,
        distributor.id,
        date,
        distributorGroupId,
      );
      const effectiveOrderNum = await this.getDistributorOrderDetail(ctx, distributorId, date, distributorGroupId);
      Logger.info(
        `分销商${distributorId}统计,有效客户数:${effectiveCustomerNum},客户总数:${customerCount},有效订单数:${effectiveOrderNum}`,
      );
      await this.connection.getRepository(ctx, DistributorDetail).update(distributionDetail.id, {
        effectiveCustomerNum: effectiveCustomerNum,
        customerTotal: customerCount,
        effectiveOrderNum: effectiveOrderNum,
      });
    }
    return 'success';
  }

  async getDistributorOrderDetail(ctx: RequestContext, distributorId: ID, date = new Date(), distributorGroupId?: ID) {
    const startDateTime = DateTime.fromJSDate(date).startOf('day').toJSDate();
    const endDateTime = DateTime.fromJSDate(date).endOf('day').toJSDate();
    const qb = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('count(distributorOrder.id)', 'orderNum')
      .andWhere('distributorOrder.distributorId = :distributorId', {distributorId})
      .leftJoin('distributorOrder.order', 'order')
      .andWhere('order.state != :state', {state: 'Cancelled'})
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime', {startDateTime, endDateTime});
    if (distributorGroupId) {
      qb.andWhere('distributorOrder.distributorGroupId = :distributorGroupId', {distributorGroupId});
    }
    const distributorOrders = await qb.getRawOne<{
      orderNum: number;
    }>();
    const effectiveOrderNum = Number(distributorOrders?.orderNum ?? 0);
    return effectiveOrderNum;
  }

  async getDistributorDetail(ctx: RequestContext, distributorId: ID, date = new Date(), distributorGroupId?: ID) {
    const startDateTime = DateTime.fromJSDate(date).startOf('day').toJSDate();
    const endDateTime = DateTime.fromJSDate(date).endOf('day').toJSDate();
    const effectiveCustomerNum = await this.getEffectiveCustomerNum(
      ctx,
      distributorId,
      startDateTime,
      endDateTime,
      distributorGroupId,
    );
    const customerCount = await this.getCustomerCount(
      ctx,
      distributorId,
      startDateTime,
      endDateTime,
      distributorGroupId,
    );
    return {
      effectiveCustomerNum,
      customerCount,
    };
  }
  // 获取时间范围内客户数
  async getCustomerCount(
    ctx: RequestContext,
    distributorId: ID,
    startDateTime: Date,
    endDateTime: Date,
    distributorGroupId?: ID,
  ) {
    const qb = this.connection
      .getRepository(ctx, DistributorBinding)
      .createQueryBuilder('distributorBinding')
      .select('distributor.id', 'distributorId')
      .addSelect('COUNT(DISTINCT distributorBinding.customerId)', 'distinctCustomerCount')
      .leftJoin('distributorBinding.distributor', 'distributor')
      .andWhere('distributor.id = :distributorId', {distributorId})
      .andWhere('distributorBinding.createdAt BETWEEN :startDateTime AND :endDateTime', {startDateTime, endDateTime});
    if (distributorGroupId) {
      qb.leftJoin('distributorBinding.distributorGroup', 'distributorGroup');
      qb.andWhere('distributorGroup.id = :distributorGroupId', {distributorGroupId});
    }
    const distributorBindings = await qb.getRawOne<{
      distributorId: ID;
      distinctCustomerCount: number;
    }>();
    return Number(distributorBindings?.distinctCustomerCount ?? 0);
  }
  // 获取时间范围内有效客户数
  async getEffectiveCustomerNum(
    ctx: RequestContext,
    distributorId: ID,
    startDateTime: Date,
    endDateTime: Date,
    distributorGroupId?: ID,
  ) {
    const qb = this.connection
      .getRepository(ctx, DistributorBinding)
      .createQueryBuilder('distributorBinding')
      .select('distributor.id', 'distributorId')
      .addSelect('COUNT(DISTINCT customer.id)', 'distinctCustomerCount')
      .leftJoin('distributorBinding.distributor', 'distributor')
      .leftJoin('distributorBinding.customer', 'customer')
      .leftJoin(
        CustomerBindingDistributor,
        'customerBindingDistributor',
        'customerBindingDistributor.customerId = customer.id AND customerBindingDistributor.distributorId = distributor.id AND customerBindingDistributor.channelId = :channelId',
        {channelId: ctx.channelId},
      )
      .andWhere('distributor.id = :distributorId', {distributorId})
      .andWhere('distributorBinding.createdAt BETWEEN :startDateTime AND :endDateTime', {startDateTime, endDateTime});
    if (distributorGroupId) {
      qb.leftJoin('distributorBinding.distributorGroup', 'distributorGroup');
      qb.andWhere('distributorGroup.id = :distributorGroupId', {distributorGroupId});
    }
    const distributorBindings = await qb.getRawOne<{
      distributorId: ID;
      distinctCustomerCount: number;
    }>();
    return Number(distributorBindings?.distinctCustomerCount ?? 0);
  }

  async distributorSharing(ctx: RequestContext, input: DistributorSharingInput) {
    const type = input.type;
    let distributorId = input.distributorId as ID;
    if (!distributorId) {
      if (!ctx.activeUserId) {
        throw new UnauthorizedError();
      }
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId);
      if (!customer) {
        throw new Error('customer not exist');
      }
      if (!distributorId) {
        const distributor = await this.getDistributorByCustomer(ctx, customer);
        distributorId = distributor.id;
      }
    }
    const programInput: ProgramLinkInput = {
      type: input.shareType,
      id: String(input.shareValue ?? ''),
    };
    if (type === SharingType.Link) {
      return this.commonService.generateSmallProgramLink(ctx, programInput, input.path, distributorId);
    } else if (type === SharingType.QrCode) {
      return this.commonService.generateSmallProgramQRCodeLink(ctx, programInput, input.path, distributorId);
    } else if (type === SharingType.H5) {
      return this.commonService.generateH5Link(ctx, programInput, input.path, distributorId);
    } else if (type === SharingType.Scheme) {
      return this.commonService.generateSchemeLink(ctx, programInput, input.path, distributorId);
    }
    return;
  }

  async getDistributorByCustomer(ctx: RequestContext, customer: Customer) {
    const distributors = await this.connection
      .getRepository(ctx, Distributor)
      .createQueryBuilder('distributor')
      .leftJoin('distributor.channels', 'channel')
      .leftJoinAndSelect('distributor.customer', 'customer')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('customer.id = :customerId', {customerId: customer.id})
      .andWhere(`distributor.deletedAt IS NULL`)
      .getMany();
    if (!distributors || distributors.length <= 0) {
      throw new Error(`You're not a distributor`);
    }
    return distributors[0];
  }

  async boundDistributor(ctx: RequestContext, distributorId: ID) {
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      Logger.error(`The distributor does not exist。distributorId:${distributorId}`);
      return `该分销员不存在`;
      // throw new Error('The distributor does not exist');
    }
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    let customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId);
    if (!customer) {
      Logger.error(`The customer does not exist。userId:${ctx?.activeUserId},distributorId:${distributorId}`);
      throw new UnauthorizedError();
    }
    // let distributorBinding = await this.getDistributorBinding(ctx, customer.id);
    const distributorGroup = distributor.distributorGroup;
    // if (distributorBinding) {
    // await this.connection.getRepository(ctx, DistributorBinding).update(distributorBinding.id, {
    //   distributor: {
    //     id: distributorId,
    //   },
    //   distributorGroup: {
    //     id: distributorGroup.id,
    //   },
    // });
    // } else {
    let distributorBinding = new DistributorBinding({
      customer: customer,
      distributor: distributor,
      distributorGroup: {
        id: distributorGroup.id,
      },
    });
    distributorBinding = await this.channelService.assignToCurrentChannel(distributorBinding, ctx);
    await this.connection.getRepository(ctx, DistributorBinding).save(distributorBinding);
    // }
    customer = await this.customerService.findOne(ctx, customer.id);
    if (!customer) {
      throw new Error('用户不存在');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    const oldDistributorId = customerBindingDistributor?.distributorId;
    if (oldDistributorId && idsAreEqual(oldDistributorId, distributorId)) {
      return 'You do not need to bind again';
    }
    await this.updateCustomerDistributor(ctx, customer.id, distributorId);
    await this.cacheService.removeCache(CacheKeyManagerService.customerDistributor(customer.id, ctx.channelId));
    await this.switchoverDistributorUpdateDetail(ctx, oldDistributorId, distributorId);
    return 'Binding success';
  }

  /**
   * 切换分销商
   * @param ctx
   * @param distributorId 分销商id
   * @param customerId 用户id
   * @returns
   */
  async switchoverDistributor(ctx: RequestContext, distributorId: ID, customerId: ID) {
    const distributor = await this.findOne(ctx, distributorId, undefined, ['distributorGroup']);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer) {
      throw new Error('用户不存在');
    }
    const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
    const oldDistributorId = customerBindingDistributor?.distributorId;
    if (oldDistributorId && oldDistributorId === distributorId) {
      throw new Error(`切换分销商不存在更改`);
    }
    const distributorGroup = distributor.distributorGroup;
    let distributorBinding = new DistributorBinding({
      customer: customer,
      distributor: distributor,
      distributorGroup: {
        id: distributorGroup.id,
      },
    });
    distributorBinding = await this.channelService.assignToCurrentChannel(distributorBinding, ctx);
    await this.connection.getRepository(ctx, DistributorBinding).save(distributorBinding);
    await this.updateCustomerDistributor(ctx, customer.id, distributorId);

    await this.cacheService.removeCache(CacheKeyManagerService.customerDistributor(customer.id, ctx.channelId));
    await this.switchoverDistributorUpdateDetail(ctx, oldDistributorId, distributorId);
    return '绑定分销商成功';
  }

  async switchoverDistributorUpdateDetail(ctx: RequestContext, oleDistributorId: ID | undefined, newDistributorId: ID) {
    if (oleDistributorId === newDistributorId) {
      return;
    }
    const newDistributor = await this.findOne(ctx, newDistributorId, undefined, ['distributorGroup']);
    if (newDistributor) {
      const distributorGroupId = newDistributor.distributorGroup?.id;
      const lock = await this.redLockService.lockResource(`Distributor:DistributorDetail:${newDistributorId}`);
      try {
        const newDistributorDetail = await this.getDistributionDetail(
          ctx,
          newDistributorId,
          new Date(),
          distributorGroupId,
        );
        const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
          ctx,
          newDistributor.id,
          new Date(),
          distributorGroupId,
        );
        if (!newDistributorDetail) {
          await this.connection.getRepository(ctx, DistributorDetail).save({
            distributorGroup: {id: distributorGroupId},
            distributor: {id: newDistributor.id},
            detailTime: new Date(),
            effectiveCustomerNum: effectiveCustomerNum,
            effectiveOrderNum: 0,
            customerTotal: customerCount,
            orderAccumulationAmount: 0,
            orderNum: 0,
          });
        } else {
          await this.connection.getRepository(ctx, DistributorDetail).update(newDistributorDetail.id, {
            effectiveCustomerNum: newDistributorDetail.effectiveCustomerNum + 1,
            customerTotal: customerCount,
          });
        }
      } catch (error) {
        Logger.error(`switchoverDistributorUpdateDetail error:${error}`);
        throw error;
      } finally {
        await this.redLockService.unlockResource(lock);
      }
    }
    if (oleDistributorId) {
      const oldDistributor = await this.findOne(ctx, oleDistributorId, undefined, ['distributorGroup']);
      if (oldDistributor) {
        const distributorGroupId = oldDistributor.distributorGroup?.id;
        const lock = await this.redLockService.lockResource(`Distributor:DistributorDetail:${oleDistributorId}`);
        try {
          const oldDistributorDetail = await this.getDistributionDetail(
            ctx,
            oleDistributorId,
            new Date(),
            distributorGroupId,
          );
          if (!oldDistributorDetail) {
            const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
              ctx,
              oldDistributor.id,
              new Date(),
              distributorGroupId,
            );
            await this.connection.getRepository(ctx, DistributorDetail).save({
              distributorGroup: {id: distributorGroupId},
              distributor: {id: oldDistributor.id},
              detailTime: new Date(),
              effectiveCustomerNum: effectiveCustomerNum,
              effectiveOrderNum: 0,
              customerTotal: customerCount,
              orderAccumulationAmount: 0,
              orderNum: 0,
            });
          } else {
            const effectiveCustomer = oldDistributorDetail.effectiveCustomerNum - 1;
            await this.connection.getRepository(ctx, DistributorDetail).update(oldDistributorDetail.id, {
              effectiveCustomerNum: effectiveCustomer > 0 ? effectiveCustomer : 0,
            });
          }
        } catch (error) {
          Logger.error(`switchoverDistributorUpdateDetail error:${error}`);
          throw error;
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
    }

    return;
  }

  async findOne(
    ctx: RequestContext,
    distributorId: ID,
    options?: ListQueryOptions<Distributor>,
    relations?: RelationPaths<Distributor>,
  ) {
    const qb = this.listQueryBuilder.build(Distributor, options, {
      ctx,
      relations: relations ?? ['channels', 'customer'],
      channelId: ctx.channelId,
    });
    qb.where(`${qb.alias}.id=:distributorId`, {distributorId: distributorId});
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const distributors = await qb.getMany();
    if (distributors.length > 0) {
      return distributors[0];
    }
    return null;
  }

  async distributorsByDay(
    ctx: RequestContext,
    name: string,
    phone: string,
    skip: number,
    take: number,
    startDateTime: Date = new Date(0),
    endDateTime: Date = new Date(),
    distributorGroupIds?: ID[],
  ) {
    let sql = `SELECT dd.detailTime, dd.effectiveCustomerNum as effectiveCustomerNum, dd.effectiveOrderNum as effectiveOrderNum, dd.orderAccumulationAmount as accumulationAmount, dd.orderNum as orderTotal, dd.customerTotal as customerCount, distributor.id, distributor.name, distributor.phone, distributor.createdAt,distributor_group.name as distributorGroupName,distributor_group.id as distributorGroupId FROM distributor_detail dd LEFT JOIN distributor_group on dd.distributorGroupId = distributor_group.id LEFT JOIN distributor ON dd.distributorId = distributor.id AND distributor.deletedAt IS NULL LEFT JOIN distributor_channels_channel ON distributor_channels_channel.distributorId = distributor.id LEFT JOIN channel ON distributor_channels_channel.channelId = channel.id WHERE channel.id = ? AND dd.detailTime BETWEEN ? AND ? {{condition}} ORDER BY dd.orderAccumulationAmount DESC {{paging}}`;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any[] = [ctx.channelId, startDateTime, endDateTime];
    let condition = '';
    if (name) {
      condition += ' AND distributor.name = ?';
      params.push(name);
    }
    if (phone) {
      condition += ' AND distributor.phone = ?';
      params.push(phone);
    }
    if (distributorGroupIds && distributorGroupIds.length > 0) {
      condition += ' AND distributor_group.id IN (?)';
      params.push(distributorGroupIds);
    }
    sql = sql.replace('{{condition}}', condition);
    let countSql = `SELECT COUNT(*) AS total FROM (${sql}) AS total`;
    countSql = countSql.replace('{{paging}}', '');
    const countData = await this.connection.rawConnection.query(countSql, params);
    const totalItems = countData[0].total;
    if (skip || take) {
      sql = sql.replace('{{paging}}', `LIMIT ${take ?? 10} OFFSET ${skip ?? 0}`);
    } else {
      sql = sql.replace('{{paging}}', '');
    }
    const datas = await this.connection.rawConnection.query(sql, params);
    return {
      totalItems,
      items: datas,
    };

    // const qb = this.listQueryBuilder.build(Distributor, options, {
    //   ctx,
    //   relations: (relations ?? ['channels']).concat(this.distributorRelations),
    //   channelId: ctx.channelId,
    // });
    // qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    // qb.leftJoinAndSelect(`${qb.alias}.distributorDetails`, 'distributorDetail');
    // qb.addSelect('COALESCE(SUM(distributorDetail.orderAccumulationAmount),0)', 'accumulationAmount');
    // qb.andWhere('distributorDetail.detailTime BETWEEN :startDateTime AND :endDateTime', {
    //   startDateTime,
    //   endDateTime,
    // });
    // qb.orderBy('accumulationAmount', 'DESC');
    // qb.groupBy('distributor.id');
    // const totalItems = await qb.getCount();
    // const items = await qb.getRawMany();
    // return {
    //   items,
    //   totalItems,
    // };
  }

  async distributorsList(
    ctx: RequestContext,
    relations: RelationPaths<Distributor>,
    startDateTime = new Date(0),
    endDateTime = new Date(),
    distributorName?: string,
    distributorPhone?: string,
    distributorGroupIds?: [ID],
    sortName = 'accumulationAmount',
    sortType = 'DESC',
    skip = 0,
    take = 10,
  ) {
    let distribution = await this.getDistribution(
      ctx,
      relations,
      distributorName,
      distributorPhone,
      distributorGroupIds,
    );

    const distributionOrderData = await this.getDistributionOrderData(
      ctx,
      startDateTime,
      endDateTime,
      distributorName,
      distributorPhone,
      distributorGroupIds,
    );
    distribution = this.distributionOrderBinding(distribution, distributionOrderData);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    distribution.sort((a: any, b: any) => {
      if (sortType === 'DESC') {
        return b[sortName] - a[sortName];
      }
      return a[sortName] - b[sortName];
    });
    const totalItems = distribution.length;
    distribution = distribution.slice(skip, skip + take);
    distribution = await this.getCustomerNum(ctx, distribution, startDateTime, endDateTime);
    return {
      totalItems,
      items: distribution,
    };

    // const qb = this.listQueryBuilder.build(DistributorGroupBinding, undefined, {
    //   ctx,
    //   relations: relations ?? ['channels'],
    // });
    // qb.leftJoinAndSelect(`${qb.alias}.channels`, 'channel').andWhere('channel.id = :channelId', {
    //   channelId: ctx.channelId,
    // });
    // qb.leftJoinAndSelect(`${qb.alias}.distributor`, 'distributor');
    // qb.leftJoin(`${qb.alias}.distributorGroup`, 'distributorGroup');
    // if (distributorGroupIds && distributorGroupIds.length > 0) {
    //   qb.andWhere('distributorGroup.id IN (:...distributorGroupIds)', {distributorGroupIds: distributorGroupIds});
    // }
    // if (distributorName) {
    //   qb.andWhere('distributor.name = :distributorName', {distributorName});
    // }
    // if (distributorPhone) {
    //   qb.andWhere('distributor.phone = :distributorPhone', {distributorPhone});
    // }
    // qb.andWhere('distributor.deletedAt IS NULL');

    // qb.leftJoinAndSelect(
    //   `distributor.distributorDetails`,
    //   'distributorDetail',
    //   'distributorDetail.distributorId = distributor.id AND distributorDetail.distributorGroupId = distributorGroup.id AND distributorDetail.detailTime BETWEEN :startDateTime AND :endDateTime',
    //   {
    //     startDateTime,
    //     endDateTime,
    //   },
    // );
    // qb.leftJoin('distributor.distributorGroup', 'distributorPresentGroup');
    // qb.select([
    //   `distributor.id as id`,
    //   `distributor.name as name`,
    //   `distributor.phone as phone`,
    //   `distributor.createdAt as createdAt`,
    //   `distributorGroup.id as distributorGroupId`,
    //   `distributorGroup.name as distributorGroupName`,
    //   `distributorPresentGroup.id as presentGroupId`,
    // ]);
    // qb.addSelect('COALESCE(SUM(distributorDetail.effectiveOrderNum),0)', 'effectiveOrderNum');
    // qb.addSelect('COALESCE(SUM(distributorDetail.orderAccumulationAmount),0)', 'accumulationAmount');
    // qb.addSelect('COALESCE(SUM(distributorDetail.orderNum),0)', 'orderTotal');
    // qb.groupBy(`${qb.alias}.distributorId`);
    // qb.addGroupBy(`${qb.alias}.distributorGroupId`);
    // qb.orderBy(sortName, sortType === 'DESC' ? 'DESC' : 'ASC');

    // const totalItems = await qb.getCount();
    // qb.skip(skip);
    // qb.take(take);
    // let items = await qb.getRawMany();
    // items = await this.getCustomerNum(ctx, items, startDateTime, endDateTime);
    // return {
    //   items,
    //   totalItems,
    // };
  }
  distributionOrderBinding(
    distribution: {
      id: ID;
      name: string;
      phone: string;
      createdAt: Date;
      distributorGroupId: ID;
      distributorGroupName: string;
      presentGroupId: ID;
      effectiveOrderNum?: number;
      accumulationAmount?: number;
      orderTotal?: number;
      orderTotalAmount?: number;
    }[],
    distributionOrderData: {
      distributorId: ID;
      distributorGroupId: ID;
      effectiveOrderNum: number;
      accumulationAmount: number;
      orderTotal: number;
      orderTotalAmount: number;
    }[],
  ) {
    distribution = distribution.map(distributor => {
      const distributorOrder = distributionOrderData.find(
        item => item.distributorId === distributor.id && item.distributorGroupId === distributor.distributorGroupId,
      );
      if (distributorOrder) {
        distributor.effectiveOrderNum = distributorOrder.effectiveOrderNum;
        distributor.accumulationAmount = distributorOrder.accumulationAmount;
        distributor.orderTotal = distributorOrder.orderTotal;
        distributor.orderTotalAmount = distributorOrder.orderTotalAmount;
      } else {
        distributor.effectiveOrderNum = 0;
        distributor.accumulationAmount = 0;
        distributor.orderTotal = 0;
        distributor.orderTotalAmount = 0;
      }
      return distributor;
    });
    return distribution;
  }
  async getDistributionOrderData(
    ctx: RequestContext,
    startDateTime: Date,
    endDateTime: Date,
    distributorName?: string,
    distributorPhone?: string,
    distributorGroupIds?: [ID],
  ) {
    const qb = this.connection
      .getRepository(ctx, DistributorDetail)
      .createQueryBuilder('distributorDetail')
      .select('distributorDetail.distributorId')
      .addSelect('distributorDetail.distributorGroupId')
      .addSelect('SUM(distributorDetail.effectiveOrderNum)', 'effectiveOrderNum')
      .addSelect('SUM(distributorDetail.orderTotalAmount)', 'orderTotalAmount')
      .addSelect('SUM(distributorDetail.orderAccumulationAmount)', 'accumulationAmount')
      .addSelect('SUM(distributorDetail.orderNum)', 'orderTotal')
      .leftJoin('distributorDetail.distributor', 'distributor')
      .where('distributorDetail.distributorGroupId is not NULL')
      .groupBy('distributorId')
      .addGroupBy('distributorDetail.distributorGroupId');
    if (distributorGroupIds && distributorGroupIds.length > 0) {
      qb.andWhere('distributorDetail.distributorGroupId IN (:...distributorGroupIds)', {distributorGroupIds});
    }
    if (distributorName) {
      qb.andWhere('distributor.name = :distributorName', {distributorName});
    }
    if (distributorPhone) {
      qb.andWhere('distributor.phone = :distributorPhone', {distributorPhone});
    }
    qb.andWhere('distributorDetail.detailTime BETWEEN :startDateTime AND :endDateTime', {
      startDateTime,
      endDateTime,
    });
    const distributorOrderData = await qb.getRawMany<{
      distributorId: ID;
      distributorGroupId: ID;
      effectiveOrderNum: number;
      accumulationAmount: number;
      orderTotal: number;
      orderTotalAmount: number;
    }>();
    return distributorOrderData;
  }
  async getDistribution(
    ctx: RequestContext,
    relations: RelationPaths<Distributor>,
    distributorName: string | undefined,
    distributorPhone: string | undefined,
    distributorGroupIds: [ID] | undefined,
  ) {
    const qb = this.listQueryBuilder.build(DistributorGroupBinding, undefined, {
      ctx,
      relations: relations ?? ['channels'],
    });
    qb.leftJoinAndSelect(`${qb.alias}.channels`, 'channel').andWhere('channel.id = :channelId', {
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.distributor`, 'distributor');
    qb.leftJoin(`${qb.alias}.distributorGroup`, 'distributorGroup');
    if (distributorGroupIds && distributorGroupIds.length > 0) {
      qb.andWhere('distributorGroup.id IN (:...distributorGroupIds)', {distributorGroupIds: distributorGroupIds});
    }
    if (distributorName) {
      qb.andWhere('distributor.name = :distributorName', {distributorName});
    }
    if (distributorPhone) {
      qb.andWhere('distributor.phone = :distributorPhone', {distributorPhone});
    }
    qb.andWhere('distributor.deletedAt IS NULL');
    qb.leftJoin('distributor.distributorGroup', 'distributorPresentGroup');
    qb.select([
      `distributor.id as id`,
      `distributor.name as name`,
      `distributor.phone as phone`,
      `distributor.createdAt as createdAt`,
      `distributorGroup.id as distributorGroupId`,
      `distributorGroup.name as distributorGroupName`,
      `distributorPresentGroup.id as presentGroupId`,
    ]);
    qb.groupBy(`distributor.id`);
    qb.addGroupBy(`distributorGroup.id`);
    return qb.getRawMany<{
      id: ID;
      name: string;
      phone: string;
      createdAt: Date;
      distributorGroupId: ID;
      distributorGroupName: string;
      presentGroupId: ID;
    }>();
  }

  async getDistributorEffectiveCustomerNum(
    ctx: RequestContext,
    startDateTime: Date,
    endDateTime: Date,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    distributors: any[],
  ) {
    const distributorIds = distributors?.map(distributor => distributor.id);
    const qb = this.connection
      .getRepository(ctx, Distributor)
      .createQueryBuilder('distributor')
      .select('distributor.id', 'distributorId')
      .addSelect('COUNT(DISTINCT customer.id)', 'distinctCustomerCount')
      .leftJoin('distributor.distributorBindings', 'distributorBinding')
      .leftJoin('distributorBinding.customer', 'customer')
      .leftJoin(
        CustomerBindingDistributor,
        'customerBindingDistributor',
        'customerBindingDistributor.customerId = customer.id AND customerBindingDistributor.distributorId = distributor.id AND customerBindingDistributor.channelId = :channelId',
        {channelId: ctx.channelId},
      )
      .andWhere('distributor.deletedAt IS NULL')
      .andWhere('distributorBinding.createdAt BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      });
    if (distributorIds && distributorIds.length > 0) {
      qb.andWhere('distributor.id IN (:...distributorIds)', {
        distributorIds,
      });
    }
    const effectiveCustomerData = await qb.groupBy('distributor.id').getRawMany<{
      distributorId: ID;
      distinctCustomerCount: number;
    }>();
    return effectiveCustomerData;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async getDistributionTotalCustomer(ctx: RequestContext, startDateTime: Date, endDateTime: Date, distributors: any[]) {
    const distributorIds = distributors?.map(distributor => distributor.id);
    const qb = this.connection
      .getRepository(ctx, Distributor)
      .createQueryBuilder('distributor')
      .select('distributor.id', 'distributorId')
      .addSelect('COUNT(DISTINCT customer.id)', 'distinctCustomerCount')
      .leftJoin('distributor.distributorBindings', 'distributorBinding')
      .leftJoin('distributorBinding.customer', 'customer')
      .andWhere('distributorBinding.createdAt BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      })
      .andWhere('distributor.deletedAt IS NULL');
    if (distributorIds && distributorIds.length > 0) {
      qb.andWhere('distributor.id IN (:...distributorIds)', {
        distributorIds,
      });
    }
    const totalCustomerData = await qb.groupBy('distributor.id').getRawMany<{
      distributorId: ID;
      distinctCustomerCount: number;
    }>();
    return totalCustomerData;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async getCustomerNum(ctx: RequestContext, distributors: any[], startDateTime: Date, endDateTime: Date) {
    const effectiveCustomerData = await this.getDistributorEffectiveCustomerNum(
      ctx,
      startDateTime,
      endDateTime,
      distributors,
    );
    const totalCustomerData = await this.getDistributionTotalCustomer(ctx, startDateTime, endDateTime, distributors);
    for (const distributor of distributors) {
      const effectiveCustomer = effectiveCustomerData.find(effectiveCustomerObj =>
        idsAreEqual(effectiveCustomerObj.distributorId, distributor.id),
      );
      const totalCustomer = totalCustomerData.find(totalCustomerObj =>
        idsAreEqual(totalCustomerObj.distributorId, distributor.id),
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (distributor as any)['effectiveCustomerNum'] = effectiveCustomer?.distinctCustomerCount ?? 0;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (distributor as any)['customerCount'] = totalCustomer?.distinctCustomerCount ?? 0;
    }
    return distributors;
  }

  async findAll(ctx: RequestContext, options: ListQueryOptions<Distributor>, relations: RelationPaths<Distributor>) {
    const qb = this.listQueryBuilder.build(Distributor, options, {
      ctx,
      relations: (relations ?? ['channels']).concat(this.distributorRelations),
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async upsertDistributor(ctx: RequestContext, input: DistributorInput) {
    const {id, phone, name} = input;
    if (!name) {
      throw new Error('分销名称不能为空');
    }
    const distributionGroup = await this.connection.getRepository(ctx, DistributorGroup).findOne({
      where: {id: input.distributorGroupId},
    });
    if (!distributionGroup) {
      throw new Error('分销组不存在');
    }
    const qb = this.listQueryBuilder.build(Customer, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.phoneNumber = :phoneNumber`, {phoneNumber: phone});
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const customer = await qb.take(1).getOne();
    if (!customer) {
      throw new Error('该手机号未注册');
    }
    if (!id) {
      await this.verifyToBeADistributor(ctx, phone);
      let distributor = new Distributor({
        name: input.name,
        phone: phone,
        customer: customer,
        distributorGroup: distributionGroup,
      });
      distributor = await this.channelService.assignToCurrentChannel(distributor, ctx);
      distributor = await this.connection.getRepository(ctx, Distributor).save(distributor);
      await this.distributorGroupService.addDistributorGroupBinding(ctx, distributor.id, distributionGroup.id);
      return distributor;
    } else {
      const distributor = await this.findOne(ctx, id, undefined, ['distributorGroup']);
      if (!distributor) {
        throw new Error(`修改的分销信息不存在`);
      }
      distributor.name = name;
      if (distributionGroup.id !== distributor.distributorGroup?.id) {
        distributor.distributorGroup = distributionGroup;
        await this.distributorGroupService.addDistributorGroupBinding(ctx, distributor.id, distributionGroup.id);
      }
      return this.connection.getRepository(ctx, Distributor).save(distributor);
    }
  }
  async verifyToBeADistributor(ctx: RequestContext, phone: string) {
    const qb = this.listQueryBuilder.build(Distributor, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.phone = :phone`, {phone});
    const count = await qb.getCount();
    if (count > 0) {
      throw new Error('The phone number is already a distributor');
    }
    return true;
  }

  async timeoutDistributor() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const currentDate = DateTime.now();
    const fifteenDaysAgo = currentDate.minus({days: Number(process.env.DISTRIBUTION_BINDING_TIME_DAY || 15)});
    const formattedDate = fifteenDaysAgo.toJSDate();
    const latestDistributorBindings = await this.getLatestDistributorBindingsByCustomer(ctx, formattedDate);
    for (const latestDistributorBinding of latestDistributorBindings) {
      const distributor = latestDistributorBinding.distributor;
      const customer = latestDistributorBinding.customer;
      const customerBindingDistributor = await this.findCustomerDistributor(ctx, customer.id);
      if (customerBindingDistributor && idsAreEqual(customerBindingDistributor?.distributorId, distributor.id)) {
        await this.connection.getRepository(ctx, CustomerBindingDistributor).update(customerBindingDistributor.id, {
          distributor: null,
        });
      }
    }
  }

  async asyncDistributorDetail() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const currentDate = DateTime.now();
    const yesterday = currentDate.minus({days: 1});
    const formattedDate = yesterday.toJSDate();
    const distributors = await this.connection
      .getRepository(adminCtx, Distributor)
      .createQueryBuilder('distributor')
      .leftJoin('distributor.distributorGroup', 'distributorGroup')
      .leftJoinAndSelect('distributor.customer', 'customer')
      .leftJoinAndSelect('distributor.channels', 'channels')
      .andWhere(`distributor.deletedAt IS NULL`)
      .getMany();
    for (const distributor of distributors) {
      const channels = distributor.channels;
      const customer = distributor.customer;
      const distributorGroupId = distributor.distributorGroup?.id;
      const ctx = await this.commonService.getCtxByCustomerAndChannels(channels, customer);
      if (!ctx) {
        continue;
      }
      const distributorDetail = await this.getDistributionDetail(
        ctx,
        distributor.id,
        formattedDate,
        distributorGroupId,
      );
      if (!distributorDetail) {
        const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
          ctx,
          distributor.id,
          formattedDate,
          distributorGroupId,
        );
        if (!effectiveCustomerNum && !customerCount) {
          continue;
        }
        await this.connection.getRepository(ctx, DistributorDetail).save({
          distributor: {id: distributor.id},
          detailTime: formattedDate,
          effectiveCustomerNum: effectiveCustomerNum,
          effectiveOrderNum: 0,
          customerTotal: customerCount,
          orderAccumulationAmount: 0,
          orderNum: 0,
          distributorGroup: {id: distributorGroupId},
        });
      } else {
        const {effectiveCustomerNum, customerCount} = await this.getDistributorDetail(
          ctx,
          distributor.id,
          formattedDate,
          distributorGroupId,
        );
        await this.connection.getRepository(ctx, DistributorDetail).update(distributorDetail.id, {
          effectiveCustomerNum: effectiveCustomerNum,
          customerTotal: customerCount,
        });
      }
    }
  }

  async getLatestDistributorBindingsByCustomer(
    ctx: RequestContext,
    expiredBindingTime: Date,
  ): Promise<DistributorBinding[]> {
    const queryBuilder = this.connection
      .getRepository(ctx, DistributorBinding)
      .createQueryBuilder('distributorBinding');
    // 按照customer分组
    queryBuilder.groupBy('distributorBinding.customerId');
    // 查询用户最新绑定的分销员记录
    queryBuilder.select('MAX(distributorBinding.id)', 'latestId');
    queryBuilder.addSelect('MAX(distributorBinding.createdAt)', 'maxCreatedAt');
    queryBuilder.leftJoin('distributorBinding.customer', 'customer');
    queryBuilder.leftJoin('distributorBinding.distributor', 'distributor');
    queryBuilder.leftJoin('distributorBinding.channels', 'channels');
    // 绑定的时间在指定时间之前
    queryBuilder.andHaving('maxCreatedAt <= :expiredBindingTime', {expiredBindingTime});
    // 获取最新的分销商绑定记录
    const results = await queryBuilder.getRawMany();
    const ids = results.map(result => result.latestId) as ID[];
    // 使用查询到的id获取实际的绑定数据
    const distributorBindings = await this.connection.getRepository(ctx, DistributorBinding).find({
      where: {id: In(ids)},
      relations: ['customer', 'distributor', 'channels'],
    });
    return distributorBindings;
  }

  /**
   * 根据customerId删除分销商
   * @param ctx
   * @param customerId 用户id
   * @returns
   */
  @Transaction()
  async deleteDistributor(ctx: RequestContext, customerId: ID) {
    const qb = this.listQueryBuilder.build(Distributor, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.leftJoin(`${qb.alias}.customer`, 'customer');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`customer.id = :customerId`, {customerId});
    const distributor = await qb.take(1).getOne();
    if (!distributor) {
      return;
    }
    return this.softDeleteDistributor(ctx, distributor.id);
  }

  async distributorPerformance(ctx: RequestContext, startDateTime = new Date(0), endDateTime = new Date()) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const distributorOrderData = await this.getDistributionCenterData(ctx, distributor, startDateTime, endDateTime);
    return distributorOrderData;
  }

  async distributorCenter(ctx: RequestContext, startDateTime = new Date(0), endDateTime = new Date()) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    // 获取今日开始时间
    const toDayStart = DateTime.now().startOf('day').toJSDate();
    // 获取今日结束时间
    const toDayEnd = DateTime.now().endOf('day').toJSDate();
    const distributorOrderDataToday = await this.getDistributionCenterData(ctx, distributor, toDayStart, toDayEnd);
    const distributorOrderDataTotal = await this.getDistributionCenterData(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
    );
    return {
      todayDistributor: distributorOrderDataToday,
      totalDistributor: distributorOrderDataTotal,
      distributor: distributor,
    };
  }

  async getDistributionCenterData(
    ctx: RequestContext,
    distributor: Distributor,
    startDateTime = new Date(0),
    endDateTime = new Date(),
  ) {
    // 获取当天开始时间
    startDateTime = DateTime.fromJSDate(startDateTime).startOf('day').toJSDate();
    // 获取当天结束时间
    endDateTime = DateTime.fromJSDate(endDateTime).endOf('day').toJSDate();
    let distributorOrderDatas = await this.getDistributionOrderData(
      ctx,
      startDateTime,
      endDateTime,
      '',
      distributor.phone,
    );
    distributorOrderDatas = distributorOrderDatas.filter(item => idsAreEqual(item.distributorId, distributor.id));
    const customerNum = await this.getCustomerNum(ctx, [distributor], startDateTime, endDateTime);
    const memberCustomerTotalCount = await this.getDistributionMemberCustomerNum(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
    );
    const memberCustomerEffectiveCount = await this.getDistributionMemberCustomerNum(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
      true,
    );
    // 计算分销商在所有分组的数据
    const distributorOrderTotal =
      distributorOrderDatas.length > 0
        ? distributorOrderDatas.reduce((prev, current) => {
            return {
              // 有效订单数
              effectiveOrderNum: Number(prev.effectiveOrderNum) + Number(current.effectiveOrderNum),
              // 有效订单金额
              accumulationAmount: Number(prev.accumulationAmount) + Number(current.accumulationAmount),
              // 订单总金额
              orderTotalAmount: Number(prev.orderTotalAmount) + Number(current.orderTotalAmount),
              // 订单总数
              orderTotal: Number(prev.orderTotal) + Number(current.orderTotal),
              // 分销员ID
              distributorId: distributor.id,
              // 分销员分组ID
              distributorGroupId: distributor.distributorGroup?.id,
            };
          })
        : {
            effectiveOrderNum: 0,
            accumulationAmount: 0,
            orderTotalAmount: 0,
            orderTotal: 0,
          };
    return {
      effectiveOrderNum: Number(distributorOrderTotal?.effectiveOrderNum ?? 0),
      accumulationAmount: Number(distributorOrderTotal?.accumulationAmount ?? 0),
      orderTotalAmount: Number(distributorOrderTotal?.orderTotalAmount ?? 0),
      orderTotal: Number(distributorOrderTotal?.orderTotal ?? 0),
      distributorId: distributor.id,
      distributorGroupId: distributor.distributorGroup?.id,
      effectiveCustomerNum: Number(customerNum[0]?.effectiveCustomerNum ?? 0),
      customerCount: Number(customerNum[0]?.customerCount ?? 0),
      memberCustomerTotalCount: memberCustomerTotalCount,
      memberCustomerEffectiveCount: memberCustomerEffectiveCount,
    };
  }
  async getDistributionMemberCustomerNum(
    ctx: RequestContext,
    distributor: Distributor,
    startDateTime = new Date(0),
    endDateTime = new Date(),
    isEffective = false,
  ) {
    const qb = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('COUNT(DISTINCT customer.id)', 'totalMemberCustomer')
      .leftJoin('distributorOrder.customer', 'customer')
      .leftJoin('distributorOrder.membershipOrder', 'membershipOrder')
      .andWhere('membershipOrder.customerId = distributorOrder.customerId')
      .andWhere('distributorOrder.distributorId = :distributorId', {distributorId: distributor.id})
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      });
    if (isEffective) {
      qb.leftJoin('membershipOrder.member', 'member').andWhere('member.state in (:...state)', {
        state: [MemberState.Unactivated, MemberState.Normal],
      });
    }
    const totalMemberCustomer = await qb.getRawOne<{
      totalMemberCustomer: number;
    }>();
    return Number(totalMemberCustomer?.totalMemberCustomer ?? 0);
  }

  async distributorBindingMembers(
    ctx: RequestContext,
    search: string,
    startDateTime = new Date(0),
    endDateTime = new Date(),
    state: MemberStateInput,
    skip = 0,
    take = 10,
  ) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const memberCustomer = await this.getDistributionMemberCustomer(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
      state,
      search,
      skip,
      take,
    );
    return memberCustomer;
  }

  async getDistributionMemberCustomer(
    ctx: RequestContext,
    distributor: Distributor,
    startDateTime = new Date(0),
    endDateTime = new Date(),
    state: MemberStateInput,
    search: string,
    skip = 0,
    take = 10,
  ) {
    // 根据分销订单关联的member进行分组  查询分销员的会员信息和续费次数已经会员卡信息
    const qb = this.connection
      .getRepository(ctx, Member)
      .createQueryBuilder('member')
      .leftJoin('member.membershipOrders', 'membershipOrder')
      .leftJoin('member.customer', 'customer')
      .leftJoin('member.membershipPlan', 'membershipPlan')
      .select('member.id', 'memberId')
      // .addSelect('COUNT(DISTINCT membershipOrder.id)', 'renewalCount')
      .addSelect(
        subQuery =>
          subQuery.select('COUNT(DISTINCT mo.id)').from(MembershipOrder, 'mo').where('mo.memberId = member.id'),
        'renewalCount',
      )
      .addSelect('Max(membershipOrder.payTime)', 'renewalTime')
      .addSelect('membershipPlan.name', 'membershipPlanName')
      .addSelect('customer.lastName', 'customerName')
      .addSelect('customer.phoneNumber', 'customerPhone')
      .addSelect('customer.customFieldsHeadportrait', 'customerHeadPortrait')
      .addSelect('customer.customFieldsIsmodified', 'isModified')
      .addSelect('member.maturityAt', 'maturityAt')
      .addSelect('member.state', 'state')
      .addSelect('member.maturityType', 'maturityType')
      .leftJoin(DistributorOrder, 'distributorOrder', 'distributorOrder.membershipOrderId = membershipOrder.id')
      .andWhere('distributorOrder.distributorId = :distributorId', {distributorId: distributor.id})
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      })
      .groupBy('member.id')
      .orderBy('renewalTime', 'DESC');
    if (state && state !== MemberStateInput.All) {
      qb.andWhere('member.state = :state', {state: state});
    }
    if (search) {
      qb.andWhere(
        new Brackets(qbSql => {
          qbSql
            .orWhere('customer.phoneNumber = :phoneNumber', {phoneNumber: search})
            .orWhere('customer.lastName LIKE :search', {search: `%${search}%`});
        }),
      );
    }
    const totalItems = await qb.getCount();
    qb.offset(skip).limit(take);
    const memberCustomer = await qb.getRawMany<{
      memberId: ID;
      renewalCount: number;
      membershipPlanName: string;
      customerName: string;
      customerPhone: string;
      customerHeadPortrait: string;
      maturityAt: Date;
      state: MemberState;
      maturityType: ValidityPeriodType;
      isModified: boolean;
      renewalTime: Date;
    }>();
    memberCustomer.forEach(item => {
      item.renewalCount = Number(item.renewalCount) - 1;
    });
    return {
      totalItems,
      items: memberCustomer,
    };
  }

  async distributorsPromoteOrders(
    ctx: RequestContext,
    customerId: ID,
    search: string,
    startDateTime: Date,
    endDateTime: Date,
    options: ListQueryOptions<DistributorOrder>,
    relations: RelationPaths<DistributorOrder>,
  ) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const qb = this.listQueryBuilder.build(DistributorOrder, options, {
      ctx,
      relations: relations,
    });
    qb.leftJoinAndSelect(`${qb.alias}.distributor`, 'distributor')
      .leftJoinAndSelect(`${qb.alias}.order`, 'order')
      .leftJoinAndSelect('order.lines', 'lines')
      .leftJoinAndSelect('lines.productVariant', 'productVariant')
      .leftJoinAndSelect('productVariant.product', 'product')
      .leftJoinAndSelect('product.translations', 'translations')
      .leftJoinAndSelect(`${qb.alias}.customer`, 'customer');
    qb.andWhere('order.id is not null');
    qb.andWhere(`${qb.alias}.distributorId = :distributorId`, {distributorId: distributor.id});
    qb.andWhere(`${qb.alias}.createdAt BETWEEN :startDateTime AND :endDateTime`, {
      startDateTime,
      endDateTime,
    });
    if (search) {
      qb.andWhere(
        new Brackets(qbSql => {
          qbSql
            .orWhere('order.code =:orderCode', {orderCode: search})
            .orWhere('customer.phoneNumber =:phoneNumber', {phoneNumber: search})
            .orWhere(`translations.name like :productName`, {productName: `%${search}%`});
        }),
      );
    }
    if (customerId) {
      qb.andWhere(`customer.id = :customerId`, {customerId});
    }
    qb.orderBy(`${qb.alias}.createdAt`, 'DESC');
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async distributorBindingCustomerDetail(ctx: RequestContext, customerId: ID) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const distributorBinding = await this.connection.getRepository(ctx, DistributorBinding).findOne({
      where: {
        distributor: {
          id: distributor.id,
        },
        customer: {
          id: customerId,
        },
      },
      relations: ['customer'],
    });
    if (!distributorBinding) {
      throw new Error('分销员未绑定该用户');
    }
    const distributorBindings = await this.getCustomerStatistics(ctx, [distributorBinding], distributor);
    return distributorBindings[0];
  }

  async distributorBindingCustomers(
    ctx: RequestContext,
    customerType: CustomerType,
    search: string,
    startDateTime: Date,
    endDateTime: Date,
    options: ListQueryOptions<DistributorBinding>,
    relations: RelationPaths<DistributorBinding>,
  ) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const subQuery = this.connection
      .getRepository(ctx, DistributorBinding)
      .createQueryBuilder('binding')
      .select('MIN(binding.id)', 'maxId')
      .leftJoin('binding.distributor', 'distributor')
      .andWhere('distributor.id = :distributorId')
      .groupBy('binding.customerId')
      .getQuery();

    const qb = this.listQueryBuilder.build(DistributorBinding, options, {
      ctx,
      relations: relations,
    });
    qb.leftJoinAndSelect(`${qb.alias}.customer`, 'customer');
    qb.leftJoinAndSelect(`${qb.alias}.distributor`, 'distributor');
    qb.andWhere('customer.deletedAt IS NULL');
    qb.andWhere(`${qb.alias}.id IN (${subQuery})`);
    qb.setParameter('distributorId', distributor.id);
    if (customerType !== CustomerType.OrderCustomer) {
      qb.andWhere(`${qb.alias}.createdAt BETWEEN :startDateTime AND :endDateTime`, {
        startDateTime,
        endDateTime,
      });
    }
    if (customerType === CustomerType.EffectiveCustomer) {
      qb.leftJoin(
        CustomerBindingDistributor,
        'customerBindingDistributor',
        'customerBindingDistributor.customerId = customer.id AND customerBindingDistributor.distributorId = distributor.id AND customerBindingDistributor.channelId = :channelId',
        {channelId: ctx.channelId},
      );
    } else if (customerType === CustomerType.InvalidCustomer) {
      qb.leftJoin(
        CustomerBindingDistributor,
        'customerBindingDistributor',
        'customerBindingDistributor.customerId = customer.id AND customerBindingDistributor.distributorId != distributor.id AND customerBindingDistributor.channelId = :channelId',
        {channelId: ctx.channelId},
      );
    } else if (customerType === CustomerType.OrderCustomer) {
      const orderCustomerIds = this.connection
        .getRepository(ctx, DistributorOrder)
        .createQueryBuilder('distributorOrder')
        .select('customer.id')
        .leftJoin('distributorOrder.customer', 'customer')
        .leftJoin('distributorOrder.distributor', 'distributor')
        .leftJoin('distributorOrder.order', 'order')
        .andWhere('order.id is not null')
        .andWhere('distributor.id = :distributorId')
        .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime')
        .groupBy('customer.id')
        .getQuery();
      qb.setParameter('startDateTime', startDateTime);
      qb.setParameter('endDateTime', endDateTime);
      qb.andWhere(`customer.id IN (${orderCustomerIds})`);
    }
    if (search) {
      qb.andWhere(
        new Brackets(qbSql => {
          qbSql.orWhere('customer.phoneNumber = :phoneNumber', {phoneNumber: search});
          qbSql.orWhere('customer.lastName like :lastName', {lastName: `%${search}%`});
        }),
      );
    }
    qb.orderBy(`${qb.alias}.createdAt`, 'DESC');
    const [items, totalItems] = await qb.getManyAndCount();
    const newItems = await this.getCustomerStatistics(ctx, items, distributor);
    return {
      items: newItems,
      totalItems,
    };
  }

  // 查询用户在该分销员下的订单数  无需扣除退款订单
  async getCustomerOrderTotalByDistributorId(ctx: RequestContext, distributorId: ID, customerIds: ID[]) {
    const customerStatistics = await this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('distributorOrder.customerId', 'customerId')
      // .addSelect('MAX(order.orderPlacedAt)', 'lastOrderTime')
      .addSelect('MAX(distributorOrder.createdAt)', 'lastOrderTime')
      .addSelect('COUNT(distributorOrder.id)', 'orderTotal')
      .addSelect('SUM(distributorOrder.price)', 'orderTotalAmount')
      .leftJoin('distributorOrder.customer', 'customer')
      // 不单单查询订单，还要查询会员卡和礼品卡订单
      // .leftJoin('distributorOrder.order', 'order')
      // .andWhere('order.id is not null')
      .andWhere('distributorOrder.customerId IN (:...customerIds)', {customerIds})
      .andWhere('distributorOrder.distributorId = :distributorId', {distributorId: distributorId})
      .groupBy('distributorOrder.customerId')
      .getRawMany<{
        customerId: ID;
        orderTotal: number;
        orderTotalAmount: number;
        lastOrderTime: Date | null;
      }>();
    const customerStatisticsMap =
      customerStatistics.length > 0
        ? customerStatistics.reduce((prev, current) => {
            prev[current.customerId] = current;
            return prev;
          }, {} as {[key: string]: {customerId: ID; orderTotal: number; orderTotalAmount: number; lastOrderTime: Date | null}})
        : {};
    return customerStatisticsMap;
  }
  // 查询用户在该分销员下的礼品卡退款金额
  async getGiftCardRefundAmountByDistributorId(ctx: RequestContext, distributorId: ID, customerIds: ID[]) {
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('giftCardOrder.id', 'giftCardOrderId')
      .leftJoin('distributorOrder.giftCardOrder', 'giftCardOrder')
      .leftJoin('distributorOrder.customer', 'customer')
      .andWhere('distributorOrder.distributorId = :distributorId')
      .andWhere('giftCardOrder.id is not null')
      .andWhere('customer.id IN (:...customerIds)')
      .getQuery();
    const giftCardRefundAmounts = await this.connection
      .getRepository(ctx, GiftCardReturn)
      .createQueryBuilder('giftCardReturn')
      .leftJoin('giftCardReturn.giftCardOrder', 'giftCardOrder')
      .leftJoin('giftCardOrder.customer', 'customer')
      .select('SUM(giftCardReturn.price)', 'refundAmount')
      .addSelect('customer.id', 'customerId')
      .andWhere('giftCardOrder.id IN (' + subQuery + ')')
      .setParameters({customerIds, distributorId: distributorId})
      .groupBy('customer.id')
      .getRawMany<{
        customerId: ID;
        refundAmount: number;
      }>();
    const giftCardRefundAmountMap =
      giftCardRefundAmounts.length > 0
        ? giftCardRefundAmounts.reduce((prev, current) => {
            prev[current.customerId] = current;
            return prev;
          }, {} as {[key: string]: {customerId: ID; refundAmount: number}})
        : {};
    return giftCardRefundAmountMap;
  }

  // 查询用户在该分销员下的会员卡退款金额
  async getMemberRefundAmountByDistributorId(ctx: RequestContext, distributorId: ID, customerIds: ID[]) {
    // 第一步：创建子查询 subQuery
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('membershipOrder.id', 'memberOrderId')
      .leftJoin('distributorOrder.membershipOrder', 'membershipOrder')
      .leftJoin('distributorOrder.customer', 'customer')
      .andWhere('distributorOrder.distributorId = :distributorId')
      .andWhere('membershipOrder.id is not null')
      .andWhere('customer.id IN (:...customerIds)')
      .getQuery();

    // 第二步：使用 subQuery 创建 memberReturnSubQuery
    const memberReturnSubQuery = this.connection
      .getRepository(ctx, MemberReturnCard)
      .createQueryBuilder('memberReturnCard')
      .leftJoin('memberReturnCard.channels', 'channel')
      .andWhere('channel.id = :channelId')
      .leftJoin('memberReturnCard.membershipOrders', 'membershipOrders')
      .andWhere('membershipOrders.id IN (' + subQuery + ')')
      .select('DISTINCT memberReturnCard.id', 'id')
      .getQuery();

    // 第三步：使用 memberReturnSubQuery 查询总退款金额，并按客户分组
    const memberCardRefundAmounts = await this.connection
      .getRepository(ctx, MemberReturnCard)
      .createQueryBuilder('memberReturnCard')
      .leftJoin('memberReturnCard.member', 'member')
      .leftJoin('member.customer', 'customer')
      .select('SUM(memberReturnCard.price)', 'refundAmount')
      .addSelect('customer.id', 'customerId')
      .setParameters({customerIds, distributorId: distributorId, channelId: ctx.channelId})
      .andWhere('memberReturnCard.id IN (' + memberReturnSubQuery + ')')
      .groupBy('customer.id')
      .getRawMany<{
        customerId: ID;
        refundAmount: number;
      }>();

    const memberCardRefundAmountMap =
      memberCardRefundAmounts.length > 0
        ? memberCardRefundAmounts.reduce((prev, current) => {
            prev[current.customerId] = current;
            return prev;
          }, {} as {[key: string]: {customerId: ID; refundAmount: number}})
        : {};
    return memberCardRefundAmountMap;
  }

  // 查询用户在该分销员下的订单主动退款金额
  async getMerchantVoluntaryRefundAmountByDistributorId(ctx: RequestContext, distributorId: ID, customerIds: ID[]) {
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('order.id', 'orderId')
      .leftJoin('distributorOrder.order', 'order')
      .leftJoin('distributorOrder.distributor', 'distributor')
      .andWhere('distributor.id = :distributorId')
      .andWhere('order.id is not null')
      .leftJoin('distributorOrder.customer', 'customer')
      .andWhere('customer.id IN (:...customerIds)')
      .getQuery();

    // 查询用户在该分销员下的订单主动退款金额
    const merchantVoluntaryRefundAmounts = await this.connection
      .getRepository(ctx, MerchantVoluntaryRefund)
      .createQueryBuilder('merchantVoluntaryRefund')
      .select('SUM(merchantVoluntaryRefund.price)', 'refundAmount')
      .addSelect('customer.id', 'customerId')
      .leftJoin('merchantVoluntaryRefund.order', 'order')
      .leftJoin('order.customer', 'customer')
      .andWhere('order.id IN (' + subQuery + ')')
      .setParameters({customerIds, distributorId: distributorId})
      .groupBy('customer.id')
      .getRawMany<{
        customerId: ID;
        refundAmount: number;
      }>();
    const merchantVoluntaryRefundAmountMap =
      merchantVoluntaryRefundAmounts.length > 0
        ? merchantVoluntaryRefundAmounts.reduce((prev, current) => {
            prev[current.customerId] = current;
            return prev;
          }, {} as {[key: string]: {customerId: ID; refundAmount: number}})
        : {};
    return merchantVoluntaryRefundAmountMap;
  }

  async getCustomerStatistics(ctx: RequestContext, items: DistributorBinding[], distributor: Distributor) {
    const customerIds = items.map(item => item.customer.id);
    if (customerIds.length === 0) {
      return items;
    }
    // 查询用户在该分销员下的订单数  无需扣除退款订单
    const customerStatisticsMap = await this.getCustomerOrderTotalByDistributorId(ctx, distributor.id, customerIds);
    // 查询用户在该分销员下的订单主动退款金额
    const merchantVoluntaryRefundAmountMap = await this.getMerchantVoluntaryRefundAmountByDistributorId(
      ctx,
      distributor.id,
      customerIds,
    );
    // 查询用户在该分销员下的售后退款金额
    const afterSaleAmounts = await this.interfaceAfterSale.getRefundAmountByDistributorId(
      ctx,
      distributor.id,
      customerIds,
    );
    // 查询用户在该分销员下的会员卡退款金额
    const memberCardRefundAmounts = await this.getMemberRefundAmountByDistributorId(ctx, distributor.id, customerIds);
    // 查询用户在该分销员下的礼品卡退款金额
    const giftCardRefundAmounts = await this.getGiftCardRefundAmountByDistributorId(ctx, distributor.id, customerIds);

    // 查询用户在该分销员下的备注
    const distributorCustomerRemarks = await this.connection.getRepository(ctx, DistributorCustomer).find({
      where: {
        customer: {
          id: In(customerIds),
        },
        distributor: {
          id: distributor.id,
        },
      },
      relations: ['customer'],
    });
    const distributorCustomerRemarksMap =
      distributorCustomerRemarks.length > 0
        ? distributorCustomerRemarks.reduce((prev, current) => {
            prev[current.customer.id] = current;
            return prev;
          }, {} as {[key: string]: DistributorCustomer})
        : {};

    return items.map(item => {
      const customerStatistic = customerStatisticsMap[item.customer.id];
      const merchantVoluntaryRefundAmount = merchantVoluntaryRefundAmountMap[item.customer.id];
      const afterSaleAmount = afterSaleAmounts[item.customer.id];
      const customerRemark = distributorCustomerRemarksMap[item.customer.id];
      const memberCardRefundAmount = memberCardRefundAmounts[item.customer.id];
      const giftCardRefundAmount = giftCardRefundAmounts[item.customer.id];
      const giftCardAmountValue = Number(giftCardRefundAmount?.refundAmount ?? 0); // 礼品卡退款金额
      const memberCardAmountValue = Number(memberCardRefundAmount?.refundAmount ?? 0); // 会员卡退款金额
      const afterSaleAmountValue = Number(afterSaleAmount?.totalPrice ?? 0); // 售后退款金额
      const refundAmountValue = Number(merchantVoluntaryRefundAmount?.refundAmount ?? 0); // 退款金额
      const orderTotalAmount = Number(customerStatistic?.orderTotalAmount ?? 0); // 订单总金额
      const orderTotalAmountValue =
        orderTotalAmount - refundAmountValue - afterSaleAmountValue - memberCardAmountValue - giftCardAmountValue; // 订单总金额 - 主动退款金额 - 退款金额 - 会员卡退款金额 - 礼品卡退款金额

      return {
        ...item,
        orderTotal: Number(customerStatistic?.orderTotal ?? 0), // 订单总数
        orderTotalAmount: orderTotalAmountValue,
        lastOrderTime: customerStatistic?.lastOrderTime ? new Date(customerStatistic?.lastOrderTime) : null, // 最后下单时间
        // 客单价
        perCustomerTransaction: Math.floor(
          customerStatistic?.orderTotal ? orderTotalAmountValue / Number(customerStatistic.orderTotal) : 0,
        ),
        distributorCustomerRemark: customerRemark?.remark ?? '',
      };
    });
  }

  async setDistributorBindingRemark(ctx: RequestContext, customerId: ID, remark: string) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    let distributedCustomer = await this.connection.getRepository(ctx, DistributorCustomer).findOne({
      where: {
        customer: {
          id: customerId,
        },
        distributor: {
          id: distributor.id,
        },
      },
    });
    if (distributedCustomer) {
      distributedCustomer.remark = remark;
      await this.connection.getRepository(ctx, DistributorCustomer).save(distributedCustomer);
    } else {
      distributedCustomer = new DistributorCustomer({
        customer: {id: customerId},
        distributor: {id: distributor.id},
        remark: remark,
      });
      await this.connection.getRepository(ctx, DistributorCustomer).save(distributedCustomer);
    }
    return this.distributorBindingCustomerDetail(ctx, customerId);
  }

  async distributorShareProduct(ctx: RequestContext, productId: ID) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    const product = await this.connection.getRepository(ctx, Product).findOne({
      where: {id: productId},
    });
    if (!product) {
      throw new Error('商品不存在');
    }
    const sharingProduct = await this.connection.getRepository(ctx, DistributorProductRecord).findOne({
      where: {
        distributor: {
          id: distributor.id,
        },
        product: {
          id: productId,
        },
      },
    });
    if (sharingProduct) {
      sharingProduct.shareCount += 1;
      await this.connection.getRepository(ctx, DistributorProductRecord).save(sharingProduct);
      return sharingProduct;
    } else {
      const newSharingProduct = new DistributorProductRecord({
        distributor: {id: distributor.id},
        product: {id: productId},
        shareCount: 1,
      });
      await this.connection.getRepository(ctx, DistributorProductRecord).save(newSharingProduct);
      return newSharingProduct;
    }
  }

  async distributorShareProducts(
    ctx: RequestContext,
    search: string,
    startDateTime: Date,
    endDateTime: Date,
    options: ListQueryOptions<DistributorProductRecord>,
    relations: RelationPaths<DistributorProductRecord>,
  ) {
    const distributor = await this.getDistributor(ctx);
    if (!distributor) {
      throw new Error('分销商不存在');
    }
    // 获取分销商商品在matomo中的数据 访客,浏览量,加购人数,加购件数
    const productVisitorData = await this.getDistributionMatomoData(ctx, distributor, startDateTime, endDateTime);
    const productIds = productVisitorData.map(item => item.productId);
    const qb = this.listQueryBuilder.build(
      DistributorProductRecord,
      {},
      {
        ctx,
        relations: relations,
      },
    );
    qb.leftJoinAndSelect(`${qb.alias}.product`, 'product');
    qb.leftJoinAndSelect('product.translations', 'translations');
    qb.leftJoin(`${qb.alias}.distributor`, 'distributor');
    qb.andWhere(`${qb.alias}.distributorId = :distributorId`, {distributorId: distributor.id});
    if (search) {
      qb.andWhere(`translations.name like :productName`, {productName: `%${search}%`});
    }
    qb.addOrderBy(`${qb.alias}.createdAt`, 'DESC');
    const [items, totalItems] = await qb.getManyAndCount();
    let newItems = items;
    if (productIds.length > 0) {
      // 按照productIds排序
      newItems = items.sort((a, b) => {
        return productIds.indexOf(String(a.product.id)) - productIds.indexOf(String(b.product.id));
      });
    }
    const skip = options?.skip ?? 0;
    const take = options?.take ?? 10;
    newItems = newItems.slice(skip, skip + take);
    // 获取分销商商品统计数据
    newItems = await this.getDistributorProductStatistics(
      ctx,
      newItems,
      startDateTime,
      endDateTime,
      distributor,
      productVisitorData,
    );
    return {
      items: newItems,
      totalItems,
    };
  }
  async getDistributorProductStatistics(
    ctx: RequestContext,
    items: DistributorProductRecord[],
    startDateTime = new Date(0),
    endDateTime = new Date(),
    distributor: Distributor,
    productVisitorData: {
      productId: ID;
      visitorsCount: number;
      pageViews: number;
      addCartPeopleCount: number;
      addCartCount: number;
    }[],
  ) {
    const productIds = items.map(item => item.product.id);
    if (productIds.length === 0) {
      return items;
    }
    // 支付件数  支付人数
    const payProductStatistics = await this.getDistributionPayProductStatistics(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
      productIds,
    );

    // 申请退款件数 成功退款件数 退款订单数 退款人数
    const afterProductStatistics = await this.interfaceAfterSale.getRefundProductStatistics(
      ctx,
      distributor,
      startDateTime,
      endDateTime,
      productIds,
    );
    items = items.map(item => {
      const payProductStatistic = payProductStatistics?.find(
        data => String(data.productId) === String(item.product.id),
      );
      const afterProductStatistic = afterProductStatistics?.find(
        data => String(data.productId) === String(item.product.id),
      );
      const productVisitor = productVisitorData?.find(data => String(data.productId) === String(item.product.id));
      const productStatistics = {
        // 商品ID
        productId: item.product.id,
        // 访客数
        visitorsCount: Number(productVisitor?.visitorsCount ?? 0),
        // 浏览量
        pageViews: Number(productVisitor?.pageViews ?? 0),
        // 加购人数
        addCartPeopleCount: Number(productVisitor?.addCartPeopleCount ?? 0),
        // 加购件数
        addCartCount: Number(productVisitor?.addCartCount ?? 0),
        // 支付人数
        numberOfPayers: Number(payProductStatistic?.payPeopleCount ?? 0),
        // 支付件数
        numberOfPaidItems: Number(payProductStatistic?.payProductCount ?? 0),
        // 支付金额
        paymentAmount: Number(payProductStatistic?.payProductAmount ?? 0),
        // 申请退款件数
        numberOfRefundItems: Number(afterProductStatistic?.numberOfRefundItems ?? 0),
        // 成功退款件数
        numberOfSuccessfulRefunds: Number(afterProductStatistic?.numberOfSuccessfulRefunds ?? 0),
        // 成功退款人数
        numberOfSuccessfulRefundPeople: Number(afterProductStatistic?.numberOfSuccessfulRefundPeople ?? 0),
        // 成功退款订单数
        numberOfSuccessfulRefundOrders: Number(afterProductStatistic?.numberOfSuccessfulRefundOrders ?? 0),
        // 支付转化率
        conversionRateOfSingleProduct:
          Number(productVisitor?.visitorsCount ?? 0) && Number(payProductStatistic?.payPeopleCount ?? 0)
            ? Number(
                ((Number(payProductStatistic?.payPeopleCount) / Number(productVisitor?.visitorsCount)) * 100).toFixed(
                  2,
                ),
              )
            : 0,
      };
      return {
        ...item,
        productStatistics: productStatistics,
      };
    });
    return items;
  }
  async getDistributionPayProductStatistics(
    ctx: RequestContext,
    distributor: Distributor,
    startDateTime: Date,
    endDateTime: Date,
    productIds: ID[],
  ) {
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('order.id', 'orderId')
      .leftJoin('distributorOrder.order', 'order')
      .leftJoin('distributorOrder.distributor', 'distributor')
      .leftJoin('order.lines', 'lines')
      .leftJoin('lines.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('distributor.id = :distributorId')
      .andWhere('order.id is not null')
      .andWhere('product.id IN (:...productIds)')
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime')
      .getQuery();
    const payProductStatistics = await this.connection
      .getRepository(ctx, OrderLinePromotionDetail)
      .createQueryBuilder('orderLinePromotionDetail')
      .select('product.id', 'productId')
      .addSelect('COUNT(DISTINCT customer.id)', 'payPeopleCount')
      .addSelect('COUNT(order.id)', 'payOrderCount')
      .addSelect('SUM(orderLinePromotionDetail.count)', 'payProductCount')
      .addSelect('SUM(orderLinePromotionDetail.price)', 'payProductAmount')
      .leftJoin('orderLinePromotionDetail.product', 'product')
      .leftJoin('orderLinePromotionDetail.order', 'order')
      .leftJoin('orderLinePromotionDetail.customer', 'customer')
      .andWhere('product.id IN (:...productIds)', {productIds})
      .andWhere('orderLinePromotionDetail.paymentTime BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      })
      .andWhere('order.id IN (' + subQuery + ')')
      .setParameters({productIds, distributorId: distributor.id, startDateTime, endDateTime})
      .groupBy('product.id')
      .getRawMany<{
        productId: ID;
        payPeopleCount: number;
        payOrderCount: number;
        payProductCount: number;
        payProductAmount: number;
      }>();
    return payProductStatistics;
  }

  // 获取分销员的商品访问数据和加购数据
  async getDistributionMatomoData(
    ctx: RequestContext,
    distributor: Distributor,
    startDateTime: Date,
    endDateTime: Date,
  ) {
    // 获取分销员的商品访问数据
    const productVisitorDatas = await this.matomoService.getDistributorProductVisitorDataStatistics(
      ctx,
      startDateTime,
      endDateTime,
      distributor.id,
    );
    // 获取分销员的商品加购数据
    const productAddCartDatas = await this.matomoService.getDistributorProductAddCartDataStatistics(
      ctx,
      startDateTime,
      endDateTime,
      distributor.id,
    );
    const visitorProductIds = productVisitorDatas?.map(item => item.productId) ?? [];
    const addCardProductIds = productAddCartDatas?.map(item => item.productId) ?? [];
    const productIds = Array.from(new Set([...visitorProductIds, ...addCardProductIds]));
    let productData = productIds.map(productId => {
      const productVisitorData = productVisitorDatas?.find(item => String(item.productId) === String(productId));
      const productAddCartData = productAddCartDatas?.find(item => String(item.productId) === String(productId));
      return {
        productId: String(productId),
        visitorsCount: Number(productVisitorData?.visits ?? 0),
        pageViews: Number(productVisitorData?.hits ?? 0),
        addCartPeopleCount: Number(productAddCartData?.visits ?? 0),
        addCartCount: Number(productAddCartData?.sumEventValue ?? 0),
      };
    });
    productData = productData.sort((a, b) => b.visitorsCount - a.visitorsCount);
    return productData;
  }

  async distributorOrderTotalAmountStatistics(ctx: RequestContext) {
    const distributorDetails = await this.connection
      .getRepository(ctx, DistributorDetail)
      .createQueryBuilder('distributorDetail')
      .leftJoinAndSelect('distributorDetail.distributor', 'distributor')
      .leftJoinAndSelect('distributorDetail.distributorGroup', 'distributorGroup')
      .andWhere('distributorGroup.id is not null')
      .getMany();
    for (const distributorDetail of distributorDetails) {
      const detailTime = distributorDetail.detailTime;
      const distributionId = distributorDetail.distributor.id;
      const distributionGroupId = distributorDetail.distributorGroup.id;
      if (!detailTime || !distributionId || !distributionGroupId) {
        continue;
      }
      const startDateTime = DateTime.fromJSDate(detailTime).startOf('day').toJSDate();
      const endDateTime = DateTime.fromJSDate(detailTime).endOf('day').toJSDate();
      // 获取分销员的总金额
      const distributorTotalAmount = await this.getDistributorOrderTotalAmount(
        ctx,
        distributionId,
        distributionGroupId,
        startDateTime,
        endDateTime,
      );
      await this.connection.getRepository(ctx, DistributorDetail).update(
        {id: distributorDetail.id},
        {
          orderTotalAmount: distributorTotalAmount?.orderTotalAmount ?? 0,
        },
      );
    }
    return 'success';
  }
  async getDistributorOrderTotalAmount(
    ctx: RequestContext,
    distributionId: ID,
    distributionGroupId: ID,
    startDateTime: Date,
    endDateTime: Date,
  ) {
    const distributorOrderTotalAmount = await this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('SUM(distributorOrder.price)', 'orderTotalAmount')
      .leftJoin('distributorOrder.distributor', 'distributor')
      .leftJoin('distributorOrder.distributorGroup', 'distributorGroup')
      .andWhere('distributor.id = :distributionId', {distributionId})
      .andWhere('distributorGroup.id = :distributionGroupId', {distributionGroupId})
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime', {
        startDateTime,
        endDateTime,
      })
      .getRawOne<{
        orderTotalAmount: number;
      }>();
    return distributorOrderTotalAmount;
  }

  // 已经绑定的分销员的渠道ID
  private readonly existDistributorChannelId = 2;

  async findCustomerDistributor(ctx: RequestContext, customerId: ID) {
    const lock = await this.redLockService.lockResource(
      `Customer:findCustomerDistributor:${customerId}:${ctx.channelId}`,
    );
    try {
      let customerBindingDistributor = await this.connection.getRepository(ctx, CustomerBindingDistributor).findOne({
        where: {
          customerId: customerId,
          channelId: ctx.channelId,
        },
      });
      if (customerBindingDistributor) {
        return customerBindingDistributor;
      }
      let distributionId: ID = 0;
      if (idsAreEqual(ctx.channelId, this.existDistributorChannelId)) {
        const customer = await this.customerService.findOne(ctx, customerId);
        if (customer) {
          if ((customer.customFields as CustomerCustomFields).distributor) {
            distributionId = (customer.customFields as CustomerCustomFields).distributor?.id as ID;
          }
        } else {
          throw new Error('用户不存在');
        }
      }
      if (!distributionId) {
        return null;
      }
      customerBindingDistributor = new CustomerBindingDistributor({
        customer: {id: customerId},
        distributor: {id: distributionId},
        channelId: ctx.channelId,
      });
      customerBindingDistributor = await this.connection
        .getRepository(ctx, CustomerBindingDistributor)
        .save(customerBindingDistributor);
      return customerBindingDistributor;
    } catch (error) {
      Logger.error(`findCustomerDistributor error: ${error.message}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async updateCustomerDistributor(ctx: RequestContext, customerId: ID, distributorId: ID) {
    const lock = await this.redLockService.lockResource(
      `Customer:updateCustomerDistributor:${customerId}:${ctx.channelId}`,
    );
    try {
      let customerBindingDistributor = await this.findCustomerDistributor(ctx, customerId);
      if (customerBindingDistributor) {
        if (customerBindingDistributor?.distributorId) {
          if (idsAreEqual(customerBindingDistributor.distributorId, distributorId)) {
            return customerBindingDistributor;
          }
        }
        customerBindingDistributor.distributorId = distributorId;
        customerBindingDistributor = await this.connection
          .getRepository(ctx, CustomerBindingDistributor)
          .save(customerBindingDistributor);
        return customerBindingDistributor;
      } else {
        customerBindingDistributor = new CustomerBindingDistributor({
          customer: {id: customerId},
          distributor: {id: distributorId},
          channelId: ctx.channelId,
        });
        customerBindingDistributor = await this.connection
          .getRepository(ctx, CustomerBindingDistributor)
          .save(customerBindingDistributor);
        return customerBindingDistributor;
      }
    } catch (error) {
      Logger.error(`updateCustomerDistributor error: ${error.message}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }
}
