import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ActiveOrderService,
  ChannelService,
  EntityNotFoundError,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  OrderService,
  ProductService,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {PurchasePremium} from '../entities';
import {PurchasePremiumProduct} from '../entities/purchase-premium-product.entity';
import {
  CouponState,
  ProductCustomFields,
  ProgramLinkInput,
  PromotionType,
  PurchasePremiumInput,
  ValidityPeriodType,
} from '../generated-admin-types';
import {ApplicableProduct, DeletionResult, OrderLineCustomFields, PurchasePattern} from '../generated-shop-types';
import {purchasePremiumAction} from '../promotion/action/purchase-premium.action';
import {customerGroupList} from '../promotion/conditions';
import {productTotalPriceConditions} from '../promotion/conditions/product-total-price.conditions';
import {ActivityProduct, ActivityUtils} from '../utils';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';

export type PurchasePremiumValue = {
  price: number;
  productId: string;
  productName: string;
};
@Injectable()
export class PurchasePremiumService {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private productService: ProductService,
    private promotionService: PromotionService,
    private activeOrderService: ActiveOrderService,
    // private couponService: CouponService,
    private orderService: OrderService,
    private requestContextService: RequestContextService,
    private commonService: CommonService,
    private kvsService: KvsService,
    private promotionResultDetailService: PromotionResultDetailService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private productCustomService: ProductCustomService,
  ) {}

  async getPurchasePremiumLink(ctx: RequestContext, input: ProgramLinkInput) {
    const purchasePremiumId = input.id;
    const purchasePremium = await this.findOne(ctx, purchasePremiumId);
    if (!purchasePremium) {
      throw new EntityNotFoundError('PurchasePremium', purchasePremiumId);
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(purchasePremium.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }
  async getPurchasePremiumQRCodeLink(ctx: RequestContext, input: ProgramLinkInput) {
    const purchasePremiumId = input.id;
    const purchasePremium = await this.findOne(ctx, purchasePremiumId);
    if (!purchasePremium) {
      throw new EntityNotFoundError('PurchasePremium', purchasePremiumId);
    }
    if (purchasePremium.smallProgramQRCodeLink) {
      return purchasePremium.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(purchasePremium.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    purchasePremium.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
    await this.cacheService.removeCache([
      CacheKeyManagerService.purchasePremium(purchasePremiumId, ctx.channelId),
      CacheKeyManagerService.promotion(purchasePremium?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(purchasePremium?.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }
  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<PurchasePremium>,
    relations: RelationPaths<PurchasePremium>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(PurchasePremium, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          PromotionType.PurchaseAtAPremium,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }

  async findOne(
    ctx: RequestContext,
    purchasePremiumId: ID,
    options?: ListQueryOptions<PurchasePremium>,
    relations?: RelationPaths<PurchasePremium>,
  ) {
    const memoryStorageCacheKey = CacheKeyManagerService.purchasePremium(purchasePremiumId, ctx.channelId);
    if (ctx.apiType === 'shop') {
      const cacheData = this.memoryStorageService.get(memoryStorageCacheKey);
      if (cacheData) {
        return cacheData as PurchasePremium;
      }
    }
    const qb = this.listQueryBuilder.build(PurchasePremium, options, {
      ctx,
      relations: (relations ?? []).concat(['promotion', 'purchasePremiumProducts', 'purchasePremiumProducts.product']),
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.id=:purchasePremiumId`, {purchasePremiumId: purchasePremiumId});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const purchasePremiums = await qb.getMany();
    if (purchasePremiums.length > 0) {
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, purchasePremiums[0]);
      }
      return purchasePremiums[0];
    }
    return null;
  }
  async upsertPurchasePremium(ctx: RequestContext, input: PurchasePremiumInput) {
    await this.validate(ctx, input);
    // 修改前的可用商品
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const purchasePremium = await this.findOne(ctx, input.id);
      if (!purchasePremium) {
        throw new EntityNotFoundError('活动不存在', input.id);
      }
      oldApplicableProduct = purchasePremium.applicableProduct;
    }
    let purchasePremium = new PurchasePremium({
      ...(input as unknown as PurchasePremium),
      state: CouponState.Normal,
    });
    if (input.validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      if (new Date(input.validityPeriod.startTime) >= new Date()) {
        purchasePremium.state = CouponState.NotStarted;
      }
      if (new Date(input.validityPeriod.endTime) <= new Date()) {
        purchasePremium.state = CouponState.HaveEnded;
      }
    } else {
      purchasePremium.state = CouponState.Normal;
    }
    purchasePremium = await this.channelService.assignToCurrentChannel(purchasePremium, ctx);
    purchasePremium = await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
    await this.connection.getRepository(ctx, PurchasePremiumProduct).delete({
      purchasePremium: {id: purchasePremium.id},
    });
    const goodsForExchanges = input.goodsForExchanges;
    const purchasePremiumProducts: PurchasePremiumValue[] = [];
    for (const goodsForExchange of goodsForExchanges) {
      const product = await this.productService.findOne(ctx, goodsForExchange.productId);
      if (!product) {
        throw new Error('');
      }
      let purchasePremiumProduct = new PurchasePremiumProduct({
        ...goodsForExchange,
        purchasePremium,
        product,
      });
      purchasePremiumProducts.push({
        price: goodsForExchange.price,
        productId: goodsForExchange.productId,
        productName: product.name,
      });
      purchasePremiumProduct = await this.channelService.assignToCurrentChannel(purchasePremiumProduct, ctx);
      await this.connection.getRepository(ctx, PurchasePremiumProduct).save(purchasePremiumProduct);
    }
    const promotion = await this.upsertPromotion(ctx, purchasePremium, purchasePremiumProducts);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      input.applicableProduct,
      oldApplicableProduct,
    );
    purchasePremium.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
    if (input.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.purchasePremium(input.id, ctx.channelId),
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return this.findOne(ctx, purchasePremium.id);
  }
  async upsertPromotion(
    ctx: RequestContext,
    purchasePremium: PurchasePremium,
    purchasePremiumProducts: PurchasePremiumValue[],
  ) {
    const promotionInput = {
      name: purchasePremium.displayName,
      startsAt: purchasePremium.validityPeriod.startTime,
      endsAt: purchasePremium.validityPeriod.endTime,
      enabled: purchasePremium.state === CouponState.Normal || purchasePremium.state === CouponState.NotStarted,
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: purchasePremium.displayName,
        },
      ],
      couponCode: purchasePremium.promotion ? purchasePremium.promotion.couponCode : generatePublicId(),
      conditions: [
        {
          code: productTotalPriceConditions.code,
          arguments: [
            {name: 'productIds', value: purchasePremium.applicableProduct.productIds},
            {name: 'type', value: purchasePremium.applicableProduct.applicableType},
            {name: 'minimum', value: purchasePremium.minimum},
          ],
        } as never,
      ],
      actions: [
        {
          code: purchasePremiumAction.code,
          arguments: [{name: 'purchasePremiumProducts', value: purchasePremiumProducts}],
        } as never,
      ],
      customFields: {
        type: PromotionType.PurchaseAtAPremium,
        isAutomatic: true,
        activityName: purchasePremium.displayName,
      },
    };
    if (purchasePremium.whetherRestrictUsers) {
      //是否限制参与用户 创建满减送的参与用户的条件
      promotionInput.conditions.push({
        code: customerGroupList.code,
        arguments: [
          {name: 'isOpen', value: purchasePremium.whetherRestrictUsers},
          {name: 'groupType', value: purchasePremium.groupType},
          {name: 'customerGroupIds', value: purchasePremium.memberPlanIds},
        ],
      } as never);
    }
    purchasePremium = (await this.findOne(ctx, purchasePremium.id)) as PurchasePremium;
    if (purchasePremium.promotion) {
      const updatePromotion = {
        ...promotionInput,
        id: purchasePremium.promotion.id,
        customFields: {
          type: PromotionType.PurchaseAtAPremium,
          isAutomatic: true,
          stackingDiscountSwitch: purchasePremium.stackingDiscountSwitch,
          stackingPromotionTypes: purchasePremium.stackingPromotionTypes,
          activityName: purchasePremium.displayName,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: PromotionType.PurchaseAtAPremium,
          isAutomatic: true,
          stackingDiscountSwitch: purchasePremium.stackingDiscountSwitch,
          stackingPromotionTypes: purchasePremium.stackingPromotionTypes,
          activityName: purchasePremium.displayName,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: PurchasePremiumInput) {
    if (input.validityPeriod.type === ValidityPeriodType.TemporalInterval) {
      if (!input.validityPeriod.startTime || !input.validityPeriod.endTime) {
        throw new Error('The start time and end time of an activity cannot be empty');
      }
    } else {
      throw new Error('The activity time type is incorrect');
    }
    if (input.minimum < 0) {
      throw new Error('The threshold of activity cannot be less than zero');
    }
    const goodsForExchanges = input.goodsForExchanges;
    for (const goodsForExchange of goodsForExchanges) {
      if (!goodsForExchange) {
        continue;
      }
      if (goodsForExchange.price < 0) {
        throw new Error('The exchange price cannot be less than zero');
      }
      const product = await this.productService.findOne(ctx, goodsForExchange.productId, ['variants']);
      if (!product) {
        throw new Error(`productVariantID:${goodsForExchange.productId} not exist`);
      }

      const minPrice = Number((product.customFields as ProductCustomFields).price ?? 0); // product.variants[0].productVariantPrices[0].price;
      //productVariantPrices 价格中存在不同币种的情况
      // for (const variant of product.variants) {
      //   const prices = variant.productVariantPrices;
      //   for (const price of prices) {
      //     if (minPrice > price.price) {
      //       minPrice = price.price;
      //     }
      //   }
      // }
      if (goodsForExchange.price > minPrice) {
        throw new Error('The replacement price shall not be greater than the original price of the commodity');
      }
      const purchasePremiumProducts = await this.getAMarkupOnTheProduct(ctx, goodsForExchange.productId);
      if (!input.id) {
        if (purchasePremiumProducts.length > 0) {
          throw new Error(`产品：${product?.name}已经参与其它加价购活动`);
        }
      } else {
        for (const purchasePremiumProduct of purchasePremiumProducts) {
          if (purchasePremiumProduct.purchasePremium.id !== input.id) {
            throw new Error(`产品：${product?.name}已经参与其它加价购活动`);
          }
        }
      }
    }
    const inputProduct: ActivityProduct = {
      startTime: input.validityPeriod.startTime,
      endTime: input.validityPeriod.endTime,
      productIds: input.applicableProduct.productIds as ID[],
      applicableType: input.applicableProduct.applicableType,
    };

    await this.verifyProductIdsAndPurchasePremium(ctx, inputProduct, input.id as ID);
    if (input.stackingDiscountSwitch) {
      if (!input.stackingPromotionTypes || input.stackingPromotionTypes.length <= 0) {
        throw new Error('When a preference can be stacked, stackingActivity cannot be empty');
      }
    }
  }

  async verifyProductIdsAndPurchasePremium(ctx: RequestContext, inputProduct: ActivityProduct, purchasePremiumId?: ID) {
    const allPurchasePremiums = await this.getNotPurchasePremiums(ctx, purchasePremiumId);
    for (const purchasePremium of allPurchasePremiums) {
      const purchasePremiumPresentPresent: ActivityProduct = {
        startTime: purchasePremium.validityPeriod.startTime,
        endTime: purchasePremium.validityPeriod.endTime,
        productIds: purchasePremium.applicableProduct.productIds as ID[],
        applicableType: purchasePremium.applicableProduct.applicableType,
      };
      // 判断时间是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(purchasePremiumPresentPresent, inputProduct);
      if (productId && ActivityUtils.isTimeOverlappingWithOtherActivity(purchasePremiumPresentPresent, inputProduct)) {
        if (productId === -1) {
          throw new Error(`活动商品和加价购活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和加价购活动商品冲突`);
      }
    }
  }

  async getNotPurchasePremiums(ctx: RequestContext, id?: ID) {
    const qb = this.listQueryBuilder.build(
      PurchasePremium,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (id) {
      qb.andWhere(`${qb.alias}.id != :id`, {id});
    }
    qb.andWhere(`${qb.alias}.state != :state`, {state: CouponState.Failure});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const purchasePremiums = await qb.getMany();
    return purchasePremiums;
  }

  /**
   * 获取加价购产品
   * @param ctx
   * @param productId
   * @returns
   */
  async getAMarkupOnTheProduct(ctx: RequestContext, productId: ID) {
    const qb = this.listQueryBuilder.build(PurchasePremiumProduct, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.product`, 'product');
    qb.leftJoinAndSelect(`${qb.alias}.purchasePremium`, 'purchasePremium');
    qb.andWhere('product.id = :productId', {productId: productId});
    qb.andWhere(`${qb.alias}.enabled = true`);
    qb.andWhere(`purchasePremium.deletedAt is null`);
    const purchasePremiumProducts = await qb.getMany();
    return purchasePremiumProducts;
  }

  // async purchasePremiumProduct(
  //   ctx: RequestContext,
  //   orderId?: ID,
  //   options?: ListQueryOptions<PurchasePremiumProduct>,
  //   relations?: RelationPaths<PurchasePremiumProduct>,
  // ) {
  //   if (!orderId) {
  //     const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
  //     if (!order) {
  //       throw new Error('order not exist');
  //     }
  //     orderId = order.id;
  //   }
  //   const order = await this.orderService.findOne(ctx, orderId);
  //   if (!order) {
  //     throw new Error('order not exist');
  //   }
  //   const purchasePremiumIds = await this.getUsablePurchasePremiumIds(ctx, order);
  //   if (purchasePremiumIds.length <= 0) {
  //     return {
  //       items: [],
  //       totalItems: 0,
  //     };
  //   }
  //   const qb = this.listQueryBuilder.build(PurchasePremiumProduct, options, {
  //     relations: relations,
  //     channelId: ctx.channelId,
  //   });
  //   qb.leftJoinAndSelect(`${qb.alias}.purchasePremium`, `purchasePremium`).andWhere(
  //     'purchasePremium.id in (:...purchasePremiumIds)',
  //     {purchasePremiumIds: purchasePremiumIds},
  //   );
  //   qb.andWhere(`purchasePremium.deletedAt is null`);
  //   const [items, totalItems] = await qb.getManyAndCount();
  //   return {
  //     items,
  //     totalItems,
  //   };
  // }
  /**
   * 获取指定订单可用的加价购活动id
   * @param ctx
   * @param order 订单
   * @returns
   */
  // async getUsablePurchasePremiumIds(ctx: RequestContext, order: Order) {
  //   const purchasePremiums = await this.getActivePurchasePremium(ctx);
  //   const usablePurchasePremiumIds = [];
  //   for (const purchasePremium of purchasePremiums) {
  //     const promotion = purchasePremium.promotion;
  //     const availableState = await this.couponService.verifyTheOfferIsAvailable(ctx, promotion, order);
  //     if (availableState) {
  //       usablePurchasePremiumIds.push(purchasePremium.id);
  //     }
  //   }
  //   return usablePurchasePremiumIds;
  // }

  /**
   * 获取活跃的加价购活动
   * @param ctx
   * @param options
   * @param relations
   * @returns
   */
  async getActivePurchasePremium(
    ctx: RequestContext,
    options?: ListQueryOptions<PurchasePremium>,
    relations?: RelationPaths<PurchasePremium>,
  ) {
    const qb = this.listQueryBuilder.build(PurchasePremium, options, {
      ctx,
      relations: relations ?? ['promotion'],
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.state = :state`, {state: CouponState.Normal});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    return qb.getMany();
  }

  async failure(ctx: RequestContext, purchasePremiumId: ID) {
    const purchasePremium = await this.findOne(ctx, purchasePremiumId);
    if (!purchasePremium) {
      throw new Error('purchasePremium not exist');
    }
    purchasePremium.state = CouponState.Failure;
    await this.connection.getRepository(ctx, Promotion).update(purchasePremium.promotion.id, {enabled: false});
    await this.failurePurchasePremiumProduct(ctx, purchasePremium.id);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
    await this.cacheService.removeCache([
      CacheKeyManagerService.purchasePremium(purchasePremiumId, ctx.channelId),
      CacheKeyManagerService.promotion(purchasePremium?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(purchasePremium?.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, purchasePremium.applicableProduct);
    return this.findOne(ctx, purchasePremiumId);
  }

  async failurePurchasePremiumProduct(ctx: RequestContext, purchasePremiumId: ID) {
    const purchasePremiumProducts = await this.connection
      .getRepository(ctx, PurchasePremiumProduct)
      .createQueryBuilder('purchasePremiumProduct')
      .leftJoin('purchasePremiumProduct.channels', 'channel')
      .andWhere('channel.id =:channelId', {channelId: ctx.channelId})
      .select('purchasePremiumProduct.id', 'purchasePremiumProductId')
      .leftJoin('purchasePremiumProduct.purchasePremium', 'purchasePremium')
      .andWhere('purchasePremium.id =:purchasePremiumId', {purchasePremiumId: purchasePremiumId})
      .getRawMany<{
        purchasePremiumProductId: string;
      }>();
    const purchasePremiumProductIds = purchasePremiumProducts.reduce(
      (value, item) => [...value, item.purchasePremiumProductId],
      <Array<string>>[],
    );
    await this.connection
      .getRepository(ctx, PurchasePremiumProduct)
      .update(purchasePremiumProductIds, {enabled: false});
  }

  // async removePurchasePremiumToOrder(ctx: RequestContext, productVariantId: ID, orderId?: ID) {
  //   const purchasePremiumProduct = await this.connection
  //     .getRepository(ctx, PurchasePremiumProduct)
  //     .createQueryBuilder('purchasePremiumProduct')
  //     .leftJoinAndSelect('purchasePremiumProduct.product', 'product')
  //     .leftJoinAndSelect('product.variants', 'variants')
  //     .leftJoinAndSelect('purchasePremiumProduct.purchasePremium', 'purchasePremium')
  //     .leftJoinAndSelect('purchasePremium.promotion', 'promotion')
  //     .andWhere('variants.id=:productVariantId', {productVariantId: productVariantId})
  //     .getOne();
  //   if (!purchasePremiumProduct) {
  //     throw new Error('The replacement item could not be found');
  //   }
  //   if (!orderId) {
  //     const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
  //     if (!order) {
  //       throw new Error('order not exist');
  //     }
  //     orderId = order.id;
  //   }
  //   const purchasePremium = purchasePremiumProduct.purchasePremium;
  //   if (!purchasePremium) {
  //     throw new Error('No markup activity found for this item');
  //   }
  //   const promotion = purchasePremium.promotion;
  //   await this.removePurchasePremiumProduct(ctx, orderId);
  //   await this.orderService.removeCouponCode(ctx, orderId, promotion.couponCode);
  //   return this.orderService.findOne(ctx, orderId);
  // }

  // async addPurchasePremiumToOrder(ctx: RequestContext, productVariantId: ID, orderId?: ID) {
  //   const purchasePremiumProduct = await this.connection
  //     .getRepository(ctx, PurchasePremiumProduct)
  //     .createQueryBuilder('purchasePremiumProduct')
  //     .leftJoinAndSelect('purchasePremiumProduct.product', 'product')
  //     .leftJoinAndSelect('product.variants', 'variants')
  //     .leftJoinAndSelect('purchasePremiumProduct.purchasePremium', 'purchasePremium')
  //     .leftJoinAndSelect('purchasePremium.promotion', 'promotion')
  //     .andWhere('purchasePremiumProduct.enabled =:enabled', {enabled: true})
  //     .andWhere('variants.id=:productVariantId', {productVariantId: productVariantId})
  //     .andWhere('purchasePremium.deletedAt is null')
  //     .getOne();
  //   if (!purchasePremiumProduct) {
  //     throw new Error('The replacement item could not be found');
  //   }
  //   if (!orderId) {
  //     const order = await this.activeOrderService.getActiveOrder(ctx, undefined);
  //     if (!order) {
  //       throw new Error('order not exist');
  //     }
  //     orderId = order.id;
  //   }
  //   let order = await this.orderService.findOne(ctx, orderId, [
  //     'lines',
  //     'promotions',
  //     'customer',
  //     'lines.productVariant',
  //   ]);
  //   if (!order) {
  //     throw new Error('order not exist');
  //   }
  //   const purchasePremium = purchasePremiumProduct.purchasePremium;
  //   if (!purchasePremium) {
  //     throw new Error('No markup activity found for this item');
  //   }
  //   const promotion = purchasePremium.promotion;
  //   const availableState = await this.couponService.verifyTheOfferIsAvailable(ctx, promotion, order);
  //   if (!availableState) {
  //     throw new Error('Your order does not meet the exchange conditions');
  //   }
  //   await this.removePurchasePremiumProduct(ctx, order.id);
  //   await this.orderService.addItemToOrder(ctx, order.id, productVariantId, 1);
  //   //TODO 添加的商品无法确定哪一个是加价购商品
  //   order = await this.orderService.findOne(ctx, order.id, ['lines', 'promotions', 'customer', 'lines.productVariant']);

  //   if (!order) {
  //     throw new Error('order not exist');
  //   }
  //   const removeDiscounts: PromotionType[] = [];
  //   if (order?.promotions) {
  //     for (const orderPromotion of order.promotions) {
  //       // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //       const discountsType = (orderPromotion.customFields as any).type;
  //       //会员优惠可以叠加
  //       if (discountsType === PromotionType.Member) {
  //         continue;
  //       }
  //       //当加价购获取可以叠加优惠时判断是否是可以叠加的类型 可叠加的直接跳过
  //       if (purchasePremium.stackingDiscountSwitch) {
  //         if (purchasePremium.stackingPromotionTypes.indexOf(discountsType) !== -1) {
  //           continue;
  //         }
  //       }
  //       //删除不可叠加的优惠信息
  //       await this.orderService.removeCouponCode(ctx, order.id, orderPromotion.couponCode);
  //       //添加删除的优惠类型给到前端
  //       if (removeDiscounts.indexOf(discountsType) === -1) {
  //         removeDiscounts.push(discountsType);
  //       }
  //       if ((orderPromotion.customFields as PromotionCustomFields).type === PromotionType.Coupon) {
  //         await this.couponService.couponUnboundOrder(ctx, order, orderPromotion);
  //       }
  //     }
  //   }
  //   await this.orderService.applyCouponCode(ctx, order.id, promotion.couponCode);
  //   for (const line of order.lines) {
  //     if (line.productVariant.id === productVariantId) {
  //       await this.orderService.adjustOrderLine(ctx, order.id, line.id, line.quantity, {
  //         purchasePattern: PurchasePattern.PurchasePremium,
  //       });
  //       break;
  //     }
  //   }
  //   return {order: await this.orderService.findOne(ctx, order.id), removeDiscounts};
  // }

  /**
   * 删除前一次加入的加价购商品
   * @param ctx
   * @param orderId
   */
  async removePurchasePremiumProduct(ctx: RequestContext, orderId: ID) {
    const order = await this.orderService.findOne(ctx, orderId, ['lines']);
    if (!order) {
      throw new Error('order not exist');
    }
    const orderLines = order.lines;
    for (const line of orderLines) {
      if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        await this.orderService.adjustOrderLine(ctx, order.id, line.id, line.quantity - 1, {
          PurchasePattern: PurchasePattern.Ordinary,
        });
      }
    }
  }

  async timeoutPurchasePremiumAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.timeoutPurchasePremium(ctx);
    }
  }

  /**
   * 加价购活动过期状态变更
   */
  async timeoutPurchasePremium(ctx: RequestContext) {
    const purchasePremiums = await this.connection
      .getRepository(ctx, PurchasePremium)
      .find({where: {state: In([CouponState.NotStarted, CouponState.Normal])}, relations: ['promotion', 'channels']});
    for (const purchasePremium of purchasePremiums) {
      const validityPeriod = purchasePremium.validityPeriod;
      if (validityPeriod.type !== ValidityPeriodType.TemporalInterval) {
        continue;
      }
      let isSave = false;
      if (
        new Date(validityPeriod.startTime) <= new Date() &&
        new Date(validityPeriod.endTime) > new Date() &&
        purchasePremium.state === CouponState.NotStarted
      ) {
        purchasePremium.state = CouponState.Normal;
        isSave = true;
      }
      if (new Date(validityPeriod.endTime) <= new Date() && purchasePremium.state === CouponState.Normal) {
        purchasePremium.state = CouponState.HaveEnded;
        isSave = true;
        await this.failurePurchasePremiumProduct(ctx, purchasePremium.id);
      }
      if (isSave) {
        // 根据渠道信息排除默认渠道，更新当前渠道的加价购活动缓存时间
        const channels = purchasePremium?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
        await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
        await this.cacheService.removeCache([
          CacheKeyManagerService.purchasePremium(purchasePremium.id, ctx.channelId),
          CacheKeyManagerService.promotion(purchasePremium?.promotion.id, ctx.channelId),
          CacheKeyManagerService.promotionIncludeFailure(purchasePremium?.promotion.id, ctx.channelId),
        ]);
        const enable = purchasePremium.state === CouponState.Normal;
        await this.connection.getRepository(ctx, Promotion).update(purchasePremium.promotion.id, {enabled: enable});
        await this.productPromotionActiveService.activeProductCacheClear(ctx, purchasePremium.applicableProduct);
      }
    }
  }

  async findOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const purchasePremium = await this.connection
      .getRepository(ctx, PurchasePremium)
      .createQueryBuilder('purchasePremium')
      .leftJoinAndSelect('purchasePremium.promotion', 'promotion')
      .leftJoin('purchasePremium.channels', 'channel')
      .andWhere('channel.id =:channelId', {channelId: ctx.channelId})
      .andWhere('promotion.id=:promotionId', {promotionId})
      .andWhere('purchasePremium.deletedAt is null')
      .take(1)
      .getOne();
    return purchasePremium;
  }

  async softDeletePurchasePremium(ctx: RequestContext, purchasePremiumId: ID) {
    const purchasePremium = await this.findOne(ctx, purchasePremiumId);
    if (!purchasePremium) {
      throw new Error('purchasePremium not exist');
    }
    purchasePremium.deletedAt = new Date();
    purchasePremium.state = CouponState.Failure;
    await this.connection.getRepository(ctx, Promotion).update(purchasePremium.promotion.id, {enabled: false});
    await this.promotionService.softDeletePromotion(ctx, purchasePremium.promotion.id);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, PurchasePremium).save(purchasePremium);
    await this.cacheService.removeCache([
      CacheKeyManagerService.purchasePremium(purchasePremiumId, ctx.channelId),
      CacheKeyManagerService.promotion(purchasePremium?.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(purchasePremium?.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, purchasePremium.applicableProduct);
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }

  activityContent(ctx: RequestContext, purchasePremium: PurchasePremium) {
    return [`购买活动商品满${purchasePremium.minimum / 100}元后可参与换购,以优惠价购买换购商品`];
  }

  activitySynopsis(ctx: RequestContext, purchasePremium: PurchasePremium) {
    return `满${purchasePremium.minimum / 100}元享优惠换购`;
  }
}
