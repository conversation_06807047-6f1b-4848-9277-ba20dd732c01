import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {CacheService, CacheKeyManagerService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {
  ChannelService,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Product,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UnauthorizedError,
} from '@vendure/core';
import striptags from 'striptags';
import {In, IsNull} from 'typeorm';
import {ForumActivity, ForumCustomer, ForumPost, ForumReview, ForumTag} from '../entities';
import {OperationError} from '../error.type';
import {
  DeletionResult,
  ForumActivityStatus,
  ForumPostAuditInput,
  ForumPostAuditStatus,
  ForumPostHotDateType,
  ForumPostInput,
  ForumPostStatus,
  ForumPostType,
  NotificationType,
} from '../generated-admin-types';
import {NodeBBForumPost, NodeBBForumReplies, NodeBBForumTopic} from '../node-bb.types';
import {Utils} from '../utils/utils';
import {ForumActivityService} from './forum-activity.service';
import {ForumCustomerService} from './forum-customer.service';
import {NodeBBApiClient} from './forum-node-bb-api.client';
import {ForumNotificationService} from './forum-notification.service';
import {ForumTagService} from './forum-tag.service';
import {InterfaceForumMatomo} from './interface-forum-matomo';
import {NodeBBService} from './node-bb.service';
@Injectable()
export class ForumPostService {
  private interfaceForumMatomo: InterfaceForumMatomo;
  constructor(
    private nodeBBApiClient: NodeBBApiClient,
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private nodeBBService: NodeBBService,
    private forumTagService: ForumTagService,
    private forumCustomerService: ForumCustomerService,
    private redLockService: RedLockService,
    @Inject(forwardRef(() => ForumActivityService))
    private forumActivityService: ForumActivityService,
    private forumNotificationService: ForumNotificationService,
    private cacheService: CacheService,
  ) {}

  registerMatomo(interfaceForumMatomo: InterfaceForumMatomo) {
    this.interfaceForumMatomo = interfaceForumMatomo;
  }

  async forumPostPin(ctx: RequestContext, forumPostId: ID, isPinned: boolean) {
    const forumPost = await this.findOne(ctx, forumPostId);
    if (!forumPost) {
      throw new Error('帖子不存在');
    }
    if (!forumPost.nodeBBTopicId) {
      throw new Error('帖子未创建');
    }
    if (isPinned) {
      await this.nodeBBService.pinTopic(forumPost.nodeBBTopicId);
    } else {
      await this.nodeBBService.unPinTopic(forumPost.nodeBBTopicId);
    }
    forumPost.pinned = isPinned;
    return forumPost;
  }

  async forumPostShare(ctx: RequestContext, id: ID) {
    const lock = await this.redLockService.lockResource(`forumPost_Share_${id}`);
    try {
      const forumPost = await this.findOne(ctx, id);
      if (!forumPost) {
        throw new Error('帖子不存在');
      }
      await this.connection.getRepository(ctx, ForumPost).update(
        {
          id: id,
        },
        {
          shareCount: () => 'shareCount + 1',
        },
      );
      return forumPost;
    } catch (error) {
      Logger.error(error);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async deleteForumPost(ctx: RequestContext, forumPostId: ID, isAdmin = false) {
    const forumPost = await this.findOne(ctx, forumPostId);
    if (!forumPost) {
      throw new Error(`论坛帖子不存在`);
    }
    if (!isAdmin) {
      const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
      if (!forumCustomer) {
        throw new UnauthorizedError();
      }
      if (!idsAreEqual(forumCustomer.id, forumPost.forumCustomerId)) {
        throw new Error('你无权删除该帖子');
      }
    }
    await this.connection.getRepository(ctx, ForumPost).update(
      {
        id: forumPostId,
      },
      {
        deletedAt: new Date(),
      },
    );
    if (forumPost.nodeBBTopicId) {
      // 删除论坛帖子
      await this.nodeBBService.deleteTopic(forumPost.nodeBBTopicId);
    }
    return {
      result: DeletionResult.Deleted,
      message: 'success',
    };
  }

  async getNewForumPostsByTag(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    forumTagId: ID,
    isWish = false,
  ) {
    const forumTag = await this.forumTagService.findOne(ctx, forumTagId, {}, []);
    if (!forumTag) {
      throw new Error('标签不存在');
    }
    const tagHash = forumTag.tagHash;
    const topics = await this.getNewForumPosts(ctx, options, isWish, tagHash);
    return topics;
  }

  async getHotForumPostsByTagHash(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    tagHash: string,
    forumPostHotDateType: ForumPostHotDateType,
    isWish = false,
  ) {
    if (tagHash) {
      // const topics = await this.getHotForumPosts(ctx, options, forumPostHotDateType, isWish, tagHash);
      const topics = await this.getTopicsIncludePinned(ctx, options, 'hot', isWish, tagHash);
      return topics;
    } else {
      // const topics = await this.getHotForumPosts(ctx, options, forumPostHotDateType, isWish);
      const topics = await this.getTopicsIncludePinned(ctx, options, 'hot', isWish);
      return topics;
    }
  }

  async getNewForumPostsByActivity(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    activityId: ID,
    isWish: boolean,
  ) {
    const activity = await this.forumActivityService.findOne(ctx, activityId, {}, []);
    if (!activity) {
      throw new Error('活动不存在');
    }
    if (activity.status === ForumActivityStatus.HaveEnded || activity.status === ForumActivityStatus.Failure) {
      return activity.archivePostData ?? {items: [], totalItems: 0};
    }
    const tagHash = activity.tagHash;
    const topics = await this.getNewForumPosts(ctx, options, isWish, tagHash);
    return topics;
  }

  async getHotForumPostsByActivity(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    activityId: ID,
    forumPostHotDateType: ForumPostHotDateType,
    isWish = false,
  ) {
    const activity = await this.forumActivityService.findOne(ctx, activityId, {}, []);
    if (!activity) {
      throw new Error('活动不存在');
    }
    if (activity.status === ForumActivityStatus.HaveEnded || activity.status === ForumActivityStatus.Failure) {
      return activity.archivePostData ?? {items: [], totalItems: 0};
    }
    const tagHash = activity.tagHash;
    const topics = await this.getHotForumPosts(ctx, options, forumPostHotDateType, isWish, tagHash);
    return topics;
  }

  async getHotForumPostsByTag(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    forumTagId: ID,
    forumPostHotDateType: ForumPostHotDateType,
    isWish = false,
  ) {
    const forumTag = await this.forumTagService.findOne(ctx, forumTagId, {}, []);
    if (!forumTag) {
      throw new Error('标签不存在');
    }
    const tagHash = forumTag.tagHash;
    const topics = await this.getHotForumPosts(ctx, options, forumPostHotDateType, isWish, tagHash);
    return topics;
  }

  forumReviews(review: NodeBBForumReplies) {
    return {
      uid: review.uid,
      pid: review.pid,
      tid: review.tid,
      votes: review.votes,
      upVotes: review.upvotes,
      downVotes: review.downvotes,
      parent: review.parent,
      toPid: review.toPid,
      user: {
        uid: review.user.uid,
        username: review.user.username,
        picture: review.user.picture,
      },
      upVoted: review.upvoted,
      downVoted: review.downvoted,
      repliesContent: striptags(review.content),
      repliesTime: new Date(review.timestamp),
      repliesCount: review?.replies?.count ?? 0,
    };
  }
  async forumDownPost(ctx: RequestContext, pid: string) {
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new UnauthorizedError();
    }
    const forumPost = await this.getForumPostByNodeBBPostId(ctx, parseInt(pid));
    if (!forumPost) {
      throw new OperationError('帖子不存在或已经删除');
    }
    const vote = await this.nodeBBService.downVotePost(pid, forumCustomer.forumUid);
    const upVoted = await this.nodeBBService.getVoteStatus([pid], forumCustomer.forumUid);
    if (vote.code === 'ok') {
      // 被取消的人需要标记存在通知
      await this.markUnreadVotedNotification(ctx, forumCustomer, parseInt(pid), false, 'downvote');
    }
    return {
      operation: vote,
      upVoted: upVoted[0],
    };
  }

  async forumUpPost(ctx: RequestContext, pid: string) {
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new UnauthorizedError();
    }
    const forumPost = await this.getForumPostByNodeBBPostId(ctx, parseInt(pid));
    if (!forumPost) {
      throw new OperationError('帖子不存在或已经删除');
    }
    const vote = await this.nodeBBService.upVotePost(pid, forumCustomer.forumUid);
    let upVoted = false;
    if (vote.code === 'ok') {
      // 被点赞的人需要标记存在通知
      await this.markUnreadVotedNotification(ctx, forumCustomer, parseInt(pid));
      const upVotedInfo = await this.nodeBBService.getVoteStatus([pid], forumCustomer.forumUid);
      upVoted = upVotedInfo[0];
    }
    return {
      operation: vote,
      upVoted: upVoted,
    };
  }
  async markUnreadVotedNotification(
    ctx: RequestContext,
    forumCustomer: ForumCustomer,
    pid: number,
    isNotification = true,
    voteType: 'upvote' | 'downvote' = 'upvote',
  ) {
    let forumCustomerId = 0 as ID;
    const post = await this.connection.getRepository(ctx, ForumPost).findOne({
      where: {
        nodeBBPostId: pid,
      },
      relations: ['forumTags', 'forumActivities'],
    });
    let tagId = 0 as ID;
    let forumActivityId = 0 as ID;
    if (post) {
      forumCustomerId = post.forumCustomerId as ID;
      tagId = post.forumTags?.[0]?.id as ID;
      if (post.forumActivities?.length > 0) {
        if (post.forumActivities[0].status === ForumActivityStatus.Normal) {
          forumActivityId = post.forumActivities?.[0]?.id as ID;
        }
      }
    } else {
      const review = await this.connection.getRepository(ctx, ForumReview).findOne({
        where: {
          postId: pid,
        },
        relations: ['forumPost', 'forumPost.forumTags', 'forumPost.forumActivities'],
      });
      if (review) {
        forumCustomerId = review.forumCustomerId as ID;
        // tagId = review.forumPost?.forumTags?.[0]?.id as ID;
        // if (review.forumPost?.forumActivities?.[0].status === ForumActivityStatus.Normal) {
        //   forumActivityId = review.forumPost?.forumActivities?.[0]?.id as ID;
        // }
        if (voteType === 'upvote') {
          await this.connection.getRepository(ctx, ForumReview).update(
            {
              id: review.id,
            },
            {
              upVotes: () => 'upVotes + 1',
            },
          );
        } else if (voteType === 'downvote') {
          await this.connection.getRepository(ctx, ForumReview).update(
            {
              id: review.id,
            },
            {
              upVotes: () => 'upVotes - 1',
            },
          );
        }
      }
    }
    if (forumCustomerId && isNotification) {
      if (!idsAreEqual(forumCustomerId, forumCustomer.id)) {
        await this.forumNotificationService.changeCommentNotificationStatus(
          ctx,
          forumCustomerId,
          NotificationType.Upvote,
          false,
        );
      }
    }
    if (tagId) {
      await this.markTagVote(ctx, tagId, voteType);
    }
    if (forumActivityId) {
      await this.markActivityVote(ctx, forumActivityId, voteType);
    }
  }
  async markActivityVote(ctx: RequestContext, forumActivityId: ID, voteType: string) {
    await this.connection.getRepository(ctx, ForumActivity).update(
      {
        id: forumActivityId,
      },
      // 如果是点赞则+1 如果是取消点赞则-1
      {
        totalVoteCount: () => (voteType === 'upvote' ? 'totalVoteCount + 1' : 'totalVoteCount - 1'),
      },
    );
  }
  async markTagVote(ctx: RequestContext, tagId: ID, voteType: string) {
    await this.connection.getRepository(ctx, ForumTag).update(
      {
        id: tagId,
      },
      // 如果是点赞则+1 如果是取消点赞则-1
      {
        totalVoteCount: () => (voteType === 'upvote' ? 'totalVoteCount + 1' : 'totalVoteCount - 1'),
      },
    );
    await this.cacheService.removeCache(CacheKeyManagerService.forumActiveTagsAll(ctx.channelId));
  }

  async getForumTopicByPostId(ctx: RequestContext, postId: string) {
    const {isAuthorized, uid} = await this.getUserInfo(ctx);
    const post = (await this.findOne(ctx, postId, {}, [])) as ForumPost;
    if (!post) {
      throw new OperationError('抱歉! 该帖子已被删除! ');
    }
    const tid = post.nodeBBTopicId;
    if (!tid) {
      return post;
    }
    const data = await this.nodeBBService.getForumTopicByPid(tid, isAuthorized, uid);
    const forumTags = await this.forumTagService.findAllActiveForumTags(ctx);
    const topic = this.formatTopicDetails.call(this, data, forumTags);
    const topics = await this.mergeNodeBBTopicAndLocalData(ctx, [topic], [post], isAuthorized, uid);
    return topics[0];
  }

  // 提取出公共获取用户信息的方法
  async getUserInfo(ctx: RequestContext) {
    let isAuthorized = false;
    let uid = 0;
    if (ctx.apiType === 'admin') {
      return {isAuthorized, uid};
    }
    if (ctx.activeUserId) {
      try {
        const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
        if (forumCustomer) {
          isAuthorized = true;
          uid = forumCustomer.forumUid;
        }
      } catch (error) {
        Logger.debug('获取用户信息失败', error);
      }
    }
    return {isAuthorized, uid};
  }

  // 提取出获取tags并过滤的逻辑
  getTags(tags: {value: string}[], forumAllTags: ForumTag[]) {
    return tags
      ?.map(tag => {
        const tagValue = forumAllTags.find(item => item.tagHash === tag.value);
        return tagValue?.name ?? '';
      })
      .filter(Boolean);
  }

  async getTopicsIncludePinned(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    sortType: 'hot' | 'new' = 'hot',
    isWish = false,
    tagHash?: string,
  ) {
    const skip = options?.skip ?? 0;
    const take = options?.take ?? 10;
    const page = Utils.convertSkipAndTakeToPageAndPerPage(skip, take);
    const {isAuthorized, uid} = await this.getUserInfo(ctx);
    const [forumAllTags, topicsData] = await Promise.all([
      this.forumTagService.findAllActiveForumTags(ctx),
      this.nodeBBService.getTopicsIncludePinned(page, sortType, isAuthorized, isWish, uid, tagHash),
    ]);
    const topics = topicsData.topics;
    const totalItems = topicsData.totalItems;
    const newTopics = topics.map(topic => this.formatTopic.call(this, topic, forumAllTags));
    const items = await this.mergeNodeBBTopicAndLocalData(ctx, newTopics, [], isAuthorized, uid);
    return {
      items: items,
      totalItems,
    };
  }

  async getNewForumPosts(ctx: RequestContext, options: ListQueryOptions<ForumPost>, isWish = false, tagHash?: string) {
    const skip = options?.skip ?? 0;
    const take = options?.take ?? 10;
    const page = Utils.convertSkipAndTakeToPageAndPerPage(skip, take);
    const {isAuthorized, uid} = await this.getUserInfo(ctx);
    const [forumAllTags, topicsData] = await Promise.all([
      this.forumTagService.findAllActiveForumTags(ctx),
      this.nodeBBService.getNewTopics(page, isAuthorized, uid, tagHash),
    ]);
    const topics = topicsData.topics;
    const totalItems = topicsData.totalItems;
    const newTopics = topics.map(topic => this.formatTopic.call(this, topic, forumAllTags));
    const items = await this.mergeNodeBBTopicAndLocalData(ctx, newTopics, [], isAuthorized, uid);
    return {
      items: items,
      totalItems,
    };
  }

  //合并nodeBB和本地数据

  async mergeNodeBBTopicAndLocalData(
    ctx: RequestContext,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    topics: any[],
    forumPosts: ForumPost[] = [],
    isAuthorized = false,
    uid = 0,
  ) {
    // 根据topics的tid获取帖子的主图
    const topicIds = topics.map(topic => topic.tid);
    if (!topicIds.length) {
      return topics;
    }
    if (!forumPosts || forumPosts.length <= 0) {
      forumPosts = await this.connection.getRepository(ctx, ForumPost).find({
        where: {
          nodeBBTopicId: In(topicIds),
          deletedAt: IsNull(),
        },
        relations: ['forumCustomer', 'forumTags', 'products', 'forumActivities', 'forumWishPost'],
      });
    }
    let forumPostAllVisitors: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      pageId: any;
      visits: number;
      hits: number;
    }[] = [];
    if (ctx.apiType === 'admin') {
      forumPostAllVisitors = await this.interfaceForumMatomo.getCustomPageDataStatistics(
        ctx,
        new Date(0),
        new Date(),
        `ForumPost`,
      );
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const items: any[] = [];
    for (const item of topics) {
      const localData = forumPosts?.find(tag => idsAreEqual(tag.nodeBBTopicId, item.tid));
      // 如果localData不存在则需过滤
      if (!localData) {
        continue;
      }
      items.push({
        ...item,
        ...localData,
        mainImage: localData?.mainImage ?? '',
        images: localData?.images ?? [],
        type: localData?.type ?? ForumPostType.Short,
        isWish: localData?.isWish ?? false,
        id: localData?.id ?? '',
        createAt: localData?.createdAt,
        releaseTime: localData?.releaseTime,
        forumCustomer: localData?.forumCustomer,
        upVoted: item.upvoted ? true : false,
        uid: localData?.forumCustomer?.forumUid,
        postCount: item.postCount > 0 ? Number(item.postCount) - 1 : 0,
      });
    }
    if (items.length > 0) {
      items.forEach(item => {
        const forumPostVisitors = forumPostAllVisitors.find(visitor => idsAreEqual(visitor.pageId, item.id));
        if (forumPostVisitors) {
          item.visitorsCount = forumPostVisitors.visits;
          item.pageViews = forumPostVisitors.hits;
        } else {
          item.visitorsCount = 0;
          item.pageViews = 0;
        }
      });
    }
    if (isAuthorized) {
      const postIds = items.map(item => item.mainPid);
      if (postIds?.length > 0) {
        const upVoted = await this.nodeBBService.getVoteStatus(postIds, uid);
        // upVoted是一个数组 和postIds一一对应
        postIds.forEach((postId, index) => {
          // eslint-disable-next-line @typescript-eslint/no-shadow
          const item = items.find(item => idsAreEqual(item.mainPid, postId));
          if (item) {
            item.upVoted = upVoted[index];
          }
        });
      }
    }
    return items;
  }

  formatTopicDetails(topic: NodeBBForumTopic, forumAllTags: ForumTag[]) {
    const post = topic.posts.find(postObj => postObj.pid === topic.mainPid) as NodeBBForumPost;
    return {
      tid: topic.tid,
      downVotes: topic.downvotes,
      upVotes: topic.upvotes,
      votes: topic.votes,
      viewCount: topic.viewcount,
      mainPid: topic.mainPid,
      postCount: topic.postcount,
      title: topic.title,
      mainImage: ``,
      tags: this.getTags(topic.tags, forumAllTags),
      user: {
        uid: topic.uid,
        username: post.user.username,
        picture: post.user.picture,
      },
      content: post.content,
    };
  }

  formatTopic(topic: NodeBBForumTopic, forumAllTags: ForumTag[]) {
    return {
      tid: topic.tid,
      downVotes: topic.downvotes,
      upVotes: topic.upvotes,
      votes: topic.votes,
      viewCount: topic.viewcount,
      mainPid: topic.mainPid,
      postCount: topic.postcount,
      title: topic.title,
      mainImage: ``,
      tags: this.getTags(topic.tags, forumAllTags),
      user: {
        uid: topic.uid,
        username: topic.user.username,
        picture: topic.user.picture,
      },
      pinned: topic.pinned,
    };
  }

  async getHotForumPosts(
    ctx: RequestContext,
    options: ListQueryOptions<ForumPost>,
    forumPostHotDateType: ForumPostHotDateType,
    isWish = false,
    tagHash?: string,
  ) {
    const skip = options?.skip ?? 0;
    const take = options?.take ?? 10;
    const page = Utils.convertSkipAndTakeToPageAndPerPage(skip, take);
    const {isAuthorized, uid} = await this.getUserInfo(ctx);
    const [forumAllTags, topicsData] = await Promise.all([
      this.forumTagService.findAllActiveForumTags(ctx),
      this.nodeBBService.getHotTopics(page, forumPostHotDateType, isAuthorized, uid, isWish, tagHash),
    ]);
    const topics = topicsData.topics;
    const totalItems = topicsData.totalItems;
    const hotForumPosts = topics.map(topic => this.formatTopic.call(this, topic, forumAllTags));
    const items = await this.mergeNodeBBTopicAndLocalData(ctx, hotForumPosts, [], isAuthorized, uid);
    return {
      items: items,
      totalItems,
    };
  }
  async auditForumPost(ctx: RequestContext, input: ForumPostAuditInput) {
    const forumPostId = input.forumPostId;
    const lock = await this.redLockService.lockResource(`forumPost_Audit_${forumPostId}`);
    try {
      const forumPost = await this.findOne(ctx, forumPostId, {}, []);
      if (!forumPost) {
        throw new Error('帖子不存在');
      }
      if (forumPost.status !== ForumPostStatus.Pending) {
        throw new Error('传入参数不是待审核状态');
      }
      forumPost.auditTime = new Date();
      if (input.status === ForumPostAuditStatus.Pass) {
        forumPost.status = ForumPostStatus.Published;
        if (forumPost.forumActivities?.length < 0) {
          const tagsIds = forumPost.forumTags?.map(tag => tag.id);
          if (tagsIds?.length > 0) {
            const actives = await this.forumActivityService.getActivesByTags(ctx, tagsIds);
            if (actives.length > 0) {
              forumPost.forumActivities = actives;
            }
          }
        }
        // 审核通过后同步到nodeBB
        await this.syncForumPostToNodeBB(ctx, forumPost);
      } else {
        if (!input.refuseReason) {
          throw new Error('拒绝原因不能为空');
        }
        forumPost.status = ForumPostStatus.Refused;
        forumPost.refuseReason = input.refuseReason ?? '';
      }
      await this.connection.getRepository(ctx, ForumPost).save(forumPost);
      await this.cacheService.removeCache(
        forumPost.forumTags.map(tag => CacheKeyManagerService.forumTagPostCount(tag.id, ctx.channelId)),
      );
      if (forumPost.forumActivities?.length > 0) {
        await this.cacheService.removeCache(
          forumPost.forumActivities.map(forumActivity =>
            CacheKeyManagerService.forumActivityPostCount(forumActivity.id, ctx.channelId),
          ),
        );
      }
      return forumPost;
    } catch (error) {
      Logger.error(error);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }
  async syncForumPostToNodeBB(ctx: RequestContext, forumPost: ForumPost) {
    // 判断forumPost是否已经同步到nodeBB 已经同步过则不再同步
    if (forumPost.nodeBBTopicId && ctx.apiType === 'shop') {
      return;
    }
    let forumCustomer = forumPost.forumCustomer;
    if (!forumCustomer) {
      const forumCustomerId = forumPost.forumCustomerId as ID;
      forumCustomer = (await this.forumCustomerService.findOne(ctx, forumCustomerId, false, {}, [])) as ForumCustomer;
    }
    let tags = forumPost.forumTags?.map(tag => tag.tagHash) ?? [];
    if (forumPost.isWish) {
      tags.push('postwish');
    }
    const actives = await this.getActivesByTags(ctx, forumPost.forumTags);
    const activesHash = actives.map(active => active.tagHash);
    tags = tags.concat(activesHash);
    const cid = Number(process.env.NODE_BB_CID ?? 1);
    if (!cid) {
      throw new Error('nodeBB分类ID不存在');
    }
    if (forumPost.nodeBBPostId) {
      await this.nodeBBService.updatePost(
        forumPost.nodeBBPostId,
        forumPost.content,
        forumPost.title,
        tags,
        forumCustomer.forumUid,
      );
    } else {
      const nodeBBTopic = await this.nodeBBService.createTopic({
        title: forumPost.title,
        content: forumPost.content,
        tags,
        cid: cid,
        _uid: forumCustomer.forumUid,
      });
      forumPost.nodeBBTopicId = nodeBBTopic.tid;
      forumPost.nodeBBPostId = nodeBBTopic.mainPid;
      forumPost.releaseTime = new Date();
      await this.connection.getRepository(ctx, ForumPost).save(forumPost);
    }
    return forumPost;
  }
  async getActivesByTags(ctx: RequestContext, tags: ForumTag[]) {
    const tagsIds = tags?.map(tag => tag.id);
    const actives = await this.forumActivityService.getActivesByTags(ctx, tagsIds);
    return actives;
  }

  async upsertForumPost(ctx: RequestContext, input: ForumPostInput, isAdmin = false) {
    await this.validInput(ctx, input);
    const forumCustomerId = input.forumCustomerId;
    let forumCustomer: ForumCustomer;
    if (forumCustomerId) {
      forumCustomer = (await this.forumCustomerService.findOne(ctx, forumCustomerId)) as ForumCustomer;
    } else {
      forumCustomer = (await this.forumCustomerService.getOrCreateForumCustomer(ctx)) as ForumCustomer;
      if (!forumCustomer) {
        throw new UnauthorizedError();
      }
    }
    let mainImage = ``;
    if (input.type === ForumPostType.Long) {
      mainImage = input.mainImage ?? ``;
    } else if (input.type === ForumPostType.Short) {
      mainImage = input?.images?.[0] ?? '';
    }
    const wishPostId = input.wishPostId;
    let forumPostId: ID = input.id as ID;
    const tagIds = input.forumTagIds?.filter(tagId => !!tagId);
    const tags = await this.forumTagService.findByIds(ctx, tagIds as string[]);
    // 根据查询的tags获取活动
    const actives = await this.getActivesByTags(ctx, tags);
    const products = input.productIds?.filter(productId => !!productId)?.map(productId => ({id: productId} as Product));
    if (input.id) {
      const forumPost = await this.findOne(ctx, input.id, {}, []);
      if (!forumPost) {
        throw new Error('帖子不存在');
      }
      if (forumPost.status === ForumPostStatus.Pending) {
        throw new OperationError('该帖子正在审核中,不允许修改');
      }
      // 如果帖子已经发布则不允许修改
      if (ctx.apiType === 'shop' && (forumPost.status === ForumPostStatus.Published || forumPost.nodeBBTopicId)) {
        throw new OperationError('该帖子已发布,不允许修改');
      }
      if (!idsAreEqual(forumPost.forumCustomerId, forumCustomer.id)) {
        throw new OperationError('该帖子不是你创建的,无权限修改该帖子');
      }
      forumPost.title = input.title;
      forumPost.content = input.content;
      if (tags?.length > 0) {
        forumPost.forumTags = tags;
      }
      if (actives?.length > 0) {
        forumPost.forumActivities = actives;
      }
      forumPost.images = input.images as string[];
      forumPost.mainImage = mainImage;
      if (input.isPublish) {
        forumPost.status = ForumPostStatus.Pending;
        if (isAdmin) {
          forumPost.status = ForumPostStatus.Published;
        }
      } else {
        forumPost.status = ForumPostStatus.Draft;
      }
      forumPost.products = products as Product[];
      forumPost.wishPostId = wishPostId as ID;
      forumPost.shareTitle = input.shareTitle ?? '';
      forumPost.shareImg = input.shareImg ?? '';
      await this.connection.getRepository(ctx, ForumPost).save(forumPost);
    } else {
      let status = ForumPostStatus.Draft;
      if (input.isPublish) {
        status = ForumPostStatus.Pending;
        if (isAdmin) {
          status = ForumPostStatus.Published;
        }
      } else {
        status = ForumPostStatus.Draft;
      }
      let isWish = false;
      if (ctx.apiType === 'admin') {
        isWish = true;
      }
      let forumPost = new ForumPost({
        title: input.title,
        content: input.content,
        images: input.images as string[],
        mainImage: mainImage,
        status: status,
        forumCustomer: forumCustomer,
        forumTags: tags,
        forumActivities: actives,
        products,
        wishPostId,
        isWish,
        type: input.type,
        shareTitle: input.shareTitle ?? '',
        shareImg: input.shareImg ?? '',
      });
      forumPost = await this.channelService.assignToCurrentChannel(forumPost, ctx);
      await this.connection.getRepository(ctx, ForumPost).save(forumPost);
      forumPostId = forumPost.id;
    }
    await this.cacheService.removeCache(
      tags.map(tag => CacheKeyManagerService.forumTagPostCount(tag.id, ctx.channelId)),
    );
    if (actives?.length > 0) {
      await this.cacheService.removeCache(
        actives.map(active => CacheKeyManagerService.forumActivityPostCount(active.id, ctx.channelId)),
      );
    }
    if (input.isPublish && isAdmin) {
      const forumPost = (await this.findOne(ctx, forumPostId, {}, [])) as ForumPost;
      // 如果是管理员发布则直接同步到nodeBB
      await this.syncForumPostToNodeBB(ctx, forumPost);
      return forumPost;
    }
    return this.findOne(ctx, forumPostId, {}, []);
  }
  async validInput(ctx: RequestContext, input: ForumPostInput) {
    if (!input.title) {
      throw new OperationError('帖子标题不能为空');
    }
    if (input.title.length > 25) {
      throw new OperationError('帖子标题不能超过25个字符');
    }
    if (!input.content) {
      throw new OperationError('帖子内容不能为空');
    }
    if (input.type === ForumPostType.Short) {
      if (ctx.apiType === 'shop' && input.content.length > 1000) {
        throw new OperationError('帖子内容不能超过1000个字符');
      }
      if (!input.images || input.images.length <= 0) {
        throw new OperationError('帖子图片不能为空');
      }
      if (input.images.length > 9) {
        throw new OperationError('帖子图片不能超过9张');
      }
    } else {
      if (!input.mainImage || input.mainImage.length <= 0) {
        throw new OperationError('帖子主图不能为空');
      }
    }
  }

  findOne(
    ctx: RequestContext,
    forumPostId: ID,
    options?: ListQueryOptions<ForumPost>,
    relations?: RelationPaths<ForumPost>,
  ): Promise<ForumPost | null> {
    const qb = this.listQueryBuilder.build(ForumPost, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.forumCustomer`, 'forumCustomer');
    qb.leftJoinAndSelect(`${qb.alias}.forumTags`, 'forumTags');
    qb.leftJoinAndSelect(`${qb.alias}.products`, 'products');
    qb.leftJoinAndSelect(`${qb.alias}.forumActivities`, 'forumActivities');
    qb.leftJoinAndSelect(`${qb.alias}.forumWishPost`, 'forumWishPost');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.id = :forumPostId`, {forumPostId});
    return qb.take(1).getOne();
  }

  async findAll(
    ctx: RequestContext,
    forumTagId?: ID,
    forumActivityId?: ID,
    options?: ListQueryOptions<ForumPost>,
    relations?: RelationPaths<ForumPost>,
  ) {
    const qb = this.listQueryBuilder.build(ForumPost, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });

    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`${qb.alias}.forumCustomer`, 'forumCustomer');
    qb.leftJoinAndSelect(`${qb.alias}.forumTags`, 'forumTags');
    qb.leftJoinAndSelect(`${qb.alias}.products`, 'products');
    qb.leftJoinAndSelect(`${qb.alias}.forumActivities`, 'forumActivities');
    qb.leftJoinAndSelect(`${qb.alias}.forumWishPost`, 'forumWishPost');
    if (forumTagId) {
      qb.andWhere(`forumTags.id = :forumTagId`, {forumTagId});
    }
    if (forumActivityId) {
      qb.andWhere(`forumActivities.id = :forumActivityId`, {forumActivityId});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    if (items.length > 0 && ctx.apiType === 'admin') {
      const forumPostAllVisitors = await this.interfaceForumMatomo.getCustomPageDataStatistics(
        ctx,
        new Date(0),
        new Date(),
        `ForumPost`,
      );
      items.forEach(item => {
        const forumPostVisitors = forumPostAllVisitors.find(visitor => idsAreEqual(visitor.pageId, item.id));
        if (forumPostVisitors) {
          item.visitorsCount = forumPostVisitors.visits;
          item.pageViews = forumPostVisitors.hits;
        } else {
          item.visitorsCount = 0;
          item.pageViews = 0;
        }
      });
    }
    return {
      items,
      totalItems,
    };
  }

  // 小程序搜索帖子列表，（获取用户是否点赞数据
  async searchForumPosts(
    ctx: RequestContext,
    forumTagId?: ID,
    forumActivityId?: ID,
    options?: ListQueryOptions<ForumPost>,
    relations?: RelationPaths<ForumPost>,
  ) {
    const {items, totalItems} = await this.findAll(ctx, forumTagId, forumActivityId, options, relations);
    const {isAuthorized, uid} = await this.getUserInfo(ctx);
    const forumTags = await this.forumTagService.findAllActiveForumTags(ctx);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const topics: any[] = [];
    // for (const i of items) {
    //   const tid = i.nodeBBTopicId;
    //   if (!tid) {
    //   }
    //   const data = await this.nodeBBService.getForumTopicByPid(tid, isAuthorized, uid);
    //   const topic = this.formatTopicDetails.call(this, data, forumTags);
    //   topics.push(topic);
    // }
    const getCurPageTopics = await Promise.all(
      items.map(i => {
        const tid = i.nodeBBTopicId;
        return this.nodeBBService.getForumTopicByPid(tid, isAuthorized, uid);
      }),
    );
    for (const t of getCurPageTopics) {
      const topic = this.formatTopicDetails.call(this, t, forumTags);
      topics.push(topic);
    }
    const resItem = await this.mergeNodeBBTopicAndLocalData(ctx, topics, items, isAuthorized, uid);
    return {
      items: resItem,
      totalItems,
    };
  }

  async getUnAuditForumPosts(ctx: RequestContext, options?: ListQueryOptions<ForumPost>) {
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new Error('用户不存在');
    }
    const qb = this.listQueryBuilder.build(ForumPost, options, {
      ctx,
      relations: ['forumTags', 'products', 'forumActivities', 'forumWishPost'],
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.forumCustomer`, 'forumCustomer');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    // qb.andWhere(`${qb.alias}.status in (:...status)`, {status: [ForumPostStatus.Pending, ForumPostStatus.Refused]});
    qb.andWhere('forumCustomer.id = :forumCustomerId', {forumCustomerId: forumCustomer.id});
    const [items, totalItems] = await qb.getManyAndCount();
    items.forEach(item => {
      const forumTags = item.forumTags;
      if (forumTags) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).tags = forumTags.map(tag => tag.name);
      }
    });
    return {
      items,
      totalItems,
    };
  }

  async getMyForumPosts(ctx: RequestContext, options: ListQueryOptions<ForumPost>) {
    const skip = options?.skip ?? 0;
    const take = options?.take ?? 10;
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new UnauthorizedError();
    }
    const page = Utils.convertSkipAndTakeToPageAndPerPage(skip, take);
    const [forumAllTags, topicsData] = await Promise.all([
      this.forumTagService.findAllActiveForumTags(ctx),
      this.nodeBBService.getMyForumPost(page, forumCustomer.userSlug),
    ]);
    const {topics, totalItems} = topicsData;
    const hotForumPosts = topics.map(topic => this.formatTopic.call(this, topic, forumAllTags));
    const items = await this.mergeNodeBBTopicAndLocalData(ctx, hotForumPosts, [], true, forumCustomer.forumUid);
    return {
      items: items,
      totalItems,
    };
  }

  async getMyForumPostCount(ctx: RequestContext) {
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new UnauthorizedError();
    }
    // 获取评论和点赞数
    const result: {
      topiccount: number;
      postcount: number;
      reputation: number;
    } = await this.nodeBBService.getMyForumPostCount(forumCustomer.forumUid);
    return {
      topicCount: result.topiccount,
      postCount: Number(result.postcount ?? 0) - Number(result.topiccount ?? 0),
      reputation: result.reputation,
    };
  }

  async getForumPostByNodeBBPostId(ctx: RequestContext, nodeBBPostId: number) {
    const forumPost = await this.connection.getRepository(ctx, ForumPost).findOne({
      where: {
        nodeBBPostId: nodeBBPostId,
        deletedAt: IsNull(),
      },
    });
    if (!forumPost) {
      const review = await this.connection.getRepository(ctx, ForumReview).findOne({
        where: {
          postId: nodeBBPostId,
        },
      });
      if (!review) {
        return new OperationError('帖子或者评论不存在');
      }
      return review;
    }
    return forumPost;
  }
}
