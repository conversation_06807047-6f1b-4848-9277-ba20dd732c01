import {Injectable} from '@nestjs/common';
import {CacheService, CacheKeyManagerService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {
  ChannelService,
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UnauthorizedError,
} from '@vendure/core';
import {ForumActivity, ForumPost, ForumReplyNotification, ForumReview, ForumTag} from '../entities';
import {OperationError} from '../error.type';
import {
  DeletionResult,
  ForumActivityStatus,
  ForumPostAuditStatus,
  ForumReviewAuditInput,
  ForumReviewAuditInBatchInput,
  ForumReviewStatus,
  NotificationType,
  Success,
} from '../generated-admin-types';
import {NodeBBForumPost} from '../node-bb.types';
import {ForumCustomerService} from './forum-customer.service';
import {NodeBBApiClient} from './forum-node-bb-api.client';
import {ForumNotificationService} from './forum-notification.service';
import {ForumPostService} from './forum-post.service';
import {NodeBBService} from './node-bb.service';
@Injectable()
export class ForumReviewService {
  async getForumReviewCount(ctx: RequestContext, forumReview: ForumReview) {
    const qb = this.connection.getRepository(ctx, ForumReview).createQueryBuilder('forumReview');
    qb.andWhere('forumReview.topicId = :topicId', {topicId: forumReview.topicId});
    qb.andWhere('forumReview.toPostId = :toPostId', {toPostId: forumReview.postId});
    qb.andWhere('forumReview.status = :status', {status: ForumReviewStatus.Pass});
    qb.andWhere('forumReview.deletedAt IS NULL');
    return qb.getCount();
  }
  constructor(
    private nodeBBApiClient: NodeBBApiClient,
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private nodeBBService: NodeBBService,
    private redLockService: RedLockService,
    private forumPostService: ForumPostService,
    private forumCustomerService: ForumCustomerService,
    private forumNotificationService: ForumNotificationService,
    private cacheService: CacheService,
  ) {}

  async findAll(ctx: RequestContext, options: ListQueryOptions<ForumReview>, relations: RelationPaths<ForumReview>) {
    const qb = this.listQueryBuilder.build(ForumReview, options, {
      relations,
      ctx,
    });
    qb.leftJoinAndSelect(`${qb.alias}.forumCustomer`, 'forumCustomer');
    qb.leftJoinAndSelect(`${qb.alias}.parentForumCustomer`, 'parentForumCustomer');
    qb.leftJoinAndSelect(`${qb.alias}.forumPost`, 'forumPost');
    qb.andWhere('forumReview.deletedAt IS NULL');
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  async findOne(ctx: RequestContext, id: ID) {
    const forumReviewQb = this.connection.getRepository(ctx, ForumReview).createQueryBuilder('forumReview');
    forumReviewQb.leftJoinAndSelect('forumReview.forumCustomer', 'forumCustomer');
    forumReviewQb.leftJoinAndSelect('forumReview.parentForumCustomer', 'parentForumCustomer');
    forumReviewQb.leftJoinAndSelect('forumReview.forumPost', 'forumPost');
    forumReviewQb.leftJoinAndSelect('forumPost.forumCustomer', 'forumPostCustomer');
    forumReviewQb.andWhere('forumReview.id = :id', {id});
    forumReviewQb.andWhere('forumReview.deletedAt IS NULL');
    return forumReviewQb.take(1).getOne();
  }

  async deleteForumReview(ctx: RequestContext, forumReviewId: ID) {
    const forumReview = await this.findOne(ctx, forumReviewId);
    if (!forumReview) {
      throw new Error('评论不存在');
    }
    await this.connection.getRepository(ctx, ForumReview).update(
      {
        id: forumReviewId,
      },
      {
        deletedAt: new Date(),
      },
    );
    return {
      result: DeletionResult.Deleted,
      message: 'success',
    };
  }

  async reviewForumAudit(ctx: RequestContext, input: ForumReviewAuditInput) {
    const forumReviewId = input.forumReviewId;
    const lock = await this.redLockService.lockResource(`forumReview_Audit_${forumReviewId}`);
    try {
      const forumReview = await this.findOne(ctx, forumReviewId);
      if (!forumReview) {
        throw new Error('评论不存在');
      }
      if (forumReview.status !== ForumReviewStatus.Pending) {
        throw new Error('评论状态不是待审核状态');
      }
      forumReview.auditTime = new Date();
      if (input.status === ForumPostAuditStatus.Pass) {
        forumReview.status = ForumReviewStatus.Pass;
        await this.forumReviewPostPublishNodeBB(ctx, forumReview);
      } else {
        if (!input.refuseReason) {
          throw new Error('拒绝原因不能为空');
        }
        forumReview.status = ForumReviewStatus.Refuse;
        forumReview.refuseReason = input.refuseReason ?? '';
      }
      await this.connection.getRepository(ctx, ForumReview).save(forumReview);
      return forumReview;
    } catch (e) {
      Logger.error(e);
      throw e;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async reviewForumAuditInBatch(ctx: RequestContext, input: ForumReviewAuditInBatchInput): Promise<Success> {
    const res = {success: false};
    const {status, refuseReason} = input;
    try {
      if (!input.forumReviewIds?.length) {
        throw new Error('查询id为空');
      }
      for (const i of input.forumReviewIds) {
        const f = i as string;
        await this.reviewForumAudit(ctx, {forumReviewId: f, status, refuseReason});
      }
      res.success = true;
      return res;
    } catch (e) {
      Logger.error(e);
      throw e;
    }
  }

  async forumReviewPost(ctx: RequestContext, id: ID, forumPostId: ID, content: string, images: string[]) {
    this.reviewValid(ctx, content, images);
    let postId = 0;
    let topicId = 0;
    let parentForumCustomerId = 0 as ID;
    let level = 0;
    let parentPostId = 0;
    if (id) {
      const reviewPost = await this.connection
        .getRepository(ctx, ForumReview)
        .createQueryBuilder('forumReview')
        .where('forumReview.id = :id', {id})
        .getOne();
      if (!reviewPost) {
        throw new Error('评论不存在');
      }
      postId = reviewPost.postId;
      if (!postId) {
        throw new OperationError('评论的帖子未审核通过');
      }
      topicId = reviewPost.topicId;
      parentForumCustomerId = reviewPost.forumCustomerId as ID;
      level = reviewPost.level + 1;
      forumPostId = reviewPost.forumPostId as ID;
      if (reviewPost.level === 0) {
        parentPostId = reviewPost.postId;
      } else {
        parentPostId = reviewPost.parentPostId;
      }
    } else {
      if (!forumPostId) {
        throw new Error('评论的帖子id不存在');
      }
      const forumPost = await this.forumPostService.findOne(ctx, forumPostId);
      if (!forumPost) {
        throw new Error('帖子不存在');
      }
      topicId = forumPost.nodeBBTopicId;
      postId = forumPost.nodeBBPostId;
      parentForumCustomerId = forumPost.forumCustomerId as ID;
      level = 0;
    }

    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (!forumCustomer) {
      throw new UnauthorizedError();
    }
    let reviewStatus = ForumReviewStatus.Pending;
    if (process.env.REVIEW_NEED_AUDIT === 'true') {
      reviewStatus = ForumReviewStatus.Pending;
    } else {
      reviewStatus = ForumReviewStatus.Pass;
    }
    let forumReview = new ForumReview({
      content: content,
      images: images,
      status: reviewStatus,
      forumCustomer: forumCustomer,
      reviewTime: new Date(),
      forumPostId: forumPostId,
      toPostId: postId ? postId : null,
      topicId: topicId ? topicId : null,
      level: level,
      parentPostId: parentPostId ? parentPostId : null,
      parentForumCustomerId: parentForumCustomerId ? parentForumCustomerId : null,
    });
    forumReview = await this.channelService.assignToCurrentChannel(forumReview, ctx);
    forumReview = await this.connection.getRepository(ctx, ForumReview).save(forumReview);
    if (reviewStatus === ForumReviewStatus.Pass) {
      await this.forumReviewPostPublishNodeBB(ctx, forumReview);
    }
    return this.findOne(ctx, forumReview.id);
  }
  reviewValid(ctx: RequestContext, content: string, images: string[]) {
    if (!content) {
      throw new Error('评论内容不能为空');
    }
    if (content.length > 300) {
      throw new Error('评论内容不能超过300个字符');
    }
    if (images.length > 3) {
      throw new Error('评论图片不能超过3张');
    }
  }

  async forumReviewPostPublishNodeBB(ctx: RequestContext, forumReview: ForumReview) {
    const post = await this.nodeBBService.postPost(
      forumReview.topicId,
      forumReview.toPostId,
      forumReview.content,
      forumReview.forumCustomer.forumUid,
    );
    // await this.nodeBBService.replyNotification(post.response, forumReview.forumCustomer.forumUid);
    await this.replyNotification(ctx, post.response, forumReview);
    const nodeBBForumPost = post.response;
    forumReview.postId = nodeBBForumPost.pid;
    forumReview.topicId = nodeBBForumPost.tid;
    await this.connection.getRepository(ctx, ForumReview).save(forumReview);
    await this.markPostCount(ctx, forumReview, 'add');
    return post;
  }
  async markPostCount(ctx: RequestContext, forumReview: ForumReview, postType: 'add' | 'delete') {
    const forumPost = await this.connection.getRepository(ctx, ForumPost).findOne({
      where: {
        id: forumReview.forumPostId,
      },
      relations: ['forumCustomer', 'forumTags', 'forumActivities'],
    });
    if (forumPost?.forumTags?.length) {
      const tagId = forumPost.forumTags[0].id;
      if (tagId) {
        await this.markPostCountByTag(ctx, tagId, postType);
      }
    }
    if (forumPost?.forumActivities?.length) {
      if (forumPost.forumActivities?.[0].status === ForumActivityStatus.Normal) {
        const activityId = forumPost.forumActivities[0].id;
        if (activityId) {
          await this.markPostCountByActivity(ctx, activityId, postType);
        }
      }
    }
  }
  async markPostCountByActivity(ctx: RequestContext, forumActivityId: ID, postType: 'add' | 'delete') {
    await this.connection.getRepository(ctx, ForumActivity).update(
      {
        id: forumActivityId,
      },
      // 如果是评论，totalPostCount + 1，如果是删除评论，totalPostCount - 1
      {
        totalPostCount: () => (postType === 'add' ? 'totalPostCount + 1' : 'totalPostCount - 1'),
      },
    );
  }
  async markPostCountByTag(ctx: RequestContext, tagId: ID, postType: 'add' | 'delete') {
    await this.connection.getRepository(ctx, ForumTag).update(
      {
        id: tagId,
      },
      {
        totalPostCount: () => (postType === 'add' ? 'totalPostCount + 1' : 'totalPostCount - 1'),
      },
    );
    await this.cacheService.removeCache(CacheKeyManagerService.forumActiveTagsAll(ctx.channelId));
  }
  async replyNotification(ctx: RequestContext, response: NodeBBForumPost, forumReview: ForumReview) {
    let forumReplyNotification = new ForumReplyNotification({
      replyPostId: forumReview.toPostId,
      replyForumCustomerId: forumReview.parentForumCustomerId,
      forumReviewId: forumReview.id,
      forumPostId: forumReview.forumPostId,
    });
    forumReplyNotification = await this.channelService.assignToCurrentChannel(forumReplyNotification, ctx);
    await this.connection.getRepository(ctx, ForumReplyNotification).save(forumReplyNotification);
    await this.forumNotificationService.changeCommentNotificationStatus(
      ctx,
      forumReview.parentForumCustomerId as ID,
      NotificationType.Reply,
      false,
    );
  }
  async getForumReviewsByPid(ctx: RequestContext, pid: string, level: number, options: ListQueryOptions<ForumReview>) {
    // const {isAuthorized, uid} = await this.getUserInfo(ctx);
    // const reviews = await this.nodeBBService.getForumReviewsByPid(pid, isAuthorized, uid);
    // return reviews?.map(review => this.forumReviews(review));
    const qb = this.listQueryBuilder.build(ForumReview, options, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.forumCustomer`, 'forumCustomer');

    qb.andWhere(`${qb.alias}.status = :status`, {status: ForumReviewStatus.Pass});
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`${qb.alias}.parentForumCustomer`, 'parentForumCustomer');
    if (level > 0) {
      qb.andWhere(`${qb.alias}.level > 0`);
      qb.andWhere(`${qb.alias}.parentPostId = :pid`, {pid});
    } else {
      qb.andWhere(`${qb.alias}.level = 0`);
      qb.andWhere(`${qb.alias}.toPostId = :pid`, {pid});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    const postIds = items.map(item => item.postId);
    const forumCustomer = await this.forumCustomerService.getOrCreateForumCustomer(ctx);
    if (postIds?.length > 0 && forumCustomer) {
      const upVoted = await this.nodeBBService.getVoteStatus(postIds, forumCustomer.forumUid);
      // upVoted 的顺序和 postIds 一致
      items.forEach((item, index) => {
        item.upVoted = upVoted[index];
      });
    }

    return {
      items,
      totalItems,
    };
  }
}
