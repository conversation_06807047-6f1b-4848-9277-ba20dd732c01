import {Allow, Ctx, ID, ProductService, RequestContext, Transaction, TransactionalConnection} from '@vendure/core';
import {SubscriptionPlanService} from '../service/subscription-plan.service';

import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {CacheService} from '@scmally/ecommerce-common';
import {SubscriptionPlan} from '../entities';
import {UpsertSubscriptionPlanInput} from '../generated-shop-types';
import {SubscriptionPlanOperate, SubscriptionPlanRead} from '../permission-definition';
import {CacheKeyManagerService} from '@scmally/kvs';

@Resolver('SubscriptionPlanAdminResolver')
export class SubscriptionPlanAdminResolver {
  constructor(
    private connection: TransactionalConnection,
    private subscriptionPlanService: SubscriptionPlanService,
    private productService: ProductService,
    private cacheService: CacheService,
  ) {}

  @Allow(SubscriptionPlanRead.Permission)
  @Query()
  async subscriptionPlan(@Ctx() ctx: RequestContext): Promise<SubscriptionPlan[]> {
    return this.subscriptionPlanService.getPlans(ctx);
  }

  @Allow(SubscriptionPlanOperate.Permission)
  @Mutation()
  @Transaction()
  async upsertSubscriptionPlan(
    @Ctx() ctx: RequestContext,
    @Args('input') input: UpsertSubscriptionPlanInput,
    @Args('productId') productId: String,
  ): Promise<SubscriptionPlan> {
    this.subscriptionPlanService.validate(input);
    const plan = await this.subscriptionPlanService.upsert(ctx, input);
    if (productId) {
      const product = await this.productService.findOne(ctx, productId as ID);
      if (product)
        await this.productService.update(ctx, {
          id: product.id as ID,
          customFields: {
            subscriptionPlanId: plan.id,
          },
        });
      await this.cacheService.removeCache(
        CacheKeyManagerService.productSubscriptionPlan(productId as ID, ctx.channelId),
      );
    }
    return plan;
  }

  @Allow(SubscriptionPlanOperate.Permission)
  @Mutation()
  @Transaction()
  async deleteSubscriptionPlan(@Ctx() ctx: RequestContext, @Args('productId') productId: string): Promise<void> {
    const product = await this.productService.findOne(ctx, productId as ID);
    if (product) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const customFields: any = product.customFields;
      if (customFields.subscriptionPlan) {
        await this.productService.update(ctx, {
          id: product.id as ID,
          customFields: {
            subscriptionPlan: null,
          },
        });
        const planId = customFields.subscriptionPlan.id;
        await this.subscriptionPlanService.delete(ctx, planId);
        await this.cacheService.removeCache(
          CacheKeyManagerService.productSubscriptionPlan(productId as ID, ctx.channelId),
        );
      }
      return;
    }
  }
}
