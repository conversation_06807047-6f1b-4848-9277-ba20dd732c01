import {Inject, Injectable, forwardRef} from '@nestjs/common';
import {
  AbstractSubscriptionOrder,
  BlindBoxActivityService,
  BlindBoxOrderService,
  CacheService,
  CustomerProductService,
  CustomerProductVariantService,
  CustomerSourceService,
  FloatingWindowService,
  InterfaceCommonCustomer,
  OrderCustomCommonService,
  OrderPromotionResult,
  OrderPromotionResultService,
  OrderTrackingService,
  PointsProductService,
  PointsService,
  ProductRestrictionsService,
} from '@scmally/ecommerce-common';
import {CacheKeyManagerService, KvsService} from '@scmally/kvs';
import {InterfaceCustomer, MemberService} from '@scmally/member';
import {RedLockService} from '@scmally/red-lock';
import {WeChatPaymentService} from '@scmally/wechat';
import {InterfaceOrder} from '@scmally/wechat/dist/service/interface-order';
import {
  CreateAddressInput,
  LanguageCode,
  OrderListOptions,
  RefundOrderInput,
} from '@vendure/common/lib/generated-types';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {summate} from '@vendure/common/lib/shared-utils';
import {
  Channel,
  ChannelService,
  ConfigService,
  CountryService,
  Customer,
  CustomerService,
  ErrorResultUnion,
  EventBus,
  Fulfillment,
  ID,
  InternalServerError,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  MultipleOrderError,
  NothingToRefundError,
  Order,
  OrderLine,
  OrderModificationError,
  OrderService,
  OrderState,
  PaginatedList,
  Payment,
  PaymentMethod,
  PaymentMethodHandler,
  PaymentMethodService,
  PaymentOrderMismatchError,
  ProductVariantService,
  Refund,
  RefundOrderStateError,
  RefundStateTransitionError,
  RefundStateTransitionEvent,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  RequestContextService,
  TaxCategory,
  Transaction,
  TransactionalConnection,
  TranslatorService,
  UnauthorizedError,
  getOrdersFromLines,
  idsAreEqual,
  manualFulfillmentHandler,
} from '@vendure/core';
import {RefundStateMachine} from '@vendure/core/dist/service/helpers/refund-state-machine/refund-state-machine';
import {DateTime} from 'luxon';
import {FindOptionsUtils} from 'typeorm';
import {OrderRefund} from '../entities';
import {ShoppingCartUpdateEvent, UpdateOrderAddressEvent} from '../event';
import {OrderLineCustomFields, PurchasePattern, RefundOrderResult} from '../generated-admin-types';
import {
  ChannelCustomFields,
  DeletionResponse,
  DeletionResult,
  OrderCustomFields,
  OrderPurchaseType,
  RefundOrderCustom,
  RefundState,
  SettlementProduct,
} from '../generated-shop-types';
import {InterfaceComp} from './abstract-comp';

@Injectable()
export class OrderCustomService
  extends AbstractSubscriptionOrder
  implements InterfaceCustomer, InterfaceCommonCustomer, InterfaceOrder
{
  public interfaceComp: InterfaceComp;

  constructor(
    private customerProductService: CustomerProductService,
    private customerProductVariantService: CustomerProductVariantService,
    private countryService: CountryService,
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private orderService: OrderService,
    private kvsService: KvsService,
    private customerService: CustomerService,
    private channelService: ChannelService,
    private refundStateMachine: RefundStateMachine,
    private paymentMethodService: PaymentMethodService,
    private requestContextService: RequestContextService,
    private eventBus: EventBus,
    private translator: TranslatorService,
    private productVariantService: ProductVariantService,
    private requestCache: RequestContextCacheService,
    private requestContextCacheService: RequestContextCacheService,
    private orderPromotionResultService: OrderPromotionResultService,
    public orderCustomCommonService: OrderCustomCommonService,
    private orderTrackingService: OrderTrackingService,
    private memberService: MemberService,
    private cacheService: CacheService,
    private configService: ConfigService,
    private weChatPaymentService: WeChatPaymentService,
    @Inject(forwardRef(() => PointsProductService))
    private pointsProductService: PointsProductService,
    @Inject(forwardRef(() => PointsService))
    private pointsService: PointsService,
    private redLockService: RedLockService,
    private productRestrictionsService: ProductRestrictionsService,
    private blindBoxOrderService: BlindBoxOrderService,
    private customerSourceService: CustomerSourceService,
    private blindBoxActivityService: BlindBoxActivityService,
    private floatingWindowService: FloatingWindowService,
  ) {
    super(orderCustomCommonService);
    this.memberService.registerCustomer(this);
    this.customerProductService.registerCustomer(this);
    this.weChatPaymentService.registerOrder(this);
    this.pointsProductService.registerCustomer(this);
    this.pointsService.registerCustomer(this);
    this.blindBoxOrderService.registerCustomer(this);
    this.customerSourceService.registerCustomer(this);
    this.blindBoxActivityService.registerCustomer(this);
    this.floatingWindowService.registerCustomer(this);
  }

  registerComp(interfaceComp: InterfaceComp) {
    this.interfaceComp = interfaceComp;
  }

  async getShoppingCart(
    ctx: RequestContext,
    isRemoveMarkUp?: boolean,
    isUseMember?: boolean,
    isUseShoppingCredit?: boolean,
  ) {
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }
    const orders = await this.getActiveOrders(ctx, [
      OrderPurchaseType.ShoppingTrolley,
      OrderPurchaseType.OutrightPurchase,
    ]);
    let outrightPurchase = orders.outrightPurchase;
    let shoppingTrolley = orders.shoppingTrolley;
    // 购物车订单是否需要重新计算价格
    let isNeedReCalculatePriceShoppingTrolley = false;
    // 预下单订单是否需要重新计算价格
    let isNeedReCalculatePriceOutrightPurchase = false;
    if (isUseMember) {
      if (outrightPurchase) {
        const isNeedPreprocess = await this.orderPromotionResultService.applyMember(ctx, outrightPurchase, false);
        if (isNeedPreprocess) {
          isNeedReCalculatePriceOutrightPurchase = true;
        }
      }
      if (shoppingTrolley) {
        const isNeedPreprocess = await this.orderPromotionResultService.applyMember(ctx, shoppingTrolley, false);
        if (isNeedPreprocess) {
          isNeedReCalculatePriceShoppingTrolley = true;
        }
      }
    }
    if (isUseShoppingCredit && outrightPurchase) {
      // 购物金和会员取消选择逻辑一致  如果需要重新应用会员时也需重新应用购物金
      const isNeedPreprocessShoppingCredits = await this.orderPromotionResultService.applyShoppingCredits(
        ctx,
        outrightPurchase,
        false,
      );
      if (isNeedPreprocessShoppingCredits) {
        isNeedReCalculatePriceOutrightPurchase = true;
      }
    }
    if (outrightPurchase) {
      // 检查订单中的商品是否存在或者是否删除
      const checkDeleteResult = await this.orderPromotionResultService.checkDeleteProduct(ctx, outrightPurchase, false);
      if (checkDeleteResult?.order) {
        outrightPurchase = checkDeleteResult?.order;
      }
      if (checkDeleteResult?.recalculateOrNot) {
        isNeedReCalculatePriceOutrightPurchase = true;
      }
    }
    // 检查购物车订单中的商品是否存在或者是否删除
    if (shoppingTrolley) {
      const checkDeleteResult = await this.orderPromotionResultService.checkDeleteProduct(ctx, shoppingTrolley, false);
      if (checkDeleteResult?.order) {
        shoppingTrolley = checkDeleteResult?.order;
      }
      if (checkDeleteResult?.recalculateOrNot) {
        isNeedReCalculatePriceShoppingTrolley = true;
      }
    }
    if (outrightPurchase) {
      const checkOrderLineProduct = await this.checkOrderLineProduct(ctx, outrightPurchase, false);
      if (checkOrderLineProduct?.order) {
        outrightPurchase = checkOrderLineProduct?.order;
      }
      if (checkOrderLineProduct?.recalculateOrNot) {
        isNeedReCalculatePriceOutrightPurchase = true;
      }
      if (isRemoveMarkUp) {
        const result = await this.orderPromotionResultService.cancelAllMarkUp(
          ctx,
          outrightPurchase.id,
          outrightPurchase,
          false,
        );
        const cancelOrder = result?.order;
        if (result?.recalculateOrNot) {
          isNeedReCalculatePriceOutrightPurchase = true;
        }
        if (cancelOrder) {
          outrightPurchase = cancelOrder;
        }
      }
      const checkInventoryResult = await this.orderPromotionResultService.checkInventory(ctx, outrightPurchase, false);
      if (checkInventoryResult?.recalculateOrNot) {
        isNeedReCalculatePriceOutrightPurchase = true;
      }
      if (checkInventoryResult?.order) {
        outrightPurchase = checkInventoryResult.order;
      }
      if (outrightPurchase) {
        const verificationPriceResult = await this.verificationPrice(ctx, outrightPurchase, false);
        if (verificationPriceResult?.recalculateOrNot) {
          isNeedReCalculatePriceOutrightPurchase = true;
        }
        if (verificationPriceResult?.order) {
          outrightPurchase = verificationPriceResult.order;
        }
      }
      if (outrightPurchase) {
        const checkOrderLimitationResult = await this.orderPromotionResultService.checkOrderLimitation(
          ctx,
          outrightPurchase,
          userId,
          false,
        );
        if (checkOrderLimitationResult?.recalculateOrNot) {
          isNeedReCalculatePriceOutrightPurchase = true;
        }
        if (checkOrderLimitationResult?.order) {
          outrightPurchase = checkOrderLimitationResult.order;
        }
      }
      if (outrightPurchase) {
        const cancelAllPutOnSaleResult = await this.orderPromotionResultService.cancelAllPutOnSale(
          ctx,
          outrightPurchase,
          false,
        );
        if (cancelAllPutOnSaleResult?.recalculateOrNot) {
          isNeedReCalculatePriceOutrightPurchase = true;
        }
        if (cancelAllPutOnSaleResult?.order) {
          outrightPurchase = cancelAllPutOnSaleResult.order;
        }
      }
    }
    if (shoppingTrolley) {
      const checkOrderLineProduct = await this.checkOrderLineProduct(ctx, shoppingTrolley, false);
      if (checkOrderLineProduct?.order) {
        shoppingTrolley = checkOrderLineProduct?.order;
      }
      if (checkOrderLineProduct?.recalculateOrNot) {
        isNeedReCalculatePriceShoppingTrolley = true;
      }
      const verifyShoppingCartOrderResult = await this.verifyShoppingCartOrderByPreOrder(
        ctx,
        shoppingTrolley,
        outrightPurchase,
        false,
      );
      if (verifyShoppingCartOrderResult?.recalculateOrNot) {
        isNeedReCalculatePriceShoppingTrolley = true;
      }
      if (verifyShoppingCartOrderResult?.order) {
        shoppingTrolley = verifyShoppingCartOrderResult.order;
      }
      const verificationPriceResult = await this.verificationPrice(ctx, shoppingTrolley);
      if (verificationPriceResult?.recalculateOrNot) {
        isNeedReCalculatePriceShoppingTrolley = true;
      }
      if (verificationPriceResult?.order) {
        shoppingTrolley = verificationPriceResult.order;
      }
    }
    if (isNeedReCalculatePriceShoppingTrolley) {
      shoppingTrolley = await this.orderPromotionResultService.applyPriceAdjustments(ctx, shoppingTrolley as Order);
    }
    if (isNeedReCalculatePriceOutrightPurchase) {
      outrightPurchase = await this.orderPromotionResultService.applyPriceAdjustments(ctx, outrightPurchase as Order);
    }
    return {shoppingTrolley, outrightPurchase};
  }

  async activeOrder(ctx: RequestContext, type: OrderPurchaseType) {
    const orders = await this.getActiveOrders(ctx, [type]);
    if (type === OrderPurchaseType.ShoppingTrolley) {
      if (orders.shoppingTrolley) return orders.shoppingTrolley;
    } else if (type === OrderPurchaseType.OutrightPurchase) {
      if (orders.outrightPurchase) return orders.outrightPurchase;
    } else if (type === OrderPurchaseType.RegularOrder) {
      if (orders.regularOrder) return orders.regularOrder;
    }
    throw new Error('order not exist');
  }

  async getActiveOrders(ctx: RequestContext, types: OrderPurchaseType[]) {
    const userId = ctx.activeUserId;
    const getActiveOrdersLock = await this.redLockService.lockResource(`Order:getActiveOrders:${userId}`);
    try {
      if (!userId) {
        throw new UnauthorizedError();
      }
      const effectiveRelations = [
        'lines',
        'lines.productVariant',
        // 'lines.productVariant.product',
        // 'lines.productVariant.taxCategory',
        // 'lines.productVariant.productVariantPrices',
        // 'lines.productVariant.translations',
        // 'lines.featuredAsset',
        // 'lines.taxCategory',
        'shippingLines',
        // 'surcharges',
      ];
      const qb = this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.customer', 'customer')
        .leftJoin('customer.user', 'user')
        .where('order.customFields.purchaseType IN (:...types)', {types})
        .andWhere('active = 1')
        .andWhere('user.id =:userId', {userId: ctx.activeUserId});
      qb.setFindOptions({relations: effectiveRelations})
        .leftJoin('order.channels', 'channel')
        .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
      // FindOptionsUtils.joinEagerRelations(qb, qb.alias, qb.expressionMap.mainAlias!.metadata);
      const orders = await qb.getMany();
      const taxCategory = (await this.customerProductVariantService.getChannelTaxCategory(ctx)) as TaxCategory;
      for (const order of orders) {
        for (const line of order.lines) {
          line.productVariant.taxCategory = taxCategory;
          line.taxCategory = taxCategory;
        }
        order.surcharges = [];
      }
      let shoppingTrolley = orders.find(
        // TODO 不应该忽略
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        order => (order.customFields as OrderCustomFields).purchaseType === OrderPurchaseType.ShoppingTrolley,
      );
      let outrightPurchase = orders.find(
        // TODO 不应该忽略
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        order => (order.customFields as OrderCustomFields).purchaseType === OrderPurchaseType.OutrightPurchase,
      );
      let regularOrder = orders.find(
        // TODO 不应该忽略
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        order => (order.customFields as OrderCustomFields).purchaseType === OrderPurchaseType.RegularOrder,
      );
      for (const type of types) {
        if (
          (type === OrderPurchaseType.ShoppingTrolley && !shoppingTrolley) ||
          (type === OrderPurchaseType.OutrightPurchase && !outrightPurchase) ||
          (type === OrderPurchaseType.RegularOrder && !regularOrder)
        ) {
          const order = await this.orderService.create(ctx, ctx.activeUserId);
          const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
          try {
            await this.orderService.updateCustomFields(ctx, order.id, {
              purchaseType: type,
            });
          } catch (error) {
            Logger.error(`update order purchaseType error:${error}`);
            throw new Error('update order purchaseType error');
          } finally {
            await this.redLockService.unlockResource(lock);
          }
          // TODO 不应该忽略
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          (order.customFields as OrderCustomFields).purchaseType = type;
          if (type === OrderPurchaseType.ShoppingTrolley) {
            shoppingTrolley = order;
          } else if (type === OrderPurchaseType.OutrightPurchase) {
            outrightPurchase = order;
          } else if (type === OrderPurchaseType.RegularOrder) {
            regularOrder = order;
          }
        }
      }
      return {shoppingTrolley: shoppingTrolley, outrightPurchase, regularOrder};
    } finally {
      await this.redLockService.unlockResource(getActiveOrdersLock);
    }
  }

  // isValidChinaPhoneNumber(phone: string): boolean {
  //   const regex = /^(1[3-9]\d{9}|[569]\d{7}|9\d{8})$/;
  //   return regex.test(phone);
  // }

  async setShippingAddress(ctx: RequestContext, orderId: ID, input: CreateAddressInput) {
    // const phone = input.phoneNumber;
    // if (!phone || !this.isValidChinaPhoneNumber(phone)) {
    //   throw new OperationError('请填写正确的手机号码');
    // }
    const order = await this.orderPromotionResultService.getOrderOrThrow(ctx, orderId);
    const country = await this.countryService.findOneByCode(ctx, input.countryCode);
    const shippingAddress = {
      ...input,
      countryCode: input.countryCode,
      country: country.name,
    };
    await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .update(Order)
      .set({
        shippingAddress,
        customFields: {
          receiverName: input.fullName ?? '',
          receiverPhone: input.phoneNumber ?? '',
        },
      })
      .where('id = :id', {id: order.id})
      .execute();
    order.shippingAddress = shippingAddress;
    // Since a changed ShippingAddress could alter the activeTaxZone,
    // we will remove any cached activeTaxZone, so it can be re-calculated
    // as needed.
    this.requestCache.set(ctx, 'activeTaxZone', undefined);
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, orderId, 'update'));
    return order;
  }

  async orderRefund(ctx: RequestContext, orderId: string, relations?: RelationPaths<OrderRefund>) {
    const qb = this.listQueryBuilder.build(
      OrderRefund,
      {sort: {createdAt: 'DESC'}},
      {
        ctx,
        relations: relations,
        channelId: ctx.channelId,
      },
    );
    qb.where('orderId =:orderId', {orderId});
    const items = await qb.getMany();
    return items[0];
  }

  async removeAllItemsFromOrder(ctx: RequestContext, order: Order) {
    const validationError = this.assertAddingItemsState(order);
    if (validationError) {
      return validationError;
    }
    await this.connection.getRepository(ctx, OrderLine).remove(order.lines);
    order.lines = [];
    await this.connection
      .getRepository(ctx, OrderPromotionResult)
      .createQueryBuilder('orderPromotionResult')
      .update()
      .set({promResult: null})
      .where('orderId = :orderId', {orderId: order.id})
      .execute();
    await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
    return order;
  }

  assertAddingItemsState(order: Order) {
    if (order.state !== 'AddingItems' && order.state !== 'Draft') {
      return new OrderModificationError();
    }
  }

  async removeItemFromOrder(
    ctx: RequestContext,
    id: ID,
    orderLineIds: ID[],
    order?: Order,
    isApplyPriceAdjustments = true,
  ) {
    if (!order) {
      order = await this.orderService.findOne(ctx, id);
    }
    if (!order) {
      throw new Error('order not exist');
    }
    for (const orderLineId of orderLineIds) {
      await this.orderPromotionResultService.removeItemFromOrder(ctx, id, orderLineId, order);
    }
    order.lines = order?.lines.filter(line => !orderLineIds.includes(line.id)) || [];
    if (!order?.lines || order.lines.length === 0) {
      await this.connection.getRepository(ctx, OrderPromotionResult).update({order: {id}}, {promResult: null});
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(id, ctx.channelId));
    }
    order = (await this.orderService.findOne(ctx, id)) as Order;
    if (isApplyPriceAdjustments) {
      order = (await this.orderPromotionResultService.applyPriceAdjustments(ctx, order)) as Order;
      return {
        order,
        recalculateOrNot: false,
      };
    } else {
      return {
        order,
        recalculateOrNot: true,
      };
    }
  }

  async orderRefunds(
    ctx: RequestContext,
    options?: ListQueryOptions<OrderRefund>,
    relations?: RelationPaths<OrderRefund>,
  ): Promise<PaginatedList<OrderRefund>> {
    const qb = this.listQueryBuilder.build(OrderRefund, options, {
      ctx,
      relations: relations,
      channelId: ctx.channelId,
    });
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async createRefund(ctx: RequestContext, refundOrderCustom: RefundOrderCustom) {
    const orderId = refundOrderCustom.orderId;
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new Error('order not exist');
    }
    let orderRefund = new OrderRefund({
      images: refundOrderCustom.images as string[],
      cancelType: refundOrderCustom.cancelType,
      cancelExplain: refundOrderCustom.cancelExplain,
      cancelCause: refundOrderCustom.cancelCause,
      refundAmount: refundOrderCustom.refundAmount,
      order: order,
    });
    orderRefund = await this.channelService.assignToCurrentChannel(orderRefund, ctx);
    return this.connection.getRepository(ctx, OrderRefund).save(orderRefund);
  }

  async refundAudit(ctx: RequestContext, refundId: ID, state: RefundState): Promise<OrderRefund> {
    const orderRefund = await this.connection.getRepository(ctx, OrderRefund).findOneOrFail({
      where: {id: refundId},
      relations: ['order', 'order.lines'],
    });
    if (state === RefundState.Settled) {
      const order = orderRefund.order;
      if (!order) {
        throw new Error('order not exist');
      }

      orderRefund.state = state;
      await this.createARefundBasedOnTheOrderId(ctx, order.id, orderRefund.refundAmount, orderRefund.cancelCause);
    } else if (state === RefundState.Failed) {
      orderRefund.state = state;
    } else {
      throw new Error('Wrong state');
    }
    return this.connection.getRepository(ctx, OrderRefund).save(orderRefund);
  }

  async createARefundBasedOnTheOrderId(ctx: RequestContext, orderId: ID, refundAmount = 0, cancelCause?: string) {
    const order = await this.orderService.findOne(ctx, orderId, ['channels', 'lines']);
    if (!order) {
      throw new Error('order not exist,create ARefund Based error');
    }
    const payments = await this.orderService.getOrderPayments(ctx, orderId);
    const orderPayment = payments[0];
    const orderLine = order.lines;
    const orderLineInput = [];
    for (const line of orderLine) {
      orderLineInput.push({
        orderLineId: line.id,
        quantity: line.quantity || line.orderPlacedQuantity,
      });
    }
    const refundInput = {
      lines: orderLineInput,
      shipping: 0,
      adjustment: 0,
      paymentId: orderPayment.id,
      reason: cancelCause,
    };
    return this.refundOrder(ctx, refundInput, refundAmount);
  }

  // 根据订单行ID创建退款
  async createARefundAccordingToOrderLineIds(
    ctx: RequestContext,
    orderId: ID,
    orderLineIds: {orderLineId: ID; quantity: number}[],
    refundAmount = 0,
    cancelCause?: string,
  ) {
    Logger.debug(`创建退款信息的orderLineIds:${JSON.stringify(orderLineIds)}`);
    const order = await this.orderService.findOne(ctx, orderId, ['channels', 'lines']);
    if (!order) {
      throw new Error('order not exist,create ARefund Based error');
    }
    const payments = await this.orderService.getOrderPayments(ctx, orderId);
    Logger.debug(`获取订单支付信息${JSON.stringify(payments)}`);
    const orderPayment = payments[0];
    // const orderLine = order.lines;
    // const orderLineInput = [];
    // for (const line of orderLine) {
    //   if (orderLineIds.indexOf(line.id) !== -1) {
    //     orderLineInput.push({orderLineId: line.id, quantity: line.quantity});
    //   }
    // }
    const refundInput = {
      lines: orderLineIds,
      shipping: 0,
      adjustment: 0,
      paymentId: orderPayment.id,
      reason: cancelCause,
    };
    return this.refundOrder(ctx, refundInput, refundAmount);
  }

  /**
   * @description Rewrite the refund order creation interface
   * Creates a {@link Refund} against the order and in doing so invokes the `createRefund()` method of the
   * {@link PaymentMethodHandler}.
   */
  async refundOrder(
    ctx: RequestContext,
    input: RefundOrderInput,
    refundAmount: number,
  ): Promise<ErrorResultUnion<RefundOrderResult, Refund>> {
    if ((!input.lines || input.lines.length === 0 || summate(input.lines, 'quantity') === 0) && input.shipping === 0) {
      return new NothingToRefundError();
    }
    const orders = await getOrdersFromLines(ctx, this.connection, input.lines ?? []);
    if (1 < orders.length) {
      return new MultipleOrderError();
    }
    const payment = await this.connection.getEntityOrThrow(ctx, Payment, input.paymentId, {
      relations: ['order'],
    });
    if (orders?.length && !idsAreEqual(payment.order.id, orders[0].id)) {
      return new PaymentOrderMismatchError();
    }
    const order = payment.order;
    if (order.state === 'AddingItems' || order.state === 'ArrangingPayment' || order.state === 'PaymentAuthorized') {
      return new RefundOrderStateError({orderState: order.state});
    }
    if (process.env.APP_ENV !== 'production') {
      refundAmount = input.lines.length * 1;
    }

    return this.refundExecute(ctx, input, order, payment, refundAmount);
  }

  /**
   * @description Refund execution interface rewrite (add gift red envelope refund deduction)
   * Creates a Refund against the specified Payment. If the amount to be refunded exceeds the value of the
   * specified Payment (in the case of multiple payments on a single Order), then the remaining outstanding
   * refund amount will be refunded against the next available Payment from the Order.
   *
   * When creating a Refund in the context of an Order, it is
   * preferable to use the {@link OrderService} `refundOrder()` method, which performs additional
   * validation.
   */
  async refundExecute(
    ctx: RequestContext,
    input: RefundOrderInput,
    order: Order,
    selectedPayment: Payment,
    refundAmount: number,
  ): Promise<Refund | RefundStateTransitionError> {
    Logger.debug(`退款订单执行`);
    const orderWithRefunds = await this.connection.getEntityOrThrow(ctx, Order, order.id, {
      relations: ['payments', 'payments.refunds', 'customer'],
    });

    function paymentRefundTotal(payment: Payment): number {
      const nonFailedRefunds = payment.refunds?.filter(refund => refund.state !== 'Failed') ?? [];
      return summate(nonFailedRefunds, 'total');
    }

    const refundsCreated: Refund[] = [];
    let orderTotalAmount = 0;
    if (order instanceof Order) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      orderTotalAmount = (order.customFields as any).orderPromotionResult?.promResult?.orderTotalPrice;
    }
    const refundablePayments = orderWithRefunds.payments.filter(p => {
      return paymentRefundTotal(p) < orderTotalAmount || p.metadata?.payerTotal || p.amount;
    });
    let primaryRefund: Refund | undefined;
    const refundedPaymentIds: ID[] = [];
    if (!orderWithRefunds.customer?.id) {
      Logger.error('退款执行:orderWithRefunds.customer?.id');
      throw Error('customer not exist');
    }
    const customer = await this.customerService.findOne(ctx, orderWithRefunds.customer?.id);
    if (!customer) {
      Logger.error('退款执行订单用户不存在');
      throw Error('customer not exist');
    }
    const lineIds = input.lines.map(l => l.orderLineId);
    // const refundableAmount = await this.orderPromotionResultService.getRefundableAmount(ctx, order.id, lineIds);
    // 先查询订单全部可退金额
    const refundableAmountAll = await this.orderPromotionResultService.getRefundableAmountByOrderId(
      ctx,
      order.id,
      true,
    );
    let refundableAmount = 0;
    refundableAmountAll.forEach(item => {
      if (lineIds.includes(Number(item.orderLine.id))) {
        refundableAmount += item.refundableAmount;
      }
    });
    const amountActivity = await this.interfaceComp.amountActivity(ctx, customer.id, order.id);
    // const refundTotal = itemAmount + input.shipping + input.adjustment - amountActivity;
    let refundTotal = refundableAmount;
    Logger.debug(
      `退款订单参数:refundTotal:${refundTotal},refundableAmount:${refundableAmount},input.shipping:${input.shipping},input.adjustment:${input.adjustment},amountActivity:${amountActivity},refundAmount:${refundAmount}`,
    );
    const refundMax =
      orderWithRefunds.payments
        ?.map(p => (orderTotalAmount || p.amount) - paymentRefundTotal(p))
        .reduce((sum, amount) => sum + amount, 0) ?? 0;
    let refundOutstanding = Math.min(refundTotal, refundMax);
    refundTotal = refundOutstanding;
    Logger.debug(
      `退款订单执行:refundTotal:${refundTotal},refundMax:${refundMax},refundOutstanding:${refundOutstanding}`,
    );
    if (process.env.APP_ENV !== 'production') {
      refundTotal = lineIds.length * 1;
    }
    // do {
    const paymentToRefund =
      (refundedPaymentIds.length === 0 && refundablePayments.find(p => idsAreEqual(p.id, selectedPayment.id))) ||
      refundablePayments.find(p => !refundedPaymentIds.includes(p.id));
    if (!paymentToRefund) {
      Logger.error('退款执行:paymentToRefund is null');
      throw new InternalServerError(`Could not find a Payment to refund`);
    }
    const amountNotRefunded = orderTotalAmount || paymentToRefund.amount - paymentRefundTotal(paymentToRefund);
    let total = Math.min(amountNotRefunded, refundOutstanding);
    //收入退款金额和可退款金额取最小值
    if (refundAmount) {
      total = Math.min(total, refundAmount);
      Logger.debug(`退款订单执行:total:${total}`);
    }
    let refund = new Refund({
      payment: paymentToRefund,
      total,
      items: refundableAmount,
      reason: input.reason,
      adjustment: input.adjustment,
      shipping: input.shipping,
      method: selectedPayment.method,
      state: 'Pending',
      metadata: {},
    });
    let paymentMethod: PaymentMethod | undefined;
    let handler: PaymentMethodHandler | undefined;
    try {
      const methodAndHandler = await this.paymentMethodService.getMethodAndOperations(ctx, paymentToRefund.method);
      paymentMethod = methodAndHandler.paymentMethod;
      handler = methodAndHandler.handler;
    } catch (e) {
      Logger.warn(
        `Could not find a corresponding PaymentMethodHandler when creating a refund for the Payment with method "${paymentToRefund.method}"`,
      );
    }
    const createRefundResult =
      paymentMethod && handler
        ? await handler.createRefund(
            ctx,
            input,
            total,
            order,
            paymentToRefund,
            paymentMethod.handler.args,
            paymentMethod,
          )
        : false;
    Logger.debug(`退款订单执行:createRefundResult:${JSON.stringify(createRefundResult)}`);
    if (createRefundResult) {
      refund.transactionId = createRefundResult.transactionId || '';
      refund.metadata = createRefundResult.metadata || {};
    }
    refund = await this.connection.getRepository(ctx, Refund).save(refund);
    if (createRefundResult) {
      const fromState = refund.state;
      try {
        await this.refundStateMachine.transition(ctx, order, refund, createRefundResult.state);
      } catch (e) {
        return new RefundStateTransitionError({
          fromState,
          toState: createRefundResult.state,
          transitionError: e.message,
        });
      }
      await this.connection.getRepository(ctx, Refund).save(refund, {reload: false});
      this.eventBus.publish(new RefundStateTransitionEvent(fromState, createRefundResult.state, ctx, refund, order));
    }
    if (primaryRefund == null) {
      primaryRefund = refund;
    }
    refundsCreated.push(refund);
    refundedPaymentIds.push(paymentToRefund.id);
    refundOutstanding = refundTotal - summate(refundsCreated, 'total');
    // } while (0 < refundOutstanding);
    // tslint:disable-next-line:no-non-null-assertion
    return primaryRefund!;
  }

  // private async getOrdersAndItemsFromLines(
  //   ctx: RequestContext,
  //   orderLinesInput: OrderLineInput[],
  //   itemMatcher: (i: OrderItem) => boolean,
  // ): Promise<{orders: Order[]; items: OrderItem[]} | false> {
  //   const orders = new Map<ID, Order>();
  //   const items = new Map<ID, OrderItem>();

  //   const lines = await this.connection.getRepository(ctx, OrderLine).findByIds(
  //     orderLinesInput.map(l => l.orderLineId),
  //     {
  //       relations: ['order', 'items', 'items.fulfillments', 'order.channels', 'items.refund'],
  //       order: {id: 'ASC'},
  //     },
  //   );
  //   for (const line of lines) {
  //     const inputLine = orderLinesInput.find(l => idsAreEqual(l.orderLineId, line.id));
  //     if (!inputLine) {
  //       continue;
  //     }
  //     const order = line.order;
  //     if (!order.channels.some(channel => channel.id === ctx.channelId)) {
  //       throw new EntityNotFoundError('Order', order.id);
  //     }
  //     if (!orders.has(order.id)) {
  //       orders.set(order.id, order);
  //     }
  //     const matchingItems = line.items.sort((a, b) => (a.id < b.id ? -1 : 1)).filter(itemMatcher);
  //     if (matchingItems.length < inputLine.quantity) {
  //       return false;
  //     }
  //     matchingItems
  //       .slice(0)
  //       .sort((a, b) =>
  //         // sort the OrderItems so that those without Fulfillments come first, as
  //         // it makes sense to cancel these prior to cancelling fulfilled items.
  //         !a.fulfillment && b.fulfillment ? -1 : a.fulfillment && !b.fulfillment ? 1 : 0,
  //       )
  //       .slice(0, inputLine.quantity)
  //       .forEach(item => {
  //         items.set(item.id, item);
  //       });
  //   }
  //   return {
  //     orders: Array.from(orders.values()),
  //     items: Array.from(items.values()),
  //   };
  // }

  async getCustomer(ctx: RequestContext): Promise<Customer> {
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }

    let customer = this.requestContextCacheService.get<Customer>(ctx, 'customer');
    if (!customer) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      customer = (ctx.session as any)?.customer;
    }
    if (!customer) {
      throw new UnauthorizedError();
    }

    return customer;
  }

  async cancelRefund(ctx: RequestContext, refundId: ID) {
    const customer = await this.getCustomer(ctx);
    const refund = await this.connection
      .getRepository(ctx, OrderRefund)
      .createQueryBuilder('refund')
      .leftJoin('refund.order', 'order')
      .where('state = :state', {state: RefundState.Cancel})
      .where('refund.id =:refundId', {refundId})
      .where('order.customerId = :customerId', {customerId: customer.id})
      .getOneOrFail();
    refund.state = RefundState.Cancel;
    return this.connection.getRepository(ctx, OrderRefund).save(refund);
  }

  async updateRefund(ctx: RequestContext, refundId: ID, refundOrderCustom: RefundOrderCustom) {
    const customer = await this.getCustomer(ctx);
    let refund = await this.connection
      .getRepository(ctx, OrderRefund)
      .createQueryBuilder('refund')
      .leftJoin('refund.order', 'order')
      .where('state != :state', {state: RefundState.Settled})
      .where('refund.id =:refundId', {refundId})
      .where('order.customerId = :customerId', {customerId: customer.id})
      .getOneOrFail();
    refund = new OrderRefund({
      ...(refundOrderCustom as unknown as OrderRefund),
      id: refund.id,
      state: RefundState.Pending,
    });
    return this.connection.getRepository(ctx, OrderRefund).save(refund);
  }

  /**
   * 处理超时未处理的取消的订单
   */
  async cancelTimeout() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const updateTimeout = DateTime.local()
      .minus({day: Number(process.env.CANCEL_ORDER_TIMEOUT_DAY || 0)})
      .toJSDate();
    const cancelsOrder = await this.connection
      .getRepository(ctx, OrderRefund)
      .createQueryBuilder('orderRefund')
      .andWhere('state= :state', {state: RefundState.Pending})
      .andWhere('updatedAt < :updateTime', {
        updateTime: updateTimeout,
      })
      .getMany();
    for (const cancelOrder of cancelsOrder) {
      await this.refundAudit(ctx, cancelOrder.id, RefundState.Settled);
    }
  }

  /**
   * 查询订单列表
   * @param ctx
   * @param options 查询条件
   * @param relations 查询关联关系
   * @returns
   */
  async findOrderAll(
    ctx: RequestContext,
    options?: OrderListOptions,
    relations?: RelationPaths<Order>,
  ): Promise<PaginatedList<Order>> {
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    const customer = await this.getCustomer(ctx);
    return this.listQueryBuilder
      .build(Order, options, {
        ctx,
        relations: relations ?? ['lines', 'customer', 'lines.productVariant', 'channels', 'shippingLines', 'payments'],
        channelId: ctx.channelId,
        customPropertyMap: {
          customerLastName: 'customer.lastName',
          transactionId: 'payments.transactionId',
        },
      })
      .andWhere('order.customerId = :customerId', {customerId: customer?.id})
      .andWhere('order.customFields.deletedAt is null')
      .getManyAndCount()
      .then(([items, totalItems]) => {
        return {
          items,
          totalItems,
        };
      });
  }

  /**
   * 软删除订单
   * @param ctx
   * @param orderId 订单id
   * @returns
   */
  async softDeleteOrder(ctx: RequestContext, orderId: ID): Promise<DeletionResponse> {
    const order = await this.getCustomerOrder(ctx, orderId);
    if (!order) {
      throw new Error('order not exist');
    }
    if (
      order.state !== 'Delivered' &&
      order.state !== 'Cancelled' &&
      (order.state as unknown) !== 'ReviewSettled' &&
      (order.state as unknown) !== 'ConfirmReceiptOfGoods'
    ) {
      throw new Error('This order cannot be deleted');
    }
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      await this.orderService.updateCustomFields(ctx, orderId, {deletedAt: new Date()});
    } catch (error) {
      Logger.error(`softDeleteOrder error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    return {
      result: DeletionResult.Deleted,
    };
  }

  /**
   * 修改收货地址
   * @param ctx
   * @param orderId 订单id
   * @param input 地址信息
   * @returns
   */
  async updateOrderShippingAddress(ctx: RequestContext, orderId: ID, input: CreateAddressInput, isAdmin = false) {
    let order;
    if (isAdmin) {
      order = await this.orderService.findOne(ctx, orderId);
    } else {
      order = await this.getCustomerOrder(ctx, orderId);
    }
    if (!order) {
      throw new Error('order not exist');
    }

    if (
      order.state !== 'Created' &&
      order.state !== 'Draft' &&
      order.state !== 'AddingItems' &&
      order.state !== 'ArrangingPayment' &&
      order.state !== 'PaymentSettled' &&
      order.state !== 'PaymentAuthorized'
    ) {
      throw new Error('The delivery address cannot be changed for this order');
    }
    const channel = ctx.channel;
    if (!isAdmin) {
      if (order.lines?.length > 0) {
        const country = await this.countryService.findOneByCode(ctx, input.countryCode);
        const shippingAddress = {
          ...input,
          countryCode: input.countryCode,
          country: country.name,
        };
        await this.productRestrictionsService.checkProductRegionRestrictions(ctx, order, shippingAddress);
      }
    }
    if (order.orderPlacedAt && !isAdmin) {
      const updateShippingAddressLimitTime = (channel.customFields as ChannelCustomFields)
        .updateShippingAddressLimitTime;
      if (updateShippingAddressLimitTime) {
        const theTimeCanBeModifiedAtLast = DateTime.fromJSDate(order.orderPlacedAt).plus({
          minute: updateShippingAddressLimitTime,
        });
        if (theTimeCanBeModifiedAtLast < DateTime.local()) {
          throw new Error(
            `Delivery address cannot be changed after ${updateShippingAddressLimitTime} minutes of order`,
          );
        }
      }
    }
    await this.setShippingAddress(ctx, orderId, input);
    if (order.orderPlacedAt) {
      this.eventBus.publish(new UpdateOrderAddressEvent(ctx, orderId));
    }
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return this.orderService.findOne(ctx, orderId);
  }

  /**
   * 获取当前用户的订单信息
   * @param ctx
   * @param orderId 订单id
   * @returns
   */
  async getCustomerOrder(ctx: RequestContext, orderId: ID) {
    if (!ctx.activeUserId) {
      throw new UnauthorizedError();
    }
    const customer = await this.getCustomer(ctx);
    if (!customer) {
      return;
    }
    const orders = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.lines', 'lines')
      .leftJoinAndSelect('lines.productVariant', 'productVariant')
      .leftJoin('order.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('order.id =:orderId', {orderId})
      .andWhere('order.customerId = :customerId', {customerId: customer?.id})
      .take(1)
      .getMany();
    if (!orders || orders.length < 0) {
      throw new Error('The corresponding order does not exist');
    }
    const order = orders[0];
    if (!order) {
      throw new Error('The corresponding order does not exist');
    }
    return order;
  }

  /**
   * 确认收货
   * @param ctx
   * @param orderId 订单id
   * @returns
   */
  async confirmReceiptOfGoods(ctx: RequestContext, orderId: ID, isTimeout = false) {
    const order = await this.getCustomerOrder(ctx, orderId);
    if (!order) {
      return;
    }
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      if (isTimeout) {
        await this.orderService.updateCustomFields(ctx, orderId, {
          isAvailableAfterSale: false,
          confirmReceiptTime: new Date(),
        });
        const orderLineIds = order.lines.map(l => l.id);
        await this.orderCustomCommonService.closeOrderLineAfterSale(ctx, order, orderLineIds);
      } else {
        if (order.state !== 'Delivered' && order.state !== 'Shipped' && order.state !== 'PartiallyDelivered') {
          throw new Error('The status of this order is wrong and the receipt cannot be confirmed');
        }
        await this.orderService.updateCustomFields(ctx, orderId, {confirmReceiptTime: new Date()});
      }
    } catch (error) {
      Logger.error(`confirmReceiptOfGoods error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.orderService.transitionToState(ctx, orderId, 'ConfirmReceiptOfGoods' as any);
  }

  /**
   * 根据订单类型获取活跃订单
   * @param ctx
   * @param type 订单类型
   * @returns
   */
  async getActiveOrderByType(
    ctx: RequestContext,
    type: OrderPurchaseType,
    isRemoveMarkUp = false,
    isUseMember = false,
    isUseShoppingCredits = false,
  ) {
    if (!type) {
      type = OrderPurchaseType.RegularOrder;
    }
    const userId = ctx.activeUserId;
    if (!userId) {
      throw new UnauthorizedError();
    }
    let order = await this.activeOrder(ctx, type);
    let isNeedReCalculatePrice = false;
    if (isUseMember) {
      const isNeedPreprocess = await this.orderPromotionResultService.applyMember(ctx, order, false);
      if (isNeedPreprocess) {
        isNeedReCalculatePrice = true;
      }
    }
    if (isUseShoppingCredits) {
      const isNeedPreprocessShoppingCredits = await this.orderPromotionResultService.applyShoppingCredits(
        ctx,
        order,
        false,
      );
      if (isNeedPreprocessShoppingCredits) {
        isNeedReCalculatePrice = true;
      }
    }

    // 检查订单中的商品是否被删除
    const checkDeleteResult = await this.orderPromotionResultService.checkDeleteProduct(ctx, order, false);
    if (checkDeleteResult?.order) {
      order = checkDeleteResult?.order;
    }
    if (checkDeleteResult?.recalculateOrNot) {
      isNeedReCalculatePrice = true;
    }

    const checkOrderLineProduct = await this.checkOrderLineProduct(ctx, order, false);
    if (checkOrderLineProduct?.order) {
      order = checkOrderLineProduct?.order;
    }
    if (checkOrderLineProduct?.recalculateOrNot) {
      isNeedReCalculatePrice = true;
    }
    if (type !== OrderPurchaseType.ShoppingTrolley) {
      const cancelAllPutOnSaleResult = await this.orderPromotionResultService.cancelAllPutOnSale(ctx, order, false);
      if (cancelAllPutOnSaleResult?.order) {
        order = cancelAllPutOnSaleResult.order;
      }
      if (cancelAllPutOnSaleResult?.recalculateOrNot) {
        isNeedReCalculatePrice = true;
      }
    }
    if (type === OrderPurchaseType.OutrightPurchase) {
      if (isRemoveMarkUp) {
        const cancelAllMarkUpResult = await this.orderPromotionResultService.cancelAllMarkUp(
          ctx,
          order.id,
          order,
          false,
        );
        if (cancelAllMarkUpResult?.order) {
          order = cancelAllMarkUpResult.order;
        }
        if (cancelAllMarkUpResult?.recalculateOrNot) {
          isNeedReCalculatePrice = true;
        }
      }
      const checkInventoryResult = await this.orderPromotionResultService.checkInventory(ctx, order, false);
      if (checkInventoryResult?.order) {
        order = checkInventoryResult.order;
      }
      if (checkInventoryResult?.recalculateOrNot) {
        isNeedReCalculatePrice = true;
      }
    }
    if (type === OrderPurchaseType.ShoppingTrolley) {
      const verificationPriceResult = await this.verifyShoppingCartOrderByPreOrder(ctx, order, undefined, false);
      if (verificationPriceResult?.order) {
        order = verificationPriceResult.order;
      }
      if (verificationPriceResult?.recalculateOrNot) {
        isNeedReCalculatePrice = true;
      }
    }
    const verificationPriceResult = await this.verificationPrice(ctx, order, false);
    if (verificationPriceResult?.order) {
      order = verificationPriceResult.order;
    }
    if (verificationPriceResult?.recalculateOrNot) {
      isNeedReCalculatePrice = true;
    }
    if (isNeedReCalculatePrice) {
      order = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
    }
    return order;
  }

  async checkOrderLineProduct(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    const {orderItemPriceCalculationStrategy, changedPriceHandlingStrategy} = this.configService.orderOptions;
    let isUpdateOrderLine = false;
    for (const line of order.lines) {
      const productSkuUpdateTime = await this.kvsService.productSkuUpdateTime.get(`SKU_ID-${line.productVariantId}`);
      if (productSkuUpdateTime && new Date(productSkuUpdateTime) > new Date(line.updatedAt)) {
        const variant = await this.customerProductVariantService.getVariantWithPriceAndTax(ctx, line.productVariantId);
        let priceResult = await orderItemPriceCalculationStrategy.calculateUnitPrice(
          ctx,
          variant,
          line.customFields || {},
          order,
          line.quantity,
        );
        const initialListPrice = line.initialListPrice ?? priceResult.price;
        if (initialListPrice !== priceResult.price) {
          priceResult = await changedPriceHandlingStrategy.handlePriceChange(ctx, priceResult, line, order);
        }

        if (line.initialListPrice == null) {
          line.initialListPrice = initialListPrice;
          isUpdateOrderLine = true;
        }
        if (line.listPrice !== priceResult.price || line.listPriceIncludesTax !== priceResult.priceIncludesTax) {
          isUpdateOrderLine = true;
        }
        line.listPrice = priceResult.price;
        line.listPriceIncludesTax = priceResult.priceIncludesTax;
      }
    }
    if (!isUpdateOrderLine) {
      // 检查赠品更新时间
      isUpdateOrderLine = await this.checkOrderGiftUpdateTime(ctx, order);
    }
    if (isUpdateOrderLine) {
      await this.connection.getRepository(ctx, OrderLine).save(order.lines);
      if (isApplyPriceAdjustments) {
        order = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
        return {
          order,
          recalculateOrNot: false,
        };
      } else {
        return {
          order,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }
  async checkOrderGiftUpdateTime(ctx: RequestContext, order: Order): Promise<boolean> {
    const orderPromResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
    const gifts = orderPromResult?.promResult?.gifts;
    if (!gifts || gifts.length === 0) {
      return false;
    }
    for (const gift of gifts) {
      const item = gift?.items;
      if (!item) {
        continue;
      }
      for (const line of item) {
        const productSkuUpdateTime = await this.kvsService.productSkuUpdateTime.get(`SKU_ID-${line?.skuId}`);
        if (productSkuUpdateTime && new Date(productSkuUpdateTime) > new Date(orderPromResult.updatedAt)) {
          return true;
        }
      }
    }
    return false;
  }

  async verificationPrice(ctx: RequestContext, order: Order, isApplyPriceAdjustments = true) {
    const orderLines = order.lines;
    for (const line of orderLines) {
      const productVariantPrice = line.productVariant.productVariantPrices.find(
        productVariant => productVariant.channelId === ctx.channelId,
      );
      if (line.unitPrice !== (productVariantPrice?.price ? productVariantPrice?.price : line.productVariant.price)) {
        // return this.orderService.applyPriceAdjustments(ctx, order, order.lines);
        if (isApplyPriceAdjustments) {
          order = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
          return {
            order,
            recalculateOrNot: false,
          };
        } else {
          return {
            order,
            recalculateOrNot: true,
          };
        }
      }
    }
    return {
      order,
      recalculateOrNot: false,
    };
  }

  async verifyShoppingCartOrderByPreOrder(
    ctx: RequestContext,
    shoppingOrder: Order,
    outrightPurchase?: Order,
    isApplyPriceAdjustments = true,
  ) {
    if (!outrightPurchase) {
      outrightPurchase = await this.getActiveOrderByType(ctx, OrderPurchaseType.OutrightPurchase);
    }
    let isUpdateOrder = false;
    for (const line of outrightPurchase.lines) {
      const preOrderLine = shoppingOrder.lines.find(l => l.productVariant.id === line.productVariant.id);
      if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        continue;
      }
      if (!preOrderLine) {
        shoppingOrder = (await this.orderPromotionResultService.addItemToOrder(
          ctx,
          shoppingOrder.id,
          line.productVariant.id,
          line.quantity,
          undefined,
          shoppingOrder,
        )) as Order;
        isUpdateOrder = true;
      } else if (preOrderLine.quantity !== line.quantity) {
        shoppingOrder = (await this.orderPromotionResultService.adjustOrderLine(
          ctx,
          shoppingOrder.id,
          preOrderLine.id,
          line.quantity,
          {},
          shoppingOrder,
        )) as Order;
        isUpdateOrder = true;
      }
    }
    if (isUpdateOrder) {
      if (isApplyPriceAdjustments) {
        shoppingOrder = await this.orderPromotionResultService.applyPriceAdjustments(ctx, shoppingOrder);
        return {
          order: shoppingOrder,
          recalculateOrNot: false,
        };
      } else {
        return {
          order: shoppingOrder,
          recalculateOrNot: true,
        };
      }
    }
    return {
      order: shoppingOrder,
      recalculateOrNot: false,
    };
  }

  /**
   * 购物车结算
   * @param ctx
   * @param input 结算时勾选的商品信息
   * @returns
   */
  async shoppingCartSettlement(ctx: RequestContext, input: SettlementProduct[]) {
    // const shoppingTrolleyOrder = await this.getActiveOrderByType(ctx, OrderPurchaseType.ShoppingTrolley);
    // let order = await this.getActiveOrderByType(ctx, OrderPurchaseType.OutrightPurchase);
    let order = await this.activeOrder(ctx, OrderPurchaseType.OutrightPurchase);
    if (!order) {
      throw new Error('order empty');
    }
    let orderPromotionResult: OrderPromotionResult | null = null;
    if (input?.length) {
      // 记录原订单优惠信息
      orderPromotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
    }
    // 删除预下单全部订单项
    order = (await this.removeAllItemsFromOrder(ctx, order)) as Order;
    if (!order) {
      throw new Error('order empty');
    }
    for (const productVariant of input) {
      const productVariantId = productVariant.productVariantId;
      const quantities = productVariant.quantity;
      const isPutOnSale = await this.orderCustomCommonService.checkProductPreSale(ctx, productVariantId, true);
      if (!isPutOnSale) {
        continue;
      }
      const isLimitation = await this.orderCustomCommonService.checkProductPurchaseLimitation(
        ctx,
        order,
        productVariantId,
        quantities,
        true,
      );
      let quantity = quantities;
      if (isLimitation === 0) {
        continue;
      } else if (isLimitation > 0) {
        quantity = isLimitation;
      }
      const isAllow = await this.customerProductService.checkProductVariantPurchasePermission(
        ctx,
        productVariantId,
        true,
      );
      if (!isAllow) {
        continue;
      }
      order = (await this.orderPromotionResultService.addItemToOrder(
        ctx,
        order.id,
        productVariantId,
        quantity,
        undefined,
        order,
      )) as Order;
    }
    //检查预下单商品是否全在购物车中 如果不在则添加
    const shoppingOrder = await this.getActiveOrderByType(ctx, OrderPurchaseType.ShoppingTrolley);
    if (shoppingOrder) {
      const result = await this.verifyShoppingCartOrderByPreOrder(ctx, shoppingOrder, order, false);
      if (result.recalculateOrNot) {
        await this.orderPromotionResultService.applyPriceAdjustments(ctx, result.order);
      }
    }
    if (!order?.lines || order.lines.length === 0) {
      await this.connection
        .getRepository(ctx, OrderPromotionResult)
        .update({order: {id: order.id}}, {promResult: null});
      await this.cacheService.removeCache(CacheKeyManagerService.orderPromotionResult(order.id, ctx.channelId));
    }
    await this.orderPromotionResultService.applyPriceAdjustments(ctx, order, [], orderPromotionResult ?? undefined);
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return this.orderService.updateCustomFields(ctx, order.id, {
      sourceType: OrderPurchaseType.ShoppingTrolley,
    });
  }

  /**
   * 取消待付款的订单
   * @param ctx
   * @param orderId 订单id
   * @returns
   */
  async cancelledOrder(ctx: RequestContext, orderId: ID) {
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${orderId}`);
    try {
      const order = await this.orderService.findOne(ctx, orderId);
      if (!order) {
        throw new Error('order empty');
      }
      if (order.state !== 'ArrangingPayment') {
        throw new Error('order state error');
      }
      return await this.orderService.transitionToState(ctx, order.id, 'Cancelled');
    } catch (error) {
      Logger.error(`cancelledOrder error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  /**
   * 在购物车修改sku
   * @param ctx
   * @param orderLineId 原LineID
   * @param productVariantId 修改后的商品SkuID
   * @param quantity 修改后的商品数量
   * @returns
   */
  async updateShoppingCartSku(ctx: RequestContext, orderLineId: ID, productVariantId: ID, quantity: number) {
    let order = await this.getActiveOrderByType(ctx, OrderPurchaseType.ShoppingTrolley);
    if (!order) {
      throw new Error('order empty');
    }
    order = (await this.orderPromotionResultService.removeItemFromOrder(ctx, order.id, orderLineId, order)) as Order;
    order = (await this.orderPromotionResultService.addItemToOrder(
      ctx,
      order.id,
      productVariantId,
      quantity,
      undefined,
      order,
    )) as Order;
    order = (await this.orderPromotionResultService.applyPriceAdjustments(ctx, order)) as Order;
    await this.orderTrackingService.updateOrderTrackingByProductVariant(
      ctx,
      order.id,
      orderLineId,
      productVariantId,
      order,
    );
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return order;
  }

  async timeoutAutomaticCancelOrder() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });

    // const endTime = DateTime.fromJSDate(new Date())
    //   .minus({minutes: Number(process.env.TIMEOUT_ORDER_TO_BE_PAID_MINUTES || 30)})
    //   .toJSDate();
    const orders = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.customer', 'customer')
      .leftJoinAndSelect('customer.user', 'user')
      .leftJoinAndSelect('order.channels', 'channels')
      .where('order.state =:state', {state: 'ArrangingPayment' as OrderState})
      .andWhere('order.active = :active', {active: false})
      .andWhere('order.customFieldsTimeoutperiodtobepaid <= :endTime', {endTime: new Date()})
      .getMany();
    for (const order of orders) {
      const customerCtx = await this.getCtxByCustomerAndChannels(order.channels, order.customer as Customer);
      if (customerCtx) {
        await this.cancelledOrder(customerCtx, order.id);
      }
    }
  }

  async timeoutAutomaticConfirmOrder() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    // const updateTimeout = DateTime.local()
    //   .minus({day: Number(process.env.CONFIRM_RECEIPT_GOODS || 15)})
    //   .toJSDate();
    // 当前时间
    const updateTimeout = new Date();
    const orders = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.customer', 'customer')
      .leftJoinAndSelect('customer.user', 'user')
      .leftJoinAndSelect('order.channels', 'channels')
      .where('order.state =:state', {state: 'Delivered' as OrderState})
      .andWhere('order.active = :active', {active: false})
      .andWhere('order.customFieldsTimeoutperiodtobereceived <= :endTime', {endTime: updateTimeout})
      .getMany();
    for (const order of orders) {
      const customerCtx = await this.getCtxByCustomerAndChannels(order.channels, order.customer as Customer);
      if (customerCtx) {
        await this.confirmReceiptOfGoods(customerCtx, order.id, true);
      }
    }
  }

  async getCtxByCustomerAndChannels(channels: Channel[], customer: Customer) {
    const user = customer.user;
    let channelOrToken;
    if (channels.length > 1) {
      for (const channel of channels) {
        if (channel.code !== DEFAULT_CHANNEL_CODE) {
          channelOrToken = channel;
        }
      }
    } else {
      channelOrToken = channels[0];
    }
    if (!channelOrToken) {
      return;
    }
    let ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    channelOrToken = await this.connection
      .getRepository(ctx, Channel)
      .findOne({where: {id: channelOrToken.id}, relations: ['defaultTaxZone']});
    if (!channelOrToken) {
      return;
    }
    ctx = await this.requestContextService.create({
      apiType: 'admin',
      channelOrToken: channelOrToken,
      user: user,
      languageCode: LanguageCode.zh_Hans,
    });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (ctx as any).session['customer'] = customer;
    this.requestContextCacheService.set(ctx, 'activeTaxZone', channelOrToken!.defaultTaxZone);
    return ctx;
  }

  async findOne(ctx: RequestContext, orderId: ID, relations?: RelationPaths<Order>): Promise<Order | null> {
    const qb = this.connection.getRepository(ctx, Order).createQueryBuilder('order');
    const effectiveRelations = relations ?? [
      'channels',
      'customer',
      'customer.user',
      'lines',
      'lines.productVariant',
      'lines.productVariant.product',
      'lines.productVariant.taxCategory',
      'lines.productVariant.productVariantPrices',
      'lines.productVariant.translations',
      'lines.featuredAsset',
      'lines.taxCategory',
      'shippingLines',
      'surcharges',
    ];
    if (
      relations &&
      effectiveRelations.includes('lines.productVariant') &&
      !effectiveRelations.includes('lines.productVariant.taxCategory')
    ) {
      effectiveRelations.push('lines.productVariant.taxCategory');
    }
    qb.setFindOptions({relations: effectiveRelations})
      .leftJoin('order.channels', 'channel')
      .where('order.id = :orderId', {orderId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId});
    if (effectiveRelations.includes('lines')) {
      // qb.addOrderBy(`order__order_lines.${qb.escape('createdAt')}`, 'ASC').addOrderBy(
      //   `order__order_lines.${qb.escape('productVariantId')}`,
      //   'ASC',
      // );
      qb.addOrderBy('order__order_lines.createdAt', 'DESC');
      // .addOrderBy('order__lines__items.createdAt', 'ASC');
    }
    FindOptionsUtils.joinEagerRelations(qb, qb.alias, qb.expressionMap.mainAlias!.metadata);

    const order = await qb.take(1).getOne();
    if (order) {
      if (effectiveRelations.includes('lines.productVariant')) {
        for (const line of order.lines) {
          line.productVariant = this.translator.translate(
            await this.productVariantService.applyChannelPriceAndTax(line.productVariant, ctx, order),
            ctx,
          );
        }
      }
      return order;
    }
    return null;
  }

  async noLogisticsUpdateOrder(ctx: RequestContext, orderId: ID) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      throw new Error('order not found');
    }
    if (order.state !== 'PaymentSettled') {
      throw new Error('order state error');
    }
    const fulfillment = (await this.orderPromotionResultService.createFulfillment(ctx, {
      lines: order.lines
        .filter(line => line.quantity !== 0)
        .map(line => ({orderLineId: line.id, quantity: line.quantity})),
      handler: {
        code: manualFulfillmentHandler.code,
        arguments: [
          {
            name: 'method',
            value: 'noLogistics',
          },
          {
            name: 'trackingCode',
            value: '-1',
          },
        ],
      },
    })) as Fulfillment;
    await this.orderService.transitionFulfillmentToState(ctx, fulfillment.id, 'Shipped');
    await this.orderService.transitionFulfillmentToState(ctx, fulfillment.id, 'Delivered');
    return this.orderService.findOne(ctx, order.id);
  }

  //获取当前天数时间戳
  getTimestampDays(currentDate = new Date()) {
    if (!currentDate) {
      currentDate = new Date();
    }
    const mouth = currentDate.getMonth() + 1;
    const mouthStr = mouth < 10 ? '0' + mouth : mouth;

    const day = currentDate.getDate();
    const dayStr = day < 10 ? '0' + day : day;
    return `${currentDate.getFullYear()}${mouthStr}${dayStr}`;
  }

  @Transaction()
  async updateOrderUpdateTime(ctx: RequestContext, orderId: ID) {
    await this.kvsService.shoppingCartUpdateTime.set(`order_ID-${orderId}`, new Date());
  }
}
