import {Injectable} from '@nestjs/common';
import {CacheService} from '@scmally/ecommerce-common';
import {RedLockService} from '@scmally/red-lock';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  Channel,
  Fulfillment,
  ID,
  LanguageCode,
  ListQueryBuilder,
  Logger,
  OrderService,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  RequestContextService,
  Transaction,
  TransactionalConnection,
} from '@vendure/core';
import {DateTime} from 'luxon';
import superagent from 'superagent';
import {Brackets} from 'typeorm';
import {Logistics} from '../entities';
import {LogisticInfo, LogisticState, Traces} from '../generated-admin-types';
import {CacheKeyManagerService} from '@scmally/kvs';
@Injectable()
export class LogisticsService {
  constructor(
    private connection: TransactionalConnection,
    private orderService: OrderService,
    private requestContextService: RequestContextService,
    private listQueryBuilder: ListQueryBuilder,
    private requestContextCacheService: RequestContextCacheService,
    private cacheService: CacheService,
    private redLockService: RedLockService,
  ) {}

  async findOne(ctx: RequestContext, orderId: ID, relations?: RelationPaths<Logistics>) {
    const qb = this.listQueryBuilder.build(Logistics, undefined, {
      relations: relations ?? ['order'],
      ctx,
    });
    qb.andWhere('orderId = :orderId', {orderId});
    const logistics = await qb.getMany();
    if (logistics.length === 0) {
      return;
    }
    return logistics[0];
  }
  @Transaction()
  async createLogistics(ctx: RequestContext, fulfillment: Fulfillment) {
    const order = await this.orderService.findOneByOrderLineId(ctx, fulfillment.lines[0].orderLineId);
    if (!order) {
      Logger.error(`order not exist`);
      return;
    }
    if (fulfillment.method === 'noLogistics') {
      const timeoutPeriodToBeReceived = DateTime.local()
        .plus({day: Number(process.env.CONFIRM_RECEIPT_GOODS_WITHOUT_LOGISTICS || 2)})
        .toJSDate();
      const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
      try {
        await this.orderService.updateCustomFields(ctx, order?.id, {timeoutPeriodToBeReceived});
      } catch (error) {
        Logger.error(`update order timeoutPeriodToBeReceived error:${error}`);
      } finally {
        await this.redLockService.unlockResource(lock);
      }
      return;
    }
    let logistics = new Logistics({
      company: fulfillment.method,
      logisticCode: fulfillment.trackingCode,
      state: LogisticState.NoInformation,
      fulfillment: fulfillment,
      order: order,
    });
    logistics = await this.connection.getRepository(ctx, Logistics).save(logistics);
    const timeoutPeriodToBeReceived = DateTime.local()
      .plus({day: Number(process.env.CONFIRM_RECEIPT_GOODS || 15)})
      .toJSDate();
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      await this.orderService.updateCustomFields(ctx, order?.id, {logistics, timeoutPeriodToBeReceived});
    } catch (error) {
      Logger.error(`update order timeoutPeriodToBeReceived error:${error}`);
      throw new Error('update order timeoutPeriodToBeReceived error');
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    await this.cacheService.removeCache(CacheKeyManagerService.orderLogistics(order.id, ctx.channelId));
  }
  async timeUpdateLogistics() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const sixHoursAgo = DateTime.now().minus({hours: 6}).toJSDate();
    // 三十天前的物流信息不再更新
    const thirtyDaysAgo = DateTime.now().minus({days: 30}).toJSDate();
    const logistics = await this.connection
      .getRepository(adminCtx, Logistics)
      .createQueryBuilder('logistics')
      .leftJoinAndSelect('logistics.order', 'order')
      .leftJoinAndSelect('order.channels', 'channels')
      .leftJoinAndSelect('logistics.fulfillment', 'fulfillment')
      .where('logistics.state != :state', {state: LogisticState.SignFor})
      .andWhere('logistics.createdAt >= :thirtyDaysAgo', {thirtyDaysAgo: thirtyDaysAgo})
      .andWhere(
        new Brackets(qbSql => {
          qbSql
            .orWhere('logistics.lastUpdateTime IS NULL')
            .orWhere('logistics.lastUpdateTime <= :sixHoursAgo', {sixHoursAgo: sixHoursAgo});
        }),
      )
      .getMany();
    for (const logistic of logistics) {
      const ctx = await this.getAdminCtxByChannels(logistic.order.channels);
      let logisticsCode = logistic.logisticCode;
      // if (logisticsCode.substring(0, 2).toLocaleUpperCase() === 'SF') {
      const shippingAddress = logistic.order.shippingAddress;
      if (shippingAddress) {
        const phoneNumber = shippingAddress.phoneNumber;
        if (phoneNumber) {
          logisticsCode = `${logisticsCode}:${phoneNumber.slice(-4)}`;
        }
      }
      // }
      const resultInfo = await this.getLogisticInfo(logisticsCode);
      const state = this.stateTransition(resultInfo.State);
      if (state === LogisticState.SignFor) {
        const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${logistic.order.id}`);
        try {
          //物流状态变更为已签收时更新订单状态为已签收
          await this.orderService.transitionFulfillmentToState(ctx, logistic.fulfillment.id, 'Delivered');
          await this.orderService.updateCustomFields(ctx, logistic.order.id, {signingTime: new Date()});
        } catch (error) {
          Logger.error(`update order signingTime error:${error}`);
          throw new Error('update order signingTime error');
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
      const logisticsInfo = this.infoTransition(resultInfo, state);
      logistic.logisticInfo = logisticsInfo;
      logistic.state = state;
      logistic.lastUpdateTime = new Date();
      await this.connection.getRepository(ctx, Logistics).save(logistic);
      await this.cacheService.removeCache(CacheKeyManagerService.orderLogistics(logistic.order.id, ctx.channelId));
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  infoTransition(resultInfo: any, state: LogisticState): LogisticInfo {
    const logisticInfo: LogisticInfo = {};
    if (state === LogisticState.CodeError || state === LogisticState.NoInformation) {
      logisticInfo.logisticCode = resultInfo.LogisticCode;
      logisticInfo.shipperCode = resultInfo.ShipperCode;
      logisticInfo.traces = resultInfo.Traces;
      logisticInfo.state = state;
      logisticInfo.success = resultInfo.Success;
      logisticInfo.reason = resultInfo.Reason;
      return logisticInfo;
    } else {
      logisticInfo.logisticCode = resultInfo.LogisticCode;
      logisticInfo.shipperCode = resultInfo.ShipperCode;
      const trace: Traces[] = [];
      const tracesArray = resultInfo.Traces;
      for (const oldTraces of tracesArray) {
        const newTraces: Traces = {};
        newTraces.acceptStation = oldTraces.AcceptStation;
        newTraces.acceptTime = new Date(oldTraces.AcceptTime);
        trace.push(newTraces);
      }
      logisticInfo.traces = trace;
      logisticInfo.state = state;
      logisticInfo.success = resultInfo.Success;
      logisticInfo.courier = resultInfo.Courier;
      logisticInfo.courierPhone = resultInfo.CourierPhone;
      logisticInfo.updateTime = new Date(resultInfo.updateTime);
      logisticInfo.takeTime = resultInfo.takeTime;
      logisticInfo.name = resultInfo.Name;
      logisticInfo.site = resultInfo.Site;
      logisticInfo.phone = resultInfo.Phone;
      logisticInfo.logo = resultInfo.Logo;
      logisticInfo.reason = resultInfo.Reason;
      return logisticInfo;
    }
  }

  stateTransition(state: string): LogisticState {
    switch (state) {
      case '-1':
        return LogisticState.CodeError;
      case '0':
        return LogisticState.NoInformation;
      case '1':
        return LogisticState.PickUp;
      case '2':
        return LogisticState.InTransit;
      case '3':
        return LogisticState.SignFor;
      case '4':
        return LogisticState.ProblemShipment;
      case '5':
        return LogisticState.DifficultItem;
      case '6':
        return LogisticState.ReturnSignFor;
      default:
        throw new Error('Incorrect status code');
      //0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
    }
  }

  async getLogisticInfo(logisticCode: string) {
    const url = `${process.env.LOGISTICS_URL}?n=${logisticCode}`;
    // if (shipmentCompany) {
    //   url = `${url}&t=${shipmentCompany}`;
    // }
    const result = await superagent.get(url).set('Authorization', `AppCode ${process.env.LOGISTICS_APP_CODE}`);
    return JSON.parse(result.text ?? '{}');
  }

  async getLogisticsByLogisticCode(ctx: RequestContext, logisticCode: string) {
    const logistics = await this.connection.getRepository(ctx, Logistics).find({
      where: {logisticCode},
      relations: ['order', 'fulfillment'],
    });
    return logistics;
  }

  async getAdminCtxByChannels(channels: Channel[]) {
    let channelOrToken;
    if (channels.length > 1) {
      for (const channel of channels) {
        if (channel.code !== DEFAULT_CHANNEL_CODE) {
          channelOrToken = channel;
        }
      }
    } else {
      channelOrToken = channels[0];
    }
    if (!channelOrToken) {
      throw new Error('channel not exist');
    }
    let ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    channelOrToken = await this.connection
      .getRepository(ctx, Channel)
      .findOne({where: {id: channelOrToken.id}, relations: ['defaultTaxZone']});
    if (!channelOrToken) {
      throw new Error('channel not exist');
    }
    ctx = await this.requestContextService.create({
      apiType: 'admin',
      channelOrToken: channelOrToken,
      languageCode: LanguageCode.zh_Hans,
    });

    this.requestContextCacheService.set(ctx, 'activeTaxZone', channelOrToken!.defaultTaxZone);

    return ctx;
  }
}
