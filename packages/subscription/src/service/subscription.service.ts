import {Injectable} from '@nestjs/common';
import {CacheService} from '@scmally/ecommerce-common';
import {RedLockService} from '@scmally/red-lock';
import {CreateAddressInput} from '@vendure/common/lib/generated-types';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  ActiveOrderService,
  Channel,
  ChannelService,
  CountryService,
  CustomerService,
  EventBus,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderService,
  PaginatedList,
  ProductService,
  RelationPaths,
  RequestContext,
  RequestContextCacheService,
  RequestContextService,
  SessionService,
  Transaction,
  TransactionalConnection,
  UserInputError,
  isGraphQlErrorResult,
} from '@vendure/core';
import {PaymentMethodService} from '@vendure/core/dist/service/services/payment-method.service';
import {DateTime, Interval} from 'luxon';
import {Subscription, SubscriptionOperation, SubscriptionPlan, SubscriptionProduct} from '../entities';
import {SubscriptionCreatedEvent} from '../event';
import {
  DiscountType,
  FrequencyUnit,
  OperationSubscritionType,
  OperationSubscritionValue,
  OrderCustomFields,
  RefundResult,
  SubscriptionState,
  SubscritionCancelInput,
} from '../generated-shop-types';
import {SubscriptionTimeType} from '../generated-types';
import {OrderCustomService} from './order-custom.service';
import {CacheKeyManagerService} from '@scmally/kvs';
@Injectable()
export class SubscriptionService {
  private readonly relations = ['orders', 'subscriptionProducts', 'subscriptionPlan', 'customer', 'customer.user'];
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private orderService: OrderService,
    private channelService: ChannelService,
    private customerService: CustomerService,
    private productService: ProductService,
    private countryService: CountryService,
    private paymentMethodService: PaymentMethodService,
    private requestContextService: RequestContextService,
    private eventBus: EventBus,
    private sessionService: SessionService,
    private activeOrderService: ActiveOrderService,
    private requestContextCacheService: RequestContextCacheService,
    private cacheService: CacheService,
    private orderCustomService: OrderCustomService,
    private redLockService: RedLockService,
  ) {}
  @Transaction()
  async orderPaymentSettled(ctx: RequestContext, order: Order) {
    const orderInfo = await this.orderService.findOne(ctx, order.id);
    if (!orderInfo) {
      throw Error('order not exist');
    }
    const customFields = orderInfo.customFields as OrderCustomFields;
    if (!customFields) {
      throw Error('customFields not exist');
    }
    const expireNumberOfPeriod = customFields.periods || 1; //订阅期数
    const firstShippingDate = customFields.firstShippingDate || new Date(); //第一次发货时间
    if (!firstShippingDate) {
      throw Error('firstShippingDate not exist');
    }
    let discountAmount = 0;
    let discountType = DiscountType.FixedAmount;
    let frequencyUnit = FrequencyUnit.Day;
    let frequency = 0;
    let cutOffDays = 0;
    const subscriptionPlan = customFields.subscriptionPlan as unknown as SubscriptionPlan;
    if (expireNumberOfPeriod > 1 && subscriptionPlan) {
      if (!subscriptionPlan) {
        throw Error('subscriptionPlan not exist');
      }
      if (!subscriptionPlan.periodAndDiscount) {
        throw Error('periodAndDiscount not exist');
      }
      if (!subscriptionPlan.cutOffDays) {
        throw Error('cutOffDays 不能为空');
      }
      cutOffDays = subscriptionPlan.cutOffDays;

      const deliveryInterval = customFields.deliveryInterval; //订阅间隔
      if (!deliveryInterval) {
        throw Error('deliveryInterval not exist');
      }
      frequency = deliveryInterval;

      for (const periodAndDiscount of subscriptionPlan.periodAndDiscount) {
        if (!periodAndDiscount) {
          continue;
        }
        if (periodAndDiscount.expireNumberOfPeriod === expireNumberOfPeriod) {
          discountAmount = periodAndDiscount.discountAmount ? periodAndDiscount.discountAmount : 0;
          discountType = periodAndDiscount.discountType ? periodAndDiscount.discountType : DiscountType.FixedAmount;
        }
      }
      if (!subscriptionPlan.subscriptionInterval?.unit) {
        throw Error('subscription unit be null');
      }
      frequencyUnit = subscriptionPlan.subscriptionInterval?.unit;
    } else {
      //单期或者不是订阅单时不需要生成订阅
      return;
    }

    let subscription = new Subscription({
      expireNumberOfPeriod: expireNumberOfPeriod,
      currentNumberOfPeriod: 1,
      cutOffDays: cutOffDays,
      discountType,
      discountAmount,
      frequencyUnit: frequencyUnit,
      frequency: frequency,
      firstShippingDate: firstShippingDate,
      lastShippingDate: firstShippingDate,
      nextShippingDate: firstShippingDate,
      customer: order.customer,
      code: order.code,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      totalMoney: order.total * expireNumberOfPeriod,
      subscriptionPlan,
    });
    subscription = await this.channelService.assignToCurrentChannel(subscription, ctx);
    subscription = await this.connection.getRepository(ctx, Subscription).save(subscription);
    for (const line of order.lines) {
      const productVariant = line.productVariant;
      const productId = productVariant.productId;
      const product = await this.productService.findOne(ctx, productId);
      let subscriptionProduct = new SubscriptionProduct({
        product,
        productVariant,
        subscription,
        quantity: line.quantity,
      });
      subscriptionProduct = await this.channelService.assignToCurrentChannel(subscriptionProduct, ctx);
      await this.connection.getRepository(ctx, SubscriptionProduct).insert({...subscriptionProduct});
    }
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      const updateOrder = await this.orderService.updateCustomFields(ctx, order.id, {
        subscription: {id: subscription.id},
      });
      Logger.info(`Subscription created with id ${subscription.id}, updateOrder ${JSON.stringify(updateOrder)}`);
    } catch (error) {
      Logger.error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    const newSubscription = await this.findOne(ctx, subscription.id as string, undefined, [
      'customer',
      'orders',
      'orders.customer',
    ]);
    Logger.info(`Subscription created with id ${subscription.id}, newSubscription ${JSON.stringify(newSubscription)}`);
    this.eventBus.publish(new SubscriptionCreatedEvent(ctx, newSubscription as Subscription));
    await this.cacheService.removeCache(CacheKeyManagerService.orderSubscriptionPlan(order.id, ctx.channelId));
    await this.cacheService.removeCache(CacheKeyManagerService.orderSubscription(order.id, ctx.channelId));
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<Subscription>,
    userId?: ID,
    relations?: RelationPaths<Subscription>,
  ): Promise<PaginatedList<Subscription>> {
    const qb = this.listQueryBuilder
      .build(Subscription, options, {
        ctx,
        relations: relations ?? this.relations,
        channelId: ctx.channelId,
      })
      .leftJoinAndSelect('subscription.orders', 'order')
      .addOrderBy('order.createdAt', 'DESC');
    if (userId) {
      const customer = await this.orderCustomService.getCustomer(ctx);
      qb.andWhere('subscription.customerId = :customerId', {customerId: customer?.id});
    }

    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  async findOne(
    ctx: RequestContext,
    subscriptionId: ID,
    userId?: ID,
    relations?: RelationPaths<Subscription>,
  ): Promise<Subscription | undefined> {
    const qb = this.listQueryBuilder
      .build(Subscription, undefined, {
        ctx,
        relations: relations ?? this.relations,
        channelId: ctx.channelId,
      })
      .andWhere('subscription.id= :subscriptionId', {subscriptionId});
    if (userId) {
      const customer = await this.orderCustomService.getCustomer(ctx);
      qb.andWhere('customerId = :customerId', {customerId: customer?.id});
    }
    //TODO getOnt()不会返回关联关系表
    const items = await qb.getMany();
    if (items.length > 0) {
      return items[0];
    }
    return undefined;
  }

  async updateSubscriptionAddress(
    ctx: RequestContext,
    input: CreateAddressInput,
    subscriptionId: string,
  ): Promise<Subscription | undefined> {
    if (!subscriptionId) {
      throw new UserInputError('subscriptionId can not be empty');
    }
    const country = await this.countryService.findOneByCode(ctx, input.countryCode);
    const shippingAddress = {...input, countryCode: input.countryCode, country: country.name};
    const subscription = await this.findOne(ctx, subscriptionId, ctx.session?.user?.id);
    if (subscription?.state !== SubscriptionState.Underway) {
      throw new Error('The subscription status is incorrect and the address cannot be changed');
    }

    await this.saveSubscriptionOperation(ctx, subscription, OperationSubscritionType.UpdateAddress, {
      modification: JSON.stringify(shippingAddress),
      original: JSON.stringify(subscription.shippingAddress),
    });
    subscription.shippingAddress = shippingAddress;
    await this.connection.getRepository(ctx, Subscription).save(subscription);
    const orderIds = subscription.orders.map(order => order.id);
    const orderKey = [
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
    ];
    await this.cacheService.removeCache(orderKey);
    return this.findOne(ctx, subscriptionId);
  }

  async saveSubscriptionOperation(
    ctx: RequestContext,
    subscription: Subscription,
    operationType: OperationSubscritionType,
    operation: OperationSubscritionValue,
  ): Promise<SubscriptionOperation | undefined> {
    let subscriptionOperation = new SubscriptionOperation({
      operationType,
      operationValue: operation,
      subscription,
    });
    subscriptionOperation = await this.channelService.assignToCurrentChannel(subscriptionOperation, ctx);
    return this.connection.getRepository(ctx, SubscriptionOperation).save(subscriptionOperation);
  }

  async updateSubscriptionFrequency(ctx: RequestContext, frequency: number, subscriptionId: string) {
    if (!subscriptionId) {
      throw new UserInputError('subscriptionId can not be empty');
    }
    const subscription = await this.findOne(ctx, subscriptionId, ctx.session?.user?.id);
    if (subscription?.state !== SubscriptionState.Underway) {
      throw new Error('The subscription status is incorrect and the period cannot be modified');
    }
    const frequencyArray = subscription.subscriptionPlan.subscriptionInterval.frequency;
    if (!frequencyArray) {
      throw new Error('There is no alterable interval for a subscription plan');
    }
    if (frequencyArray?.indexOf(frequency) === -1) {
      throw new Error(' The number of incorrect intervals');
    }
    const currentTime = DateTime.now();
    let nextShippingDate = DateTime.fromJSDate(subscription.nextShippingDate);
    const lastShippingDate = DateTime.fromJSDate(subscription.lastShippingDate);
    const interval = Interval.fromDateTimes(currentTime, nextShippingDate);
    if (interval.length('days') > 1) {
      switch (subscription.frequencyUnit) {
        case FrequencyUnit.Day:
          nextShippingDate = lastShippingDate.plus({days: frequency});
          break;
        case FrequencyUnit.Week:
          nextShippingDate = lastShippingDate.plus({weeks: frequency});
          break;
        case FrequencyUnit.Month:
          nextShippingDate = lastShippingDate.plus({months: frequency});
          break;
        default:
          throw new Error('Type of unprocessed interval');
      }
    }
    await this.saveSubscriptionOperation(ctx, subscription, OperationSubscritionType.UpdateFrequency, {
      modification: JSON.stringify({frequency: frequency, nextShippingDate: nextShippingDate}),
      original: JSON.stringify({frequency: subscription.frequency, nextShippingDate: subscription.nextShippingDate}),
    });
    subscription.frequency = frequency;
    subscription.nextShippingDate = nextShippingDate.toJSDate();
    await this.connection.getRepository(ctx, Subscription).save(subscription);
    const orderIds = subscription.orders.map(order => order.id);
    const orderKey = [
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
    ];
    await this.cacheService.removeCache(orderKey);
    return this.findOne(ctx, subscriptionId);
  }

  async updateSubscriptionFirstShippingDate(ctx: RequestContext, firstShippingDate: Date, subscriptionId: string) {
    if (!subscriptionId) {
      throw new UserInputError('subscriptionId can not be empty');
    }
    const currentTime = DateTime.now();
    let newFirstShippingDate: DateTime;
    if (!firstShippingDate) {
      newFirstShippingDate = currentTime;
    } else {
      newFirstShippingDate = DateTime.fromJSDate(firstShippingDate);
    }
    if (newFirstShippingDate < currentTime) {
      throw new Error('The delivery time cannot be less than the current time');
    }
    const subscription = await this.findOne(ctx, subscriptionId, ctx.session?.user?.id);
    if (subscription?.state !== SubscriptionState.Underway) {
      throw new Error('The subscription status is incorrect and the period cannot be modified');
    }
    if (subscription.currentNumberOfPeriod > 1) {
      throw new Error('The first phase has been shipped and the first delivery time cannot be modified');
    }
    const oldFirstShippingDate = DateTime.fromJSDate(subscription.firstShippingDate);
    const interval = Interval.fromDateTimes(currentTime, oldFirstShippingDate);
    if (interval.length('days') < 1) {
      throw new Error('The goods are being prepared, the delivery time cannot be changed');
    }
    const newFirstShippingDateToJSDate = newFirstShippingDate.toJSDate();
    await this.saveSubscriptionOperation(ctx, subscription, OperationSubscritionType.UpdateFirstShippingDate, {
      modification: JSON.stringify({
        firstShippingDate: newFirstShippingDateToJSDate,
        nextShippingDate: newFirstShippingDateToJSDate,
        lastShippingDate: newFirstShippingDateToJSDate,
      }),
      original: JSON.stringify({
        firstShippingDate: subscription.firstShippingDate,
        nextShippingDate: subscription.nextShippingDate,
        lastShippingDate: subscription.lastShippingDate,
      }),
    });
    subscription.firstShippingDate = newFirstShippingDateToJSDate;
    subscription.nextShippingDate = newFirstShippingDateToJSDate;
    subscription.lastShippingDate = newFirstShippingDateToJSDate;
    await this.connection.getRepository(ctx, Subscription).save(subscription);
    const orderIds = subscription.orders.map(order => order.id);
    const orderKey = [
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
    ];
    await this.cacheService.removeCache(orderKey);
    return this.findOne(ctx, subscriptionId);
  }

  async unsubscribe(ctx: RequestContext, input: SubscritionCancelInput): Promise<RefundResult | Boolean> {
    const subscriptionId = input.subscriptionId;
    if (!subscriptionId) {
      throw new Error('input error');
    }
    const subscription = await this.findOne(ctx, subscriptionId, ctx.session?.user?.id, ['orders']);
    if (!subscription) {
      throw new Error('Subscription does not exist');
    }
    if (subscription.state !== SubscriptionState.Underway) {
      if (subscription.state === SubscriptionState.Cancel) {
        throw new Error('Order status error');
      }
    }
    const order = await this.orderService.findOneByCode(ctx, subscription.code);
    if (!order) {
      throw new Error('order not exist');
    }
    let cancelTotalMoney = 0;
    const currentTime = DateTime.now();
    const nextShippingDate = DateTime.fromJSDate(subscription.nextShippingDate);
    const interval = Interval.fromDateTimes(currentTime, nextShippingDate);
    if (subscription.currentNumberOfPeriod === subscription.expireNumberOfPeriod && interval.length('days') < 1) {
      throw new Error('The last order has been shipped');
    }
    if (subscription.currentNumberOfPeriod === 1 && interval.length('days') > 1) {
      cancelTotalMoney = subscription.totalMoney;
      if (process.env.APP_ENV !== 'production') {
        cancelTotalMoney = 1 * subscription.expireNumberOfPeriod;
      }
    } else {
      let surplus = subscription.expireNumberOfPeriod - subscription.currentNumberOfPeriod;
      if (interval.length('days') > 1) {
        surplus += 1;
      }
      cancelTotalMoney = order.total * surplus;
      if (process.env.APP_ENV !== 'production') {
        cancelTotalMoney = 1 * surplus;
      }
    }

    subscription.state = SubscriptionState.Cancel;
    await this.connection.getRepository(ctx, Subscription).save(subscription);

    const orderIds = subscription.orders.map(item => item.id);
    const orderKey = [
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
    ];
    await this.cacheService.removeCache(orderKey);
    await this.saveSubscriptionOperation(ctx, subscription, OperationSubscritionType.Unsubscribe, {
      cancelMoney: cancelTotalMoney,
      cancelCause: input.cancelCause,
      cancelExplain: input.cancelExplain,
    });

    const payments = await this.orderService.getOrderPayments(ctx, order?.id);
    const payment = payments[0];
    const {paymentMethod, handler} = await this.paymentMethodService.getMethodAndOperations(ctx, payment.method);
    const refundOrderInput = {
      lines: [],
      shipping: 0,
      adjustment: 0,
      paymentId: payment.id.toString(),
      reason: '',
    };
    const cancel = await handler.createRefund(
      ctx,
      refundOrderInput,
      cancelTotalMoney,
      order,
      payment,
      [],
      paymentMethod,
    );
    return cancel;
  }

  async subscriptionOperations(
    ctx: RequestContext,
    subscriptionId: string,
    operationType: OperationSubscritionType,
  ): Promise<SubscriptionOperation[]> {
    return this.connection
      .getRepository(ctx, SubscriptionOperation)
      .createQueryBuilder('subscriptionOperation')
      .leftJoin('subscriptionOperation.channels', 'channel')
      .where('operationType = :operationType', {operationType})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('subscriptionId = :subscriptionId', {subscriptionId})
      .getMany();
  }

  async subscriptionsUnderway(
    ctx: RequestContext,
    userId: ID | undefined,
    relations?: RelationPaths<Subscription>,
  ): Promise<Subscription[]> {
    const qb = this.listQueryBuilder
      .build(Subscription, undefined, {
        ctx,
        relations: relations ?? [
          'orders',
          'subscriptionProducts',
          'subscriptionProducts.product',
          'subscriptionProducts.productVariant',
        ],
        channelId: ctx.channelId,
      })
      .andWhere('subscription.state = :state', {state: SubscriptionState.Underway})
      .leftJoinAndSelect('subscription.orders', 'order')
      .addOrderBy('order.createdAt', 'DESC');
    if (userId) {
      const customer = await this.orderCustomService.getCustomer(ctx);
      qb.andWhere('subscription.customerId = :customerId', {customerId: customer?.id});
    }

    const subscriptions = await qb.getMany();
    const newSubscriptions = [];
    const currentTime = DateTime.now();
    for (const subscription of subscriptions) {
      const nextShippingDate = DateTime.fromJSDate(subscription.nextShippingDate);
      if (currentTime > nextShippingDate) {
        newSubscriptions.push(subscription);
      } else {
        const interval = Interval.fromDateTimes(currentTime, nextShippingDate);
        if (interval.length('days') <= subscription.cutOffDays) {
          newSubscriptions.push(subscription);
        }
      }
    }
    return newSubscriptions;
  }

  async findAllActiveSubscription(timeType: SubscriptionTimeType) {
    const adminCtx = await this.requestContextService.create({apiType: 'admin'});
    const totalCount = await this.connection.getRepository(adminCtx, Subscription).count({
      where: {state: SubscriptionState.Underway},
    });
    let skip = 0;
    const take = 1000;
    do {
      const subscriptions = await this.connection.getRepository(adminCtx, Subscription).find({
        where: {state: SubscriptionState.Underway},
        relations: ['orders'],
        skip,
        take,
      });
      skip += take;
      for (const subscription of subscriptions) {
        const ctx = await this.requestContextService.create({
          apiType: 'admin',
        });
        if (timeType === SubscriptionTimeType.CreateOrder) {
          await this.createOrderAfterCreatingCtx(subscription.id, true);
        } else {
          await this.updateSubscriptionPeriod(ctx, subscription);
        }
      }
    } while (skip + take < totalCount);
  }

  async createOrderAfterCreatingCtx(subscriptionId: ID, isTime = false): Promise<Subscription> {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const subscription = await this.connection
      .getRepository(ctx, Subscription)
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.customer', 'customer')
      .leftJoinAndSelect('customer.user', 'user')
      .leftJoinAndSelect('subscription.orders', 'orders')
      .leftJoinAndSelect('subscription.subscriptionProducts', 'subscriptionProducts')
      .leftJoinAndSelect('subscriptionProducts.productVariant', 'productVariant')
      .leftJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
      .leftJoinAndSelect('subscription.channels', 'channels')
      .andWhere('subscription.id= :subscriptionId', {subscriptionId})
      .take(1)
      .getOne();
    if (!subscription) {
      throw new Error('subscription not exist');
    }
    const userCtx = await this.createCtxContinueNext(subscription);
    if (!userCtx) {
      throw new Error('ctx not exist');
    }
    await this.orderCreateBySubscription(userCtx, subscription, isTime);
    return subscription;
  }

  async createCtxContinueNext(subscription: Subscription) {
    const customer = subscription.customer;
    const user = customer.user;
    let channelOrToken;
    if (subscription.channels.length > 1) {
      for (const channel of subscription.channels) {
        if (channel.code !== DEFAULT_CHANNEL_CODE) {
          channelOrToken = channel;
        }
      }
    } else {
      channelOrToken = subscription.channels[0];
    }
    if (!channelOrToken) {
      return;
    }
    channelOrToken = await this.connection
      .getRepository(Channel)
      .findOne({where: {id: channelOrToken.id}, relations: ['defaultTaxZone']});
    if (!channelOrToken) {
      return;
    }
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
      channelOrToken: channelOrToken,
      user: user,
      languageCode: LanguageCode.zh_Hans,
    });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (ctx as any).session['customer'] = customer;
    this.requestContextCacheService.set(ctx, 'activeTaxZone', channelOrToken!.defaultTaxZone);

    return ctx;
  }

  async updateSubscriptionPeriod(ctx: RequestContext, subscription: Subscription) {
    if (subscription.currentNumberOfPeriod < subscription.expireNumberOfPeriod) {
      if (subscription.currentNumberOfPeriod <= subscription.orders.length) {
        const currentTime = DateTime.now();
        let nextShippingDate = DateTime.fromJSDate(subscription.nextShippingDate);
        //上一期已发货才判断是否推进到下一期
        if (currentTime > nextShippingDate) {
          switch (subscription.frequencyUnit) {
            case FrequencyUnit.Day:
              nextShippingDate = nextShippingDate.plus({days: subscription.frequency});
              break;
            case FrequencyUnit.Week:
              nextShippingDate = nextShippingDate.plus({weeks: subscription.frequency});
              break;
            case FrequencyUnit.Month:
              nextShippingDate = nextShippingDate.plus({months: subscription.frequency});
              break;
            default:
              throw new Error(`Type ${subscription.frequencyUnit} of unprocessed interval`);
          }
          if (currentTime < nextShippingDate) {
            const interval = Interval.fromDateTimes(currentTime, nextShippingDate);
            //当前日期到下一期时间在通知时间内时推进到下一期
            if (interval.length('days') <= subscription.cutOffDays) {
              subscription.currentNumberOfPeriod += 1;
              subscription.lastShippingDate = subscription.nextShippingDate;
              subscription.nextShippingDate = nextShippingDate.toJSDate();
            }
          } else {
            subscription.currentNumberOfPeriod += 1;
            subscription.lastShippingDate = subscription.nextShippingDate;
            subscription.nextShippingDate = nextShippingDate.toJSDate();
          }
          await this.connection.getRepository(ctx, Subscription).save(subscription);
          const orderIds = subscription.orders.map(item => item.id);
          const orderKey = [
            ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
            ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
          ];
          await this.cacheService.removeCache(orderKey);
        }
        return;
      }
    }
  }

  async createOrderBySubscription(ctx: RequestContext, subscriptionId: string, isTime = false): Promise<Subscription> {
    const subscription = await this.findOne(ctx, subscriptionId, undefined, [
      'customer',
      'customer.user',
      'orders',
      'subscriptionProducts',
      'subscriptionProducts.productVariant',
      'subscriptionPlan',
      'channels',
    ]);
    if (!subscription) {
      throw new Error('subscription not exist');
    }
    await this.orderCreateBySubscription(ctx, subscription, isTime);
    return subscription;
  }

  async orderCreateBySubscription(ctx: RequestContext, subscription: Subscription, isTime = false) {
    if (isTime) {
      if (subscription.currentNumberOfPeriod <= subscription.orders.length) {
        return;
      }
      const currentTime = DateTime.now();
      const nextShippingDate = DateTime.fromJSDate(subscription.nextShippingDate);
      if (currentTime < nextShippingDate) {
        const interval = Interval.fromDateTimes(currentTime, nextShippingDate);
        if (interval.length('days') > 1) {
          return;
        }
      }
    }
    const customer = subscription.customer;
    const oneOrder = await this.orderService.findOneByCode(ctx, subscription.code);
    if (!oneOrder) {
      Logger.error('There is no initial order');
      return;
    }
    //获取活跃订单
    const order = await this.activeOrderService.getActiveOrder(ctx, {}, true);

    //清除当前订单全部产品
    await this.orderService.removeAllItemsFromOrder(ctx, order.id);

    //添加产品到订单
    for (const subscriptionProduct of subscription.subscriptionProducts) {
      await this.orderService.addItemToOrder(
        ctx,
        order.id,
        subscriptionProduct.productVariant.id,
        subscriptionProduct.quantity,
      );
    }
    //添加用户到订单
    // customer = await this.customerService.createOrUpdate(ctx, customer, true);
    // if (isGraphQlErrorResult(customer)) {
    //   return;
    // }
    await this.orderService.addCustomerToOrder(ctx, order.id, customer);
    if (!subscription.shippingAddress) {
      Logger.error('shippingAddress not exits');
      return;
    }
    //添加收货地址到订单
    await this.orderService.setShippingAddress(ctx, order.id, subscription.shippingAddress as CreateAddressInput);

    //添加收货方式
    const eligibleShippingMethods = await this.orderService.getEligibleShippingMethods(ctx, oneOrder.id);
    await this.orderService.setShippingMethod(ctx, order.id, [eligibleShippingMethods[0].id]);
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      //设置订单订阅相关参数
      await this.orderService.updateCustomFields(ctx, order.id, {...oneOrder.customFields, subscription});
    } catch (error) {
      Logger.error(`update order subscription error:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    //获取支付方式
    const payments = await this.orderService.getOrderPayments(ctx, oneOrder.id);
    await this.orderService.transitionToState(ctx, order.id, 'ArrangingPayment');
    //完成支付
    const paymentToOrder = await this.orderService.addPaymentToOrder(ctx, order.id, {
      method: payments[0].method,
      metadata: payments[0].metadata,
    });
    if (isGraphQlErrorResult(paymentToOrder)) {
      return order;
    }
    if (paymentToOrder.active === false) {
      await this.customerService.createAddressesForNewCustomer(ctx, paymentToOrder);
    }
    if (paymentToOrder.active === false && ctx.session?.activeOrderId === order.id) {
      await this.sessionService.unsetActiveOrder(ctx, ctx.session);
    }
    const orderIds = subscription.orders.map(item => item.id);
    const orderKey = [
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscriptionPlan(orderId, ctx.channelId)),
      ...orderIds.map(orderId => CacheKeyManagerService.orderSubscription(orderId, ctx.channelId)),
    ];
    await this.cacheService.removeCache(orderKey);
  }

  async subscriptionOperation(ctx: RequestContext, subscriptionId: string, operationType: OperationSubscritionType) {
    return this.connection
      .getRepository(ctx, SubscriptionOperation)
      .createQueryBuilder('subscriptionOperation')
      .where('subscriptionId =:subscriptionId', {subscriptionId})
      .andWhere('operationType=:operationType', {operationType})
      .getMany();
  }
}
