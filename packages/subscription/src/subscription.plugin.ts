import {CacheService, CommonPlugin} from '@scmally/ecommerce-common';
import {CacheKeyManagerService, KvsPlugin} from '@scmally/kvs';
import {MemberPlugin} from '@scmally/member';
import {RedLockPlugin} from '@scmally/red-lock';
import {WeChatPlugin} from '@scmally/wechat';
import {
  EventBus,
  FulfillmentStateTransitionEvent,
  Logger,
  OrderEvent,
  OrderStateTransitionEvent,
  PluginCommonModule,
  VendurePlugin,
} from '@vendure/core';
import {AdminUiExtension} from '@vendure/ui-devkit/compiler';
import path from 'path';
import {filter} from 'rxjs/operators';
import {
  OrderCustomAdminResolver,
  OrderCustomResolver,
  ProductSearchResolver,
  SubscriptionAdminResolver,
  SubscriptionPlanAdminResolver,
  SubscriptionResolver,
} from './api';
import {LogisticsAdminResolver} from './api/logistics-admin.resolver';
import {LogisticsResolver} from './api/logistics.resolver';
import {
  Logistics,
  OrderRefund,
  Subscription,
  SubscriptionOperation,
  SubscriptionPlan,
  SubscriptionPlanProduct,
  SubscriptionProduct,
} from './entities';
import {ShoppingCartUpdateEvent} from './event';
import {adminSchemaExtensions, shopSchemaExtensions} from './graphql-schemas';
import {ActiveOrderTypeStrategy, MyOrderCodeStrategy} from './order_strategy';
import {
  DeliveryPermission,
  SubscriptionOperate,
  SubscriptionPlanOperate,
  SubscriptionPlanRead,
  SubscriptionRead,
  UpdateOrderCustomFieldPermission,
  UpdateOrderShippingAddressPermission,
} from './permission-definition';
import {
  LogisticsService,
  OrderCodeService,
  OrderCustomService,
  ProductSearchService,
  SubscriptionPlanService,
  SubscriptionService,
} from './service';
import {CrontabService} from './service/crontab.service';
import {
  addressCustomFields,
  channelCustomFields,
  customerCustomFields,
  orderSubscription,
  productCustomFields,
} from './service/customFields';

@VendurePlugin({
  imports: [RedLockPlugin, PluginCommonModule, KvsPlugin, WeChatPlugin, MemberPlugin, CommonPlugin],
  entities: [
    Subscription,
    SubscriptionPlan,
    SubscriptionPlanProduct,
    SubscriptionProduct,
    SubscriptionOperation,
    OrderRefund,
    Logistics,
  ],
  providers: [
    SubscriptionPlanService,
    SubscriptionService,
    OrderCustomService,
    LogisticsService,
    CrontabService,
    ProductSearchService,
    OrderCodeService,
  ],
  shopApiExtensions: {
    schema: shopSchemaExtensions,
    resolvers: [SubscriptionResolver, OrderCustomResolver, LogisticsResolver],
  },
  adminApiExtensions: {
    schema: adminSchemaExtensions,
    resolvers: [
      SubscriptionPlanAdminResolver,
      SubscriptionAdminResolver,
      OrderCustomAdminResolver,
      LogisticsAdminResolver,
      ProductSearchResolver,
    ],
  },
  configuration: config => {
    config.customFields.Address.push(...addressCustomFields);
    config.customFields.Product.push(...productCustomFields);
    config.customFields.Order.push(...orderSubscription);
    config.customFields.Channel.push(...channelCustomFields);
    config.customFields.Customer.push(...customerCustomFields);
    config.authOptions.customPermissions.push(SubscriptionOperate);
    config.authOptions.customPermissions.push(SubscriptionRead);
    config.authOptions.customPermissions.push(SubscriptionPlanOperate);
    config.authOptions.customPermissions.push(SubscriptionPlanRead);
    config.authOptions.customPermissions.push(UpdateOrderCustomFieldPermission);
    config.authOptions.customPermissions.push(UpdateOrderShippingAddressPermission);
    config.authOptions.customPermissions.push(DeliveryPermission);
    config.orderOptions.activeOrderStrategy = new ActiveOrderTypeStrategy();
    config.orderOptions.orderCodeStrategy = new MyOrderCodeStrategy();
    return config;
  },
})
export class SubscriptionPlugin {
  constructor(
    private eventBus: EventBus,
    private subscriptionService: SubscriptionService,
    private logisticsService: LogisticsService,
    private orderCustomService: OrderCustomService,
    private crontabService: CrontabService,
    private cacheService: CacheService,
  ) {}
  // 是否禁用定时器
  static isDisableCron = false;
  static init(option: {isDisableCron: boolean}) {
    this.isDisableCron = option.isDisableCron ?? false;
    return this;
  }

  onModuleInit() {
    this.crontabService.init(SubscriptionPlugin.isDisableCron);
  }

  async onApplicationBootstrap() {
    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'PaymentSettled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        if ((event.order.customFields as {subscription: Subscription}).subscription) {
          return;
        }
        try {
          await this.subscriptionService.orderPaymentSettled(event.ctx, event.order);
        } catch (error) {
          Logger.error(`支付完成,生成订阅信息错误:${error}`);
        }
      });
    this.eventBus
      .ofType(FulfillmentStateTransitionEvent)
      .pipe(filter(event => event.toState === 'Shipped'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.logisticsService.createLogistics(event.ctx, event.fulfillment);
        } catch (error) {
          Logger.error(`订单发货创建物流错误:${error}`);
        }
      });

    this.eventBus
      .ofType(ShoppingCartUpdateEvent)
      .pipe(filter(event => event.type === 'update'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.orderCustomService.updateOrderUpdateTime(event.ctx, event.orderId);
        } catch (error) {
          Logger.error(`修改订单时间错误:${error}`);
        }
      });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    this.eventBus.ofType(OrderEvent).subscribe(async event => {
      const ctx = event.ctx;
      await this.cacheService.removeCache(CacheKeyManagerService.orderSubscriptionPlan(event.order.id, ctx.channelId));
      await this.cacheService.removeCache(CacheKeyManagerService.orderSubscription(event.order.id, ctx.channelId));
    });
  }

  static ui: AdminUiExtension = {
    extensionPath: path.join(__dirname, '../src/ui'),

    ngModules: [
      {
        type: 'shared' as const,
        ngModuleFileName: 'subscription-ui-shared.module.ts',
        ngModuleName: 'SubscriptionUiSharedModule',
      },
      {
        type: 'lazy' as const,
        route: 'subscription-list',
        ngModuleFileName: 'subscription-ui.module.ts',
        ngModuleName: 'SubscriptionUiModule',
      },
    ],
  };
}
