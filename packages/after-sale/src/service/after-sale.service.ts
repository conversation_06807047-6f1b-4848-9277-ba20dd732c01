import {Injectable} from '@nestjs/common';
import {
  AbstractAfterSale,
  BlindBoxOrderService,
  CommonService,
  CouponService,
  CustomerLevelHandlerImplService,
  CustomerPointsService,
  CustomerSummaryService,
  Distributor,
  DistributorOrder,
  DistributorService,
  OperationError,
  OrderCustomCommonService,
  OrderPromotionResult,
  OrderPromotionResultService,
  PayCouponService,
  PaymentRewardActivityService,
  PointsChangeEvent,
  PointsHistory,
  PointsProductService,
  PointsService,
  PromotionResultDetail,
  ShoppingCreditsClaimActivityService,
  ShoppingCreditsDeductionActivityService,
  UserCoupon,
} from '@scmally/ecommerce-common';
import {KvsService, CacheKeyManagerService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {LogisticsService, OrderCustomService} from '@scmally/subscription';
import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {VirtualCurrencyCode, VirtualCurrencySourceType} from '../ui/generated-admin-types';
import {
  ChannelService,
  Customer,
  CustomerService,
  EventBus,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  Order,
  OrderLine,
  OrderService,
  Product,
  ProductService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  Transaction,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {DateTime} from 'luxon';
import {Brackets, In} from 'typeorm';
import {AfterSale, AfterSaleLine, AfterSaleLogistics} from '../entities';
import {AfterSaleEvent} from '../event';
import {HistoryMessage, OrderState} from '../type';
import {LogisticsType} from '../ui/generated-admin-types';
import {
  AfterSaleAuditInput,
  AfterSaleHistoryType,
  AfterSaleInput,
  AfterSaleLineInput,
  AfterSaleMode,
  AfterSaleState,
  AfterSaleType,
  AuditState,
  CouponType,
  LogisticState,
  OperatorType,
  OrderCustomFields,
  OrderLineCustomFields,
  PlatformAfterSaleInput,
  PointsSourceType,
  ProductCustomFields,
  PromotionType,
  PurchasePattern,
  ReturnLogisticsInput,
  SymbolType,
} from '../ui/generated-shop-types';
import {AfterSaleHistoryService} from './after-sale-history.service';
import {ChannelAddressService} from './channel-address.service';
@Injectable()
export class AfterSaleService extends AbstractAfterSale {
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private channelAddressService: ChannelAddressService,
    private logisticsService: LogisticsService,
    private orderCustomService: OrderCustomService,
    private afterSaleHistoryService: AfterSaleHistoryService,
    private requestContextService: RequestContextService,
    private customerService: CustomerService,
    private eventBus: EventBus,
    private orderService: OrderService,
    private pointsService: PointsService,
    public commonService: CommonService,
    private productService: ProductService,
    private kvsService: KvsService,
    public orderPromotionResultService: OrderPromotionResultService,
    public orderCustomCommonService: OrderCustomCommonService,
    public couponService: CouponService,
    public distributorService: DistributorService,
    public paymentRewardActivityService: PaymentRewardActivityService,
    public payCouponService: PayCouponService,
    private pointsProductService: PointsProductService,
    public redLockService: RedLockService,
    private customerPointsService: CustomerPointsService,
    private blindBoxOrderService: BlindBoxOrderService,
    private shoppingCreditsClaimActivityService: ShoppingCreditsClaimActivityService,
    private shoppingCreditsDeductionActivityService: ShoppingCreditsDeductionActivityService,
    private virtualCurrencyService: VirtualCurrencyService,
    public customerSummaryService: CustomerSummaryService,
    public customerLevelHandlerImplService: CustomerLevelHandlerImplService,
  ) {
    super(
      orderPromotionResultService,
      commonService,
      distributorService,
      paymentRewardActivityService,
      customerSummaryService,
      customerLevelHandlerImplService,
    );
  }

  async getSuccessAfterSales(ctx: RequestContext, orderIds: ID[]) {
    const afterSales = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoinAndSelect('afterSale.order', 'order')
      .andWhere('order.id in (:...orderIds)', {orderIds})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .getMany();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return afterSales as any;
  }

  async removeAfterSaleCache(ctx: RequestContext, afterSale: AfterSale) {
    const orderLineIds = afterSale.afterSaleLines.map(afterSaleLine => afterSaleLine.orderLine.id);
    if (!orderLineIds?.length) {
      return;
    }
    const orderLineKeys = [
      ...orderLineIds.map(orderLineId => CacheKeyManagerService.orderLineAfterSaleLine(orderLineId, ctx.channelId)),
      ...orderLineIds.map(orderLineId => CacheKeyManagerService.orderLineAfterSale(orderLineId, ctx.channelId)),
      ...orderLineIds.map(
        orderLineId => `${CacheKeyManagerService.orderLineAfterSaleLine(orderLineId, ctx.channelId)}-pagination`,
      ),
      ...orderLineIds.map(
        orderLineId => `${CacheKeyManagerService.orderLineAfterSale(orderLineId, ctx.channelId)}-pagination`,
      ),
      ...orderLineIds.map(
        orderLineId => `${CacheKeyManagerService.orderLineAfterSaleLine(orderLineId, ctx.channelId)}-count`,
      ),
      ...orderLineIds.map(
        orderLineId => `${CacheKeyManagerService.orderLineAfterSale(orderLineId, ctx.channelId)}-count`,
      ),
    ];
    await this.connection.rawConnection.queryResultCache?.remove(orderLineKeys);
  }

  async getNotRefundableAfterSaleOrderLineIds(ctx: RequestContext, orderId: ID, orderLineIds: ID[]): Promise<ID[]> {
    // 获取售后订单行，如果为空则返回空数组
    const afterSales = (await this.getOrderNotCancelAndNotExchangeGoodsAfterSaleLines(ctx, orderId)) ?? [];
    // 提取售后订单行ID，如果为空则返回空数组
    const afterSaleLineIds = afterSales.flatMap(afterSale => {
      return (afterSale.afterSaleLines ?? []).map(afterSaleLine => afterSaleLine.orderLine.id);
    });
    // 过滤出没有售后的订单行ID，如果输入的 orderLineIds 为空则返回空数组
    const notRefundableOrderLineIds = (orderLineIds ?? []).filter(
      orderLineId => !afterSaleLineIds.includes(orderLineId),
    );
    return notRefundableOrderLineIds ?? [];
  }

  async getOrderIsAfterSale(ctx: RequestContext, orderId: ID): Promise<boolean> {
    const afterSaleLines = await this.getOrderNotCancelAndNotExchangeGoodsAfterSaleLines(ctx, orderId);
    // 如果afterSaleLines不存在或者长度为0则返回false
    if (!afterSaleLines || afterSaleLines.length === 0) {
      return false;
    }
    return true;
  }

  // 获取售后成功的订单行
  async getAfterSaleSuccessOrderLineIds(ctx: RequestContext, orderId: ID): Promise<ID[]> {
    const afterSales = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoinAndSelect('afterSale.order', 'order')
      .leftJoinAndSelect('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoinAndSelect('afterSaleLines.orderLine', 'orderLine')
      .andWhere('order.id = :orderId', {orderId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('afterSale.mode != :mode', {mode: AfterSaleMode.ExchangeGoods})
      .getMany();
    // 提取售后成功的订单行ID
    const afterSaleLineIds = afterSales?.flatMap(afterSale => {
      return (afterSale.afterSaleLines ?? []).map(afterSaleLine => afterSaleLine.orderLineId);
    }) as ID[];
    return afterSaleLineIds ?? [];
  }

  // 获取订单未取消和未换货的售后单行
  async getOrderNotCancelAndNotExchangeGoodsAfterSaleLines(ctx: RequestContext, orderId: ID): Promise<AfterSale[]> {
    const qb = this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoinAndSelect('afterSale.order', 'order')
      .leftJoinAndSelect('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoinAndSelect('afterSaleLines.orderLine', 'orderLine')
      .andWhere('order.id = :orderId', {orderId})
      .andWhere('afterSale.state != :state', {state: AfterSaleState.Cancel})
      .andWhere('afterSale.mode != :mode', {mode: AfterSaleMode.ExchangeGoods});
    const afterSales = await qb.getMany();
    return afterSales;
  }
  /**
   * 获取订单售后单已退邮费
   */
  async getRefundedShippingPrice(
    ctx: RequestContext,
    orderId: ID,
    orderLineIds: ID[],
    isUpdate = false,
  ): Promise<number> {
    const afterSales = await this.getOrderNotCancelAndNotExchangeGoodsAfterSaleLines(ctx, orderId);
    let totalShippingPrice = 0;
    for (const afterSale of afterSales) {
      // 当前售后订单项在订单行中的ID 则不需要加上邮费
      const afterSaleLineIds = afterSale.afterSaleLines.map(afterSaleLine => afterSaleLine.orderLine.id);
      if (isUpdate && orderLineIds.some(orderLineId => afterSaleLineIds.includes(orderLineId))) {
        continue;
      }
      if (afterSale.isIncludeShipping) {
        totalShippingPrice += Number(afterSale.refundShipping);
      }
    }
    return totalShippingPrice;
  }

  async getAfterSaleUnRecycledShoppingCredits(ctx: RequestContext, orderId: ID) {
    const qb = this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.order', 'order')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .andWhere('order.id = :orderId', {orderId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund});

    // 获取应回收购物金总和  和实际回收购物金总和
    const totalPrice = await qb
      .select(`SUM(${qb.alias}.shouldReclaimedShoppingCredits)`, 'totalPrice')
      .addSelect(`SUM(${qb.alias}.actualReclaimedShoppingCredits)`, 'actualTotalPrice')
      .addSelect(`SUM(${qb.alias}.returnedShoppingCredits)`, 'returnedShoppingCredits')
      .getRawOne<{
        totalPrice: number;
        actualTotalPrice: number;
        returnedShoppingCredits: number;
      }>();
    // const shouldReclaimedShoppingCredits =
    //   Number(totalPrice?.totalPrice ?? 0) - Number(totalPrice?.actualTotalPrice ?? 0);
    return {
      shouldReclaimedShoppingCredits: Number(totalPrice?.totalPrice ?? 0),
      actualReclaimedShoppingCredits: Number(totalPrice?.actualTotalPrice ?? 0),
      returnedShoppingCredits: Number(totalPrice?.returnedShoppingCredits ?? 0),
    };
  }

  /**
   * 获取订单售后成功的总金额
   * @param ctx
   * @param orderId
   * @returns
   */
  async getAfterSaleSuccessTotalPrice(
    ctx: RequestContext,
    orderId: ID,
    orderLineIds: ID[],
    isInclusionProgress = false,
  ): Promise<number> {
    const qb = this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoinAndSelect('afterSale.order', 'order')
      .leftJoinAndSelect('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoinAndSelect('afterSaleLines.orderLine', 'orderLine')
      .andWhere('order.id = :orderId', {orderId});
    if (isInclusionProgress) {
      qb.andWhere('afterSale.state != :state', {state: AfterSaleState.Cancel});
      qb.andWhere('afterSale.mode != :mode', {mode: AfterSaleMode.ExchangeGoods});
    } else {
      qb.andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund});
    }
    const afterSalesSuccess = await qb.getMany();
    let totalPrice = 0;
    for (const afterSale of afterSalesSuccess) {
      // 遍历售后行
      for (const afterSaleLine of afterSale.afterSaleLines) {
        // 判断是否是当前订单行
        if (orderLineIds.includes(afterSaleLine.orderLine.id)) {
          // 如果是当前订单行则直接加上售后金额
          if (afterSale.afterSaleLines.length === 1) {
            totalPrice += Number(afterSale.price);
            if (afterSale.isIncludeShipping) {
              totalPrice -= Number(afterSale.refundShipping);
            }
          } else {
            totalPrice += Number(afterSaleLine.price);
          }
        }
      }
    }
    return totalPrice;
  }

  /**
   * 获取待处理退款订单数
   * @param ctx
   * @param orderId
   * @param options
   * @param relations
   * @returns
   */
  async getNumberOfPendingRefunds(ctx: RequestContext) {
    const numberOfPendingRefunds = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('state In (:...state)', {
        state: [
          AfterSaleState.Submit,
          AfterSaleState.WaitingForDelivery,
          AfterSaleState.GoodsToBeReceived,
          AfterSaleState.ConfirmReceiptAndDelivery,
        ],
      })
      //仅退款和退货退款
      .andWhere('mode In (:...mode)', {
        mode: [AfterSaleMode.RefundOnly, AfterSaleMode.RefundForReturnedGoods],
      })
      .getCount();
    return numberOfPendingRefunds ?? 0;
  }

  /**
   * 获取指定时间段内的退款金额
   * @param ctx
   * @param orderId
   * @param options
   * @param relations
   * @returns
   */
  async getRefundAmountByDateTime(ctx: RequestContext, date?: Date, endDate?: Date, isSpecificTime?: Boolean) {
    if (!date) {
      date = new Date();
    }
    if (!endDate) {
      endDate = date;
    }
    let startDate = date;
    if (!isSpecificTime) {
      startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
      endDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59);
    }
    const totalPrice = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channels')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('afterSale.auditAt BETWEEN :startDate AND :endDate', {startDate, endDate})
      .select('SUM(afterSale.price)', 'totalPrice')
      .getRawOne();
    return Number(totalPrice?.totalPrice ?? 0);
  }

  /**
   * 获取多个用户在指定分销员下的累积退款金额
   */
  async getRefundAmountByDistributorId(ctx: RequestContext, distributorId: ID, customerIds: ID[]) {
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('order.id', 'orderId')
      .leftJoin('distributorOrder.order', 'order')
      .leftJoin('distributorOrder.distributor', 'distributor')
      .leftJoin('distributorOrder.customer', 'customer')
      .andWhere('distributor.id = :distributorId')
      .andWhere('order.id is not null')
      .andWhere('customer.id IN (:...customerIds)')
      .getQuery();
    const refundAmounts = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.order', 'order')
      .leftJoin('order.customer', 'customer')
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('order.id IN (' + subQuery + ')')
      .setParameters({distributorId, customerIds})
      .select('SUM(afterSale.price)', 'totalPrice')
      .addSelect('customer.id', 'customerId')
      .groupBy('customer.id')
      .getRawMany<{
        customerId: ID;
        totalPrice: number;
      }>();
    const refundAmountMap =
      refundAmounts.length > 0
        ? refundAmounts.reduce((prev, current) => {
            prev[current.customerId] = current;
            return prev;
          }, {} as {[key: string]: {customerId: ID; totalPrice: number}})
        : {};
    return refundAmountMap;
  }

  /**
   * 获取累积退款金额
   * @param ctx
   * @param customerId
   */
  async getCumulativeRefundAmount(ctx: RequestContext, customerId: ID): Promise<number> {
    const totalPrice = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channels')
      .leftJoin('afterSale.order', 'order')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('order.customerId = :customerId', {customerId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .select('SUM(afterSale.price)', 'totalPrice')
      .getRawOne();
    return Number(totalPrice?.totalPrice ?? 0);
  }

  /**
   * 获取累积退款订单数
   * @param ctx
   * @param customerId
   */
  async getCumulativeRefundOrderNumber(ctx: RequestContext, customerId: ID): Promise<number> {
    const orderNumber = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channels')
      .leftJoin('afterSale.order', 'order')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('order.customerId = :customerId', {customerId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .select('COUNT(DISTINCT order.id)', 'orderNumber')
      .getRawOne();
    return parseInt(orderNumber?.orderNumber ?? 0);
  }

  async afterSaleByOrderId(
    ctx: RequestContext,
    orderId: ID,
    options?: ListQueryOptions<AfterSale>,
    relations?: RelationPaths<AfterSale>,
  ) {
    const qb = this.listQueryBuilder.build(AfterSale, options, {
      ctx,
      relations: relations ?? ['channels', 'order', 'afterSaleLines', 'afterSaleLines.orderLine'],
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.order`, 'order');
    qb.andWhere('order.id = :orderId', {orderId});
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async afterSaleAudit(ctx: RequestContext, input: AfterSaleAuditInput, isTimeout = true) {
    const lock = await this.redLockService.lockResource(`AfterSale:AfterSaleAudit:${input.afterSaleId}`);
    try {
      let afterSale = await this.findOne(ctx, input.afterSaleId);
      if (!afterSale) {
        throw new Error('No corresponding after-sales information can be found');
      }
      if (afterSale.mode === AfterSaleMode.RefundOnly) {
        //仅退款只有提交申请状态下才能审核
        if (afterSale.state !== AfterSaleState.Submit) {
          throw new Error('This after-sales status does not need to be addressed');
        }
      } else if (
        //退货和换货都只能在用户提交申请或者用户寄出之后才能审核操作
        afterSale.mode === AfterSaleMode.RefundForReturnedGoods ||
        afterSale.mode === AfterSaleMode.ExchangeGoods
      ) {
        if (afterSale.state !== AfterSaleState.Submit && afterSale.state !== AfterSaleState.GoodsToBeReceived) {
          throw new Error('This after-sales status does not need to be addressed');
        }
      } else {
        Logger.error('错误的售后状态');
        throw new Error('mode error');
      }
      afterSale.auditAt = new Date();
      if (input.state === AuditState.Refuse) {
        if (!input.massage) {
          throw new Error('The reason for refusal cannot be empty');
        }
        if (afterSale.state === AfterSaleState.GoodsToBeReceived) {
          afterSale.state = AfterSaleState.RefusalOfGoods;
        } else {
          afterSale.state = AfterSaleState.RefusalOfRefund;
        }
        afterSale.groundsRefusal = input.massage;
        await this.afterSaleHistoryService.createHistory(
          ctx,
          AfterSaleHistoryType.RefusalOfRefund,
          OperatorType.Channel,
          afterSale,
          HistoryMessage.RefusalOfRefund,
        );
      } else {
        const orderLineIds = afterSale.afterSaleLines.map(afterSaleLine => {
          return {
            orderLineId: afterSaleLine.orderLine.id,
            quantity: afterSaleLine.quantity,
          };
        });
        if (afterSale.mode === AfterSaleMode.RefundOnly) {
          // afterSale.state = AfterSaleState.AgreeToRefund;
          afterSale.state = AfterSaleState.SuccessfulRefund;
          // 需要回收的购物金
          const shoppingCredits = afterSale.reclaimedShoppingCredits;
          afterSale = await this.afterSaleOperationShoppingCredits(ctx, afterSale);
          // 实际回收的购物金
          const actualShoppingCredits = afterSale.reclaimedShoppingCredits;
          afterSale.actualReclaimedShoppingCredits = actualShoppingCredits;
          // 未回收的购物金
          const unclaimedShoppingCredits = shoppingCredits - actualShoppingCredits;
          let differenceDescription = ``;
          let afterSalePrice = afterSale.price;
          if (unclaimedShoppingCredits > 0 && afterSalePrice > 0) {
            afterSalePrice = afterSalePrice - unclaimedShoppingCredits;
            differenceDescription = `购物金不足已回收,实际回收购物金${actualShoppingCredits / 100}, 退款金额${
              afterSalePrice / 100
            },金额抵扣购物金${unclaimedShoppingCredits / 100}`;
            // 如果有未回收的购物金则需要更新售后价格
            afterSale.price = afterSalePrice;
          }
          // 根据订单id和lineId调用退款
          if (afterSalePrice) {
            await this.orderCustomService.createARefundAccordingToOrderLineIds(
              ctx,
              afterSale.order.id,
              orderLineIds,
              afterSalePrice,
            );
          }

          // 是否包含盲盒退款  afterSaleLines中只要有一个包含盲盒退款就需要退盲盒
          const isBlindBoxRefund = afterSale.afterSaleLines.some(afterSaleLine => {
            return afterSaleLine.isBlindBox;
          });
          if (isBlindBoxRefund) {
            await this.blindBoxOrderService.refundBlindBoxOrderByOrderLineIds(ctx, orderLineIds);
          }
          await this.afterSaleHistoryService.createHistory(
            ctx,
            AfterSaleHistoryType.AgreeToRefundOnly,
            OperatorType.Channel,
            afterSale,
            HistoryMessage.AgreeToRefundOnly,
            unclaimedShoppingCredits,
            differenceDescription,
          );
        } else if (
          //退货退款或者是换货
          afterSale.mode === AfterSaleMode.RefundForReturnedGoods ||
          afterSale.mode === AfterSaleMode.ExchangeGoods
        ) {
          //售后当前状态是申请状态 不管是退货还是换货都是发送收货地址给用户
          if (afterSale.state === AfterSaleState.Submit) {
            if (!input.channelAddressId && !isTimeout) {
              throw new Error('Agree to return the goods and refund the refund address');
            }
            const channelAddress = await this.channelAddressService.findOne(ctx, input.channelAddressId || undefined);
            if (!channelAddress) {
              throw new Error('channelAddress not exist');
            }
            const afterSaleChannelAddress = {
              fullName: channelAddress.fullName,
              company: channelAddress.company,
              streetLine1: channelAddress.streetLine1,
              streetLine2: channelAddress.streetLine2,
              city: channelAddress.city,
              province: channelAddress.province,
              postalCode: channelAddress.postalCode,
              country: channelAddress.country.name,
              countryCode: channelAddress.country.code,
              phoneNumber: channelAddress.phoneNumber,
              district: channelAddress.district,
            };
            afterSale.channelAddress = afterSaleChannelAddress;
            afterSale.state = AfterSaleState.WaitingForDelivery;
            await this.afterSaleHistoryService.createHistory(
              ctx,
              AfterSaleHistoryType.ShippingReturnAddress,
              OperatorType.Channel,
              afterSale,
              HistoryMessage.ShippingReturnAddress,
            );
          } else if (
            //当前状态是待商家收货状态并且是退货退款  执行退款逻辑
            afterSale.state === AfterSaleState.GoodsToBeReceived &&
            afterSale.mode === AfterSaleMode.RefundForReturnedGoods
          ) {
            // afterSale.state = AfterSaleState.AgreeToRefund;
            afterSale.state = AfterSaleState.SuccessfulRefund;
            // 需要回收的购物金
            const shoppingCredits = afterSale.reclaimedShoppingCredits;
            afterSale = await this.afterSaleOperationShoppingCredits(ctx, afterSale);
            // 实际回收的购物金
            const actualShoppingCredits = afterSale.reclaimedShoppingCredits;
            afterSale.actualReclaimedShoppingCredits = actualShoppingCredits;
            // 未回收的购物金
            const unclaimedShoppingCredits = shoppingCredits - actualShoppingCredits;
            let differenceDescription = ``;
            let afterSalePrice = afterSale.price;
            if (unclaimedShoppingCredits > 0 && afterSalePrice > 0) {
              afterSalePrice = afterSalePrice - unclaimedShoppingCredits;
              differenceDescription = `购物金不足已回收,实际回收购物金${actualShoppingCredits / 100}, 退款金额${
                afterSalePrice / 100
              },金额抵扣购物金${unclaimedShoppingCredits / 100}`;
              // 如果有未回收的购物金则需要更新售后价格
              afterSale.price = afterSalePrice;
            }
            if (afterSalePrice) {
              // 根据订单id调用退款
              await this.orderCustomService.createARefundAccordingToOrderLineIds(
                ctx,
                afterSale.order.id,
                orderLineIds,
                afterSalePrice,
              );
            }
            // 是否包含盲盒退款  afterSaleLines中只要有一个包含盲盒退款就需要退盲盒
            const isBlindBoxRefund = afterSale.afterSaleLines.some(afterSaleLine => {
              return afterSaleLine.isBlindBox;
            });
            if (isBlindBoxRefund) {
              await this.blindBoxOrderService.refundBlindBoxOrderByOrderLineIds(ctx, orderLineIds);
            }

            await this.afterSaleHistoryService.createHistory(
              ctx,
              AfterSaleHistoryType.ConfirmReceiptAndRefund,
              OperatorType.Channel,
              afterSale,
              HistoryMessage.ConfirmReceiptAndRefund,
              unclaimedShoppingCredits,
              differenceDescription,
            );
          } else if (
            //当前状态是待商家收货状态并且是换货
            afterSale.state === AfterSaleState.GoodsToBeReceived &&
            afterSale.mode === AfterSaleMode.ExchangeGoods
          ) {
            //添加商家发货物流
            return await this.addExchangeGoodsDeliver(ctx, input);
          }
        } else {
          throw new Error('mode error');
        }
      }
      await this.connection.getRepository(ctx, AfterSale).save(afterSale);
      await this.removeUserAfterSaleProductLimitCache(ctx, afterSale.order.customerId as ID, afterSale);
      this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
      return await this.connection.getRepository(ctx, AfterSale).findOneOrFail({where: {id: afterSale.id}});
    } catch (error) {
      Logger.error(error.message);
      throw new Error(error.message);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  // 根据售后重新计算购物金
  async recalculateShoppingCredits(ctx: RequestContext, afterSale: AfterSale) {
    const orderId = afterSale.orderId as ID;
    // 获取订单项发放的购物金
    const shoppingCreditsClaims = await this.shoppingCreditsClaimActivityService.getOrderItemShoppingCreditsClaim(
      ctx,
      orderId,
    );
    // 获取订单项抵扣的购物金
    const shoppingCreditsDeductions =
      await this.shoppingCreditsDeductionActivityService.getOrderItemShoppingCreditsDeduction(ctx, orderId);
    const afterSaleLines = afterSale.afterSaleLines;
    let shoppingCreditsClaim = 0;
    let shoppingCreditsDeduction = 0;
    for (const afterSaleLine of afterSaleLines) {
      const orderLine = afterSaleLine.orderLine;
      if (!orderLine) {
        continue;
      }
      const shoppingCreditsClaimItem = shoppingCreditsClaims.find(item =>
        idsAreEqual(item.orderLineId as ID, orderLine.id),
      );
      const shoppingCreditsDeductionItem = shoppingCreditsDeductions.find(item =>
        idsAreEqual(item.orderLineId as ID, orderLine.id),
      );
      if (shoppingCreditsClaimItem) {
        const reclaimedShoppingCredits = Number(shoppingCreditsClaimItem.shoppingCreditsClaim ?? 0);
        shoppingCreditsClaim += reclaimedShoppingCredits;
        await this.connection.getRepository(ctx, AfterSaleLine).update(
          {
            id: afterSaleLine.id,
          },
          {
            reclaimedShoppingCredits: reclaimedShoppingCredits,
          },
        );
      }
      if (shoppingCreditsDeductionItem) {
        const returnedShoppingCredits = Number(shoppingCreditsDeductionItem.shoppingCreditsDeductionAmount ?? 0);
        shoppingCreditsDeduction += returnedShoppingCredits;
        await this.connection.getRepository(ctx, AfterSaleLine).update(
          {
            id: afterSaleLine.id,
          },
          {
            returnedShoppingCredits: returnedShoppingCredits,
          },
        );
      }
    }
    afterSale.returnedShoppingCredits = shoppingCreditsDeduction;
    afterSale.reclaimedShoppingCredits = shoppingCreditsClaim;
    return afterSale;
  }

  // 售后购物金操作 先退回用户购物金 再回收购物金 如果用户的最大购物金小于回收购物金则只回收用户的最大购物金
  async afterSaleOperationShoppingCredits(ctx: RequestContext, afterSale: AfterSale) {
    // 重新获取订单项可售后的购物金
    afterSale = await this.recalculateShoppingCredits(ctx, afterSale);

    // 是否需要退购物金或者回收购物
    if (afterSale.reclaimedShoppingCredits || afterSale.returnedShoppingCredits) {
      if (afterSale.returnedShoppingCredits) {
        // 执行退回购物金
        await this.virtualCurrencyService.addCurrency(
          ctx,
          VirtualCurrencyCode.ShoppingCredits,
          afterSale.returnedShoppingCredits,
          '售后退回购物金',
          VirtualCurrencySourceType.Returned,
          afterSale.id as string,
          afterSale.order.customerId,
        );
      }
      const customerShoppingCredits = await this.virtualCurrencyService.getBalance(
        ctx,
        VirtualCurrencyCode.ShoppingCredits,
        afterSale.order.customerId,
      );
      // 回收购物金只能回收到用户的最大购物金
      const minShoppingCredits = Math.min(customerShoppingCredits, afterSale.reclaimedShoppingCredits);
      afterSale.reclaimedShoppingCredits = minShoppingCredits;
      if (afterSale.reclaimedShoppingCredits) {
        // 执行回收购物金
        await this.virtualCurrencyService.subtractCurrency(
          ctx,
          VirtualCurrencyCode.ShoppingCredits,
          afterSale.reclaimedShoppingCredits,
          '售后回收购物金',
          VirtualCurrencySourceType.Reclaimed,
          afterSale.id as string,
          afterSale.order.customerId,
        );
      }
    }
    return afterSale;
  }

  // 移除用户售后商品限购缓存
  async removeUserAfterSaleProductLimitCache(ctx: RequestContext, customerId: ID, afterSale: AfterSale) {
    const productIds = afterSale?.afterSaleLines.map(
      afterSaleLine => afterSaleLine?.orderLine?.productVariant?.productId,
    );
    if (!productIds?.length) {
      return;
    }
    for (const productId of productIds) {
      await this.kvsService.productLimitation.del(`${customerId}_${productId}`);
      const pointsExchangeInfo = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
      if (pointsExchangeInfo) {
        await this.kvsService.productLimitation.del(`Exchangeable:${customerId}_${productId}_${pointsExchangeInfo.id}`);
      }
    }
  }

  /**
   * 商家添加发货物流
   * @param ctx 、
   * @param input
   * @returns
   */
  async addExchangeGoodsDeliver(ctx: RequestContext, input: AfterSaleAuditInput) {
    const {logisticCode, company, afterSaleId} = input;
    if (!logisticCode || !company) {
      throw new Error('The logistics information cannot be empty');
    }
    const afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('after sale not exist');
    }
    const logisticCodeSF = await this.getNewLogisticsCode(ctx, company, logisticCode, afterSale);
    const resultInfo = await this.logisticsService.getLogisticInfo(logisticCodeSF);
    const state = this.logisticsService.stateTransition(resultInfo.State);
    if (state === LogisticState.CodeError) {
      throw new Error(resultInfo.Reason);
    }
    let afterSaleLogistics = new AfterSaleLogistics({
      order: afterSale.order,
      afterSale: afterSale,
      logisticCode: logisticCode,
      type: LogisticsType.DeliverGoods,
      company: company,
      state,
      lastUpdateTime: new Date(),
    });
    afterSaleLogistics = await this.connection.getRepository(ctx, AfterSaleLogistics).save(afterSaleLogistics);
    const logisticInfo = this.logisticsService.infoTransition(resultInfo, state);
    await this.connection
      .getRepository(ctx, AfterSaleLogistics)
      .update(afterSaleLogistics.id, {logisticInfo: logisticInfo});
    await this.connection.getRepository(ctx, AfterSale).update(afterSale.id, {
      auditAt: new Date(),
      merchantDeliveryTime: new Date(),
      state: AfterSaleState.ConfirmReceiptAndDelivery,
    });
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.ConfirmReceiptAndDelivery,
      OperatorType.Platform,
      afterSale,
      HistoryMessage.ConfirmReceiptAndDelivery,
    );
    return this.connection.getRepository(ctx, AfterSale).findOneOrFail({where: {id: afterSale.id}});
  }

  async getNewLogisticsCode(ctx: RequestContext, company: string, logisticCode: string, afterSale: AfterSale) {
    let logisticCodeSF = logisticCode;
    // if (logisticCodeSF.substring(0, 2).toLocaleUpperCase() === 'SF') {
    const order = await this.orderService.findOne(ctx, afterSale.order.id);
    const shippingAddress = order?.shippingAddress;
    if (shippingAddress) {
      const phoneNumber = shippingAddress.phoneNumber;
      if (phoneNumber) {
        logisticCodeSF = `${logisticCodeSF}:${phoneNumber.slice(-4)}`;
      }
    }
    // }
    return logisticCodeSF;
  }

  async platformRefund(ctx: RequestContext, input: PlatformAfterSaleInput) {
    const {orderId, description, price, platformAfterLines} = input;
    const afterSaleInput = {
      orderId,
      mode: AfterSaleMode.RefundOnly,
      cause: '平台主动退款',
      description,
      afterSaleLines: platformAfterLines,
      price,
    };
    const order = await this.verifyTheOrder(ctx, afterSaleInput);
    // const type = this.getAfterSaleTypeByOrder(order);
    let afterSale = new AfterSale({
      ...afterSaleInput,
      order,
      type: AfterSaleType.MerchantVoluntaryRefund,
      applyAt: new Date(),
      state: AfterSaleState.SuccessfulRefund,
      code: generatePublicId(),
    });
    afterSale = await this.channelService.assignToCurrentChannel(afterSale, ctx);
    afterSale = await this.connection.getRepository(ctx, AfterSale).save(afterSale);
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.ConfirmReceiptAndRefund,
      OperatorType.Platform,
      afterSale,
      HistoryMessage.PlatformRefund,
    );
    const afterSaleLines = await this.createAnAftermarketOrderLine(ctx, afterSale, platformAfterLines);
    afterSale.afterSaleLines = afterSaleLines;
    await this.connection.getRepository(ctx, AfterSale).save(afterSale);
    const orderLineIds = afterSale.afterSaleLines.map(afterSaleLine => {
      return {
        orderLineId: afterSaleLine.orderLine.id,
        quantity: afterSaleLine.quantity,
      };
    });
    await this.orderCustomService.createARefundAccordingToOrderLineIds(
      ctx,
      afterSale.order.id,
      orderLineIds,
      afterSale.price,
    );
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
    return this.findOne(ctx, afterSale.id);
  }

  async createAfterSale(ctx: RequestContext, input: AfterSaleInput) {
    const lock = await this.redLockService.lockResource(`AfterSale:CreateAfterSale:${input.orderId}`);
    try {
      // this.validate(ctx, input);
      const order = await this.verifyTheOrder(ctx, input);
      const orderLineIds = input.afterSaleLines.map(item => Number(item.lineId));
      const type = this.getAfterSaleTypeByOrder(order);
      // 不包含邮费的可退金额
      const refundableAmount = await this.orderPromotionResultService.getRefundableAmount(
        ctx,
        order.id,
        orderLineIds,
        false,
      );
      // 是否包含邮费
      let isIncludeShipping = false;
      let shippingPrice = 0;
      if (refundableAmount < Number(input.price)) {
        isIncludeShipping = true;
        shippingPrice = Number(input.price) - Number(refundableAmount);
      }
      let afterSale = new AfterSale({
        ...(input as unknown as AfterSale),
        order,
        type,
        applyAt: new Date(),
        state: AfterSaleState.Submit,
        code: this.generateDateString(),
        isIncludeShipping,
        refundShipping: shippingPrice,
      });
      afterSale = await this.channelService.assignToCurrentChannel(afterSale, ctx);
      afterSale = await this.connection.getRepository(ctx, AfterSale).save(afterSale);
      const afterSaleLines = await this.createAnAftermarketOrderLine(ctx, afterSale, input.afterSaleLines);
      afterSale.afterSaleLines = afterSaleLines;
      afterSale.reclaimedShoppingCredits = afterSaleLines.reduce((total, line) => {
        return total + Number(line.reclaimedShoppingCredits || 0);
      }, 0);
      afterSale.shouldReclaimedShoppingCredits = afterSale.reclaimedShoppingCredits;
      afterSale.returnedShoppingCredits = afterSaleLines.reduce((total, line) => {
        return total + Number(line.returnedShoppingCredits || 0);
      }, 0);
      await this.connection.getRepository(ctx, AfterSale).save(afterSale);
      await this.afterSaleHistoryService.createHistory(
        ctx,
        AfterSaleHistoryType.Submit,
        OperatorType.User,
        afterSale,
        HistoryMessage.Create,
      );
      this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'created'));
      return await this.findOne(ctx, afterSale.id);
    } catch (error) {
      Logger.error(error.message);
      throw new Error(error.message);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async createAnAftermarketOrderLine(
    ctx: RequestContext,
    afterSale: AfterSale,
    afterSaleLinesInput: AfterSaleLineInput[],
  ) {
    const orderId = afterSale.orderId as ID;
    const orderPromotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    if (!orderPromotionResult) {
      throw new Error(`订单优惠信息未找到`);
    }
    const promLineResults = orderPromotionResult.promResult?.promLineResults;
    // 获取类型为BlindBox的盲盒优惠信息
    const blindBoxResult = promLineResults?.find(item => item?.type === PromotionType.BlindBox);
    const orderLineBlindBoxMap = blindBoxResult?.orderLineBlindBoxMap;
    const afterSaleLines = [];
    // 获取订单项发放的购物金
    const shoppingCreditsClaims =
      afterSale.mode === AfterSaleMode.ExchangeGoods
        ? []
        : await this.shoppingCreditsClaimActivityService.getOrderItemShoppingCreditsClaim(ctx, orderId);
    // 获取订单项抵扣的购物金
    const shoppingCreditsDeductions =
      afterSale.mode === AfterSaleMode.ExchangeGoods
        ? []
        : await this.shoppingCreditsDeductionActivityService.getOrderItemShoppingCreditsDeduction(ctx, orderId);
    for (const afterSaleLineInput of afterSaleLinesInput) {
      const line = await this.connection
        .getRepository(ctx, OrderLine)
        .findOne({where: {id: afterSaleLineInput.lineId}});
      if (!line) {
        throw new Error(`order line not exist`);
      }
      let isBlindBox = false;
      let blindBoxPrice = 0;
      if (orderLineBlindBoxMap) {
        for (const item of orderLineBlindBoxMap) {
          if (item && idsAreEqual(item.orderLineId ?? undefined, line.id)) {
            isBlindBox = true;
            blindBoxPrice += item.blindBoxOrderBuyPrice ?? 0;
          }
        }
      }
      // 根据订单项id获取发放的购物金
      const shoppingCreditsClaim =
        shoppingCreditsClaims?.find(item => {
          return idsAreEqual(item.orderLineId as ID, line.id);
        })?.shoppingCreditsClaim ?? 0;
      // 根据订单项id获取抵扣的购物金
      const shoppingCreditsDeduction =
        shoppingCreditsDeductions?.find(item => {
          return idsAreEqual(item.orderLineId as ID, line.id);
        })?.shoppingCreditsDeductionAmount ?? 0;
      let afterSaleLine = new AfterSaleLine({
        orderLine: line,
        price: afterSaleLineInput.price ?? line.unitPrice,
        quantity: afterSaleLineInput.quantity ?? (line.quantity || line.orderPlacedQuantity),
        afterSale,
        isBlindBox,
        blindBoxPrice,
        reclaimedShoppingCredits: shoppingCreditsClaim,
        returnedShoppingCredits: shoppingCreditsDeduction,
      });
      afterSaleLine = await this.connection.getRepository(ctx, AfterSaleLine).save(afterSaleLine);
      await this.connection
        .getRepository(ctx, OrderLine)
        .update(line.id, {customFields: {afterSaleLine: afterSaleLine, afterSale: afterSale}});
      afterSaleLines.push(afterSaleLine);
    }
    return afterSaleLines;
  }
  async updateAfterSale(ctx: RequestContext, input: AfterSaleInput) {
    const afterSaleId = input.id;
    if (!afterSaleId) {
      throw new Error('after sale id can not be empty ');
    }
    let afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('afterSale not exist');
    }
    if (afterSale.state !== AfterSaleState.Submit && afterSale.state !== AfterSaleState.RefusalOfRefund) {
      throw new Error('The current after-sales status cannot be modified');
    }
    await this.verifyTheOrder(ctx, input, true);
    const order = await this.connection.findOneInChannel(ctx, Order, input.orderId, ctx.channelId);
    if (!order) {
      throw new Error('order not exist');
    }
    const afterSaleLines = input.afterSaleLines;
    const oldAfterSaleLines = afterSale.afterSaleLines;
    for (const oldAfterSaleLine of oldAfterSaleLines) {
      const oldAfterSaleLineId = oldAfterSaleLine.id;
      const oldAfterSaleLineIndex = afterSaleLines.findIndex(item => item.lineId === oldAfterSaleLine.orderLine.id);
      if (oldAfterSaleLineIndex === -1) {
        throw new Error('The order line cannot be empty');
      }
      const oldAfterSaleLineInput = afterSaleLines[oldAfterSaleLineIndex];
      await this.connection.getRepository(ctx, AfterSaleLine).update(oldAfterSaleLineId, {
        price: oldAfterSaleLineInput.price ?? oldAfterSaleLine.price,
        quantity: oldAfterSaleLineInput.quantity ?? oldAfterSaleLine.quantity,
      });
    }
    const oldAfterSaleMode = afterSale.mode;
    // 当修改了售后类型并且修改为换货的时候 退款金额和是否包含邮费需要重置
    if (oldAfterSaleMode !== input.mode) {
      if (input.mode === AfterSaleMode.ExchangeGoods) {
        afterSale.isIncludeShipping = false;
        afterSale.refundShipping = 0;
        afterSale.price = 0;
      }
    }
    // 当修改后的售后类型不是换货的时候 重新计算是否包含邮费和退邮费金额
    if (input.mode !== AfterSaleMode.ExchangeGoods) {
      const refundLineIds = afterSaleLines.map(item => Number(item.lineId));
      // 不包含邮费的可退金额
      const refundableAmount = await this.orderPromotionResultService.getRefundableAmount(
        ctx,
        order.id,
        refundLineIds,
        false,
      );
      // 如果不包含邮费的可退金额小于退款金额 则是需要退邮费
      if (refundableAmount < Number(input.price)) {
        afterSale.isIncludeShipping = true;
        afterSale.refundShipping = Number(input.price) - Number(refundableAmount);
      } else {
        // 不需要退邮费
        afterSale.isIncludeShipping = false;
        afterSale.refundShipping = 0;
      }
    }
    const type = this.getAfterSaleTypeByOrder(order);
    afterSale.state = AfterSaleState.Submit;
    afterSale.mode = input.mode;
    afterSale.type = type;
    afterSale.cause = input.cause;
    afterSale.description = input.description;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    afterSale.img = input.img as any;
    afterSale.price = input.price;
    afterSale.applyAt = new Date();
    afterSale = await this.connection.getRepository(ctx, AfterSale).save(afterSale);
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.UpdateSubmit,
      OperatorType.User,
      afterSale,
      HistoryMessage.UpdateSubmit,
    );
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'created'));
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
    return this.findOne(ctx, afterSale.id);
  }
  // validate(ctx: RequestContext, input: AfterSaleInput) {}
  async verifyTheOrder(ctx: RequestContext, input: AfterSaleInput, isUpdate = false) {
    const order = await this.connection.findOneInChannel(ctx, Order, input.orderId, ctx.channelId);
    if (!order) {
      throw new Error('order not exist');
    }
    if (
      order.state === OrderState.Created ||
      order.state === OrderState.Draft ||
      order.state === OrderState.AddingItems ||
      order.state === OrderState.ArrangingPayment ||
      order.state === OrderState.PaymentAuthorized ||
      order.state === OrderState.Cancelled
    ) {
      throw new Error('You cannot apply for a refund under the current order status');
    }
    const afterSaleLines = input.afterSaleLines;
    const refundLineIds = afterSaleLines.map(item => Number(item.lineId));
    // 先判读总的退款金额是否大于订单金额
    // 获取可退款金额
    const refundableAmount = await this.orderPromotionResultService.getRefundableAmount(
      ctx,
      order.id,
      refundLineIds,
      true,
      isUpdate,
    );
    // 先查询订单全部可退金额
    const refundableAmountAll = await this.orderPromotionResultService.getRefundableAmountByOrderId(
      ctx,
      order.id,
      isUpdate,
    );
    // 获取当前refundLineIds在订单中的可退款金额总和
    let refundableAmountTotal = 0;
    refundableAmountAll.forEach(item => {
      if (refundLineIds.includes(Number(item.orderLine.id))) {
        refundableAmountTotal += item.refundableAmount;
      }
    });
    if (input.price > refundableAmountTotal) {
      if (input.price > refundableAmount) {
        throw new Error(
          `退款总金额不能大于最大可退款金额:${refundableAmountTotal / 100},refundableAmount:${refundableAmount / 100}`,
        );
      }
    }
    // 是否是批量退款 如果是批量退款则判断每个售后行的可退款金额是否大于实际退款金额
    if (refundLineIds.length > 1) {
      let totalPrice = 0;
      afterSaleLines.sort((a, b) => Number(a.lineId) - Number(b.lineId));
      for (const afterSaleLine of afterSaleLines) {
        // 获取可退款金额
        const orderLineAmount =
          refundableAmountAll.find(item => Number(item.orderLine.id) === Number(afterSaleLine.lineId))
            ?.refundableAmount ?? 0;
        // afterSaleLine.price 不能为空也不能小于0  但是可以等于0
        if ((!afterSaleLine.price && afterSaleLine.price !== 0) || afterSaleLine.price < 0) {
          throw new Error(`退款金额不能小于0`);
        }
        if (afterSaleLine.price > orderLineAmount) {
          // 实际退款金额不能大于最大可退款金额
          throw new Error(`退款单项金额不能大于对应的最大可退款金额:${orderLineAmount / 100}`);
        }
        totalPrice += afterSaleLine.price;
      }
      if (totalPrice !== input.price) {
        throw new Error(`退款总金额不等于退款单项金额之和`);
      }
    }
    const totalRefundAmount = input.afterSaleLines.reduce((total, item) => {
      return total + (item?.price || 0);
    }, 0);
    if (totalRefundAmount !== input.price) {
      throw new Error('The refund amount is incorrect');
    }
    if (input.afterSaleLines.length <= 0) {
      throw new Error('The order line cannot be empty');
    }
    if (!isUpdate) {
      const afterSales = await this.connection
        .getRepository(ctx, AfterSale)
        .createQueryBuilder('afterSale')
        .leftJoinAndSelect('afterSale.afterSaleLines', 'afterSaleLines')
        .leftJoinAndSelect('afterSaleLines.orderLine', 'orderLine')
        .andWhere('afterSale.orderId = :orderId', {orderId: order.id})
        .andWhere('afterSale.state != :state', {state: AfterSaleState.Cancel})
        .getMany();
      for (const afterSale of afterSales) {
        for (const afterSaleLine of afterSale.afterSaleLines) {
          //获取提交的售后行id
          const lineIds = input.afterSaleLines.map(item => item.lineId.toString());
          //判断提交的售后行id是否包含已经提交的售后行id(没有关闭的状态)
          if (lineIds.includes(afterSaleLine.orderLine.id.toString())) {
            throw new Error('The order has been applied for after-sales');
          }
        }
      }
    }
    return order;
  }

  async fillOutReturnLogistics(ctx: RequestContext, input: ReturnLogisticsInput) {
    const {logisticCode, company, afterSaleId} = input;
    const afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('after sale not exist');
    }
    if (afterSale.state !== AfterSaleState.WaitingForDelivery && afterSale.state !== AfterSaleState.GoodsToBeReceived) {
      throw new Error('The return logistics cannot be set in the current state');
    }
    const oldAfterSaleLogistics = await this.connection.getRepository(ctx, AfterSaleLogistics).findOne({
      where: {
        afterSale: {id: afterSale.id},
        type: LogisticsType.ReturnGoods,
      },
    });
    if (oldAfterSaleLogistics) {
      throw new Error('The return logistics has been filled in');
    }
    const logisticCodeSF = await this.getNewLogisticsCode(ctx, company, logisticCode, afterSale);
    const resultInfo = await this.logisticsService.getLogisticInfo(logisticCodeSF);
    const state = this.logisticsService.stateTransition(resultInfo.State);
    if (state === LogisticState.CodeError) {
      throw new Error(resultInfo.Reason);
    }
    let afterSaleLogistics = new AfterSaleLogistics({
      order: afterSale.order,
      afterSale: afterSale,
      logisticCode: logisticCode,
      company: company,
      state,
      lastUpdateTime: new Date(),
    });
    afterSaleLogistics = await this.connection.getRepository(ctx, AfterSaleLogistics).save(afterSaleLogistics);
    const logisticInfo = this.logisticsService.infoTransition(resultInfo, state);
    await this.connection
      .getRepository(ctx, AfterSaleLogistics)
      .update(afterSaleLogistics.id, {logisticInfo: logisticInfo});
    await this.connection
      .getRepository(ctx, AfterSale)
      .update(afterSale.id, {deliverAt: new Date(), state: AfterSaleState.GoodsToBeReceived});
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.FillOutReturnLogistics,
      OperatorType.User,
      afterSale,
      HistoryMessage.FillOutReturnLogistics,
    );
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
    return this.findOne(ctx, afterSale.id);
  }

  async updateReturnLogistics(ctx: RequestContext, input: ReturnLogisticsInput) {
    const {logisticCode, company, afterSaleId} = input;
    const afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('after sale not exist');
    }
    //只有售后状态为待收货或者售后拒绝的时候才能修改物流信息
    if (afterSale.state !== AfterSaleState.GoodsToBeReceived && afterSale.state !== AfterSaleState.RefusalOfGoods) {
      throw new Error('The return logistics cannot be set in the current state');
    }
    const logisticCodeSF = await this.getNewLogisticsCode(ctx, company, logisticCode, afterSale);
    const resultInfo = await this.logisticsService.getLogisticInfo(logisticCodeSF);
    const state = this.logisticsService.stateTransition(resultInfo.State);
    if (state === LogisticState.CodeError) {
      throw new Error(resultInfo.Reason);
    }
    afterSale.state = AfterSaleState.GoodsToBeReceived;
    await this.connection.getRepository(ctx, AfterSale).save(afterSale);
    const afterSaleLogistics = await this.connection.getRepository(ctx, AfterSaleLogistics).findOne({
      where: {
        afterSale: {id: afterSale.id},
        type: LogisticsType.ReturnGoods,
      },
    });
    if (!afterSaleLogistics) {
      throw new Error('after sale logistics not exist');
    }
    const logisticInfo = this.logisticsService.infoTransition(resultInfo, state);
    await this.connection.getRepository(ctx, AfterSaleLogistics).update(afterSaleLogistics.id, {
      logisticCode: logisticCode,
      company: company,
      logisticInfo: logisticInfo,
      lastUpdateTime: new Date(),
      state,
    });
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.UpdateReturnLogistics,
      OperatorType.User,
      afterSale,
      HistoryMessage.UpdateReturnLogistics,
    );
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
    return this.findOne(ctx, afterSale.id);
  }

  getAfterSaleTypeByOrder(order: Order) {
    switch (order.state as OrderState) {
      case OrderState.PaymentSettled:
      case OrderState.PartiallyShipped:
      case OrderState.Shipped:
      case OrderState.PartiallyDelivered:
        return AfterSaleType.RefundOnSale;
      case OrderState.Delivered:
      case OrderState.Modifying:
      case OrderState.ArrangingAdditionalPayment:
      case OrderState.ConfirmReceiptOfGoods:
      case OrderState.ReviewSettled:
        return AfterSaleType.RefundAfterSale;
      default:
        throw new Error('order state error');
    }
  }

  async findAll(
    ctx: RequestContext,
    orderCode: string,
    productName: string,
    options: ListQueryOptions<AfterSale>,
    relations: RelationPaths<AfterSale>,
    userId?: ID,
  ) {
    const qb = this.listQueryBuilder.build(AfterSale, options, {
      ctx,
      relations: relations ?? ['channels', 'order', 'afterSaleLines', 'afterSaleLines.orderLine'],
      channelId: ctx.channelId,
    });
    if (orderCode) {
      qb.leftJoinAndSelect(`${qb.alias}.order`, 'order').andWhere('order.code =:orderCode', {orderCode});
    }
    if (userId) {
      const customer = await this.customerService.findOneByUserId(ctx, userId);
      qb.leftJoinAndSelect(`${qb.alias}.order`, 'order').andWhere(`order.customerId = :customerId`, {
        customerId: customer?.id,
      });
    }
    if (productName) {
      qb.leftJoinAndSelect(`${qb.alias}.afterSaleLines`, 'afterSaleLines')
        .leftJoinAndSelect('afterSaleLines.orderLine', 'lines')
        .leftJoinAndSelect(`lines.productVariant`, 'productVariant')
        .leftJoinAndSelect('productVariant.translations', 'translations')
        .andWhere(`translations.name like :productName`, {productName: `%${productName}%`});
    }
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async findOne(
    ctx: RequestContext,
    afterSaleId?: ID,
    options?: ListQueryOptions<AfterSale>,
    relations?: RelationPaths<AfterSale>,
  ) {
    const qb = this.listQueryBuilder.build(AfterSale, options, {
      ctx,
      relations: relations ?? [
        'channels',
        'order',
        'afterSaleLines',
        'afterSaleLines.orderLine',
        'afterSaleLines.orderLine.productVariant',
      ],
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.id=:afterSaleId`, {afterSaleId: afterSaleId});
    const afterSale = await qb.getMany();
    if (afterSale.length > 0) {
      return afterSale[0];
    }
    return null;
  }

  async cancelAfterSale(ctx: RequestContext, afterSaleId: ID) {
    let afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('after sale not exist');
    }
    if (
      afterSale.state === AfterSaleState.AgreeToRefund ||
      afterSale.state === AfterSaleState.ConfirmReceiptAndDelivery ||
      afterSale.state === AfterSaleState.SuccessfulRefund ||
      afterSale.state === AfterSaleState.Complete ||
      afterSale.state === AfterSaleState.Cancel
    ) {
      throw new OperationError('当前售后状态不能取消');
    }
    afterSale.state = AfterSaleState.Cancel;
    afterSale.cancelAt = new Date();
    afterSale = await this.connection.getRepository(ctx, AfterSale).save(afterSale);
    await this.orderLineCancelAfterSale(ctx, afterSale);
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.CancelAfterSale,
      OperatorType.User,
      afterSale,
      HistoryMessage.CancelAfterSale,
    );
    this.eventBus.publish(new AfterSaleEvent(ctx, afterSale, 'updated'));
    return afterSale;
  }
  async orderLineCancelAfterSale(ctx: RequestContext, afterSale: AfterSale) {
    const aftersaleLines = afterSale.afterSaleLines;
    for (const afterSaleLine of aftersaleLines) {
      const orderLine = afterSaleLine.orderLine;
      await this.connection
        .getRepository(ctx, OrderLine)
        .update(orderLine.id, {customFields: {afterSaleLine: null, afterSale: null}});
    }
  }

  async afterSaleTimeout() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const afterSales = await this.connection.getRepository(adminCtx, AfterSale).find({
      where: {
        state: In([
          AfterSaleState.Submit,
          AfterSaleState.WaitingForDelivery,
          AfterSaleState.GoodsToBeReceived,
          AfterSaleState.ConfirmReceiptAndDelivery,
          AfterSaleState.RefusalOfRefund,
          AfterSaleState.RefusalOfGoods,
        ]),
      },
      relations: ['order', 'order.customer', 'channels'],
    });
    for (const afterSale of afterSales) {
      try {
        const customerId = afterSale.order.customer?.id;
        if (!customerId) {
          continue;
        }
        const channels = afterSale.channels;
        const customer = await this.customerService.findOne(adminCtx, customerId);
        if (!customer || !channels) {
          continue;
        }

        const ctx = await this.commonService.getCtxByCustomerAndChannels(channels, customer);
        if (!ctx) {
          continue;
        }
        if (afterSale.state === AfterSaleState.Submit) {
          //未审核的售后检查是否超时并处理
          await this.inspectionTimeoutAudit(ctx, afterSale);
        } else if (afterSale.state === AfterSaleState.WaitingForDelivery) {
          //未填写物流信息的订单检查超时
          await this.deliveryTimeout(ctx, afterSale);
        } else if (
          afterSale.state === AfterSaleState.GoodsToBeReceived &&
          afterSale.mode === AfterSaleMode.RefundForReturnedGoods
        ) {
          //退货退款超时商家未确认收货
          await this.confirmReceiptTimeout(ctx, afterSale);
        } else if (afterSale.state === AfterSaleState.ConfirmReceiptAndDelivery) {
          //换货超时用户未确认收货
          await this.confirmReceiptAndDeliveryTimeout(ctx, afterSale);
        } else if (
          afterSale.state === AfterSaleState.RefusalOfRefund ||
          afterSale.state === AfterSaleState.RefusalOfGoods
        ) {
          //商家拒绝 待用户处理超时
          await this.confirmReceiptAndRefundTimeout(ctx, afterSale);
        }
      } catch (error) {
        Logger.error(error);
      }
    }
  }
  async confirmReceiptAndRefundTimeout(ctx: RequestContext, afterSale: AfterSale) {
    if (this.verifyTimeOut(afterSale.auditAt, Number(process.env.REFUSE_TIMEOUT_DAYS || 7))) {
      await this.cancelAfterSale(ctx, afterSale.id);
    } else {
      return;
    }
  }
  /**
   * 换货用户超时未确认收货
   * @param ctx
   * @param afterSale
   */
  async confirmReceiptAndDeliveryTimeout(ctx: RequestContext, afterSale: AfterSale) {
    if (this.verifyTimeOut(afterSale.merchantDeliveryTime, Number(process.env.EXCHANGE_TIMEOUT_DAYS || 15))) {
      await this.afterSaleCustomerReceipt(ctx, afterSale.id);
    } else {
      return;
    }
  }
  async confirmReceiptTimeout(ctx: RequestContext, afterSale: AfterSale) {
    if (this.verifyTimeOut(afterSale.deliverAt, Number(process.env.CONFIRM_RECEIPT_TIMEOUT_DAYS || 7))) {
      await this.afterSaleAudit(ctx, {
        afterSaleId: afterSale.id as string,
        state: AuditState.Agree,
      });
    } else {
      return;
    }
  }
  async deliveryTimeout(ctx: RequestContext, afterSale: AfterSale) {
    if (this.verifyTimeOut(afterSale.auditAt, Number(process.env.RETURN_TIMEOUT_DAYS || 7))) {
      await this.cancelAfterSale(ctx, afterSale.id);
    } else {
      return;
    }
  }
  async inspectionTimeoutAudit(ctx: RequestContext, afterSale: AfterSale) {
    if (this.verifyTimeOut(afterSale.applyAt, Number(process.env.AUDIT_TIMEOUT_DAYS || 7))) {
      await this.afterSaleAudit(ctx, {
        afterSaleId: afterSale.id as string,
        state: AuditState.Agree,
      });
    } else {
      return;
    }
  }

  verifyTimeOut(verificationTime: Date, timeoutDays: number) {
    const currentTime = DateTime.now();
    const maturityTime = DateTime.fromJSDate(verificationTime).plus({days: timeoutDays});
    if (maturityTime <= currentTime) {
      return true;
    } else {
      return false;
    }
  }

  async afterSaleCustomerReceipt(ctx: RequestContext, afterSaleId: ID) {
    const afterSale = await this.findOne(ctx, afterSaleId);
    if (!afterSale) {
      throw new Error('after sale not exist');
    }
    if (afterSale.state !== AfterSaleState.ConfirmReceiptAndDelivery) {
      throw new Error('The order is not in the state of waiting for the customer to confirm receipt');
    }
    await this.connection.getRepository(ctx, AfterSale).update(afterSaleId, {
      state: AfterSaleState.Complete,
      confirmReceiptAt: new Date(),
    });
    await this.afterSaleHistoryService.createHistory(
      ctx,
      AfterSaleHistoryType.Complete,
      OperatorType.User,
      afterSale,
      HistoryMessage.ConfirmReceiptOfGoods,
    );
    return afterSale;
  }
  @Transaction()
  async afterSaleCreate(ctx: RequestContext, afterSale: AfterSale, type: string) {
    if (!afterSale) {
      Logger.error(`after sale not exist`);
      return;
    }
    if (afterSale.state !== AfterSaleState.Submit || afterSale.mode === AfterSaleMode.ExchangeGoods) {
      return;
    }
    // 申请售后锁定支付有礼赠送的优惠券
    await this.paymentRewardActivityService.applyRefundOrderLockPaymentRewardCoupon(ctx, afterSale.order.id);
    // 申请售后锁定购买的优惠券
    await this.payCouponService.applyRefundOrderLockPayCoupon(ctx, afterSale.order.id);
  }

  @Transaction()
  async afterSaleCancel(ctx: RequestContext, afterSale: AfterSale, type: string) {
    if (!afterSale) {
      Logger.error(`after sale not exist`);
      return;
    }
    if (afterSale.state !== AfterSaleState.Cancel || afterSale.mode === AfterSaleMode.ExchangeGoods) {
      return;
    }
    // 取消售后释放支付有礼赠送的优惠券
    await this.paymentRewardActivityService.cancelRefundOrderUnlockPaymentRewardCoupon(ctx, afterSale.order.id);
    // 取消售后释放购买的优惠券
    await this.payCouponService.cancelRefundOrderUnlockPayCoupon(ctx, afterSale.order.id);
  }

  @Transaction()
  async afterSaleStatusChange(ctx: RequestContext, id: ID, type: string) {
    const afterSale = await this.findOne(ctx, id, {}, [
      'order',
      'afterSaleLines',
      'afterSaleLines.orderLine',
      'afterSaleLines.orderLine.productVariant',
      'afterSaleLines.orderLine.productVariant.product',
    ]);
    if (!afterSale) {
      Logger.error(`after sale not exist`);
      return;
    }
    if (afterSale.state !== AfterSaleState.SuccessfulRefund) {
      return;
    }
    const order = await this.orderService.findOne(ctx, afterSale.order.id);
    if (!order) {
      Logger.error(`order not exist`);
      return;
    }
    //售后完成修改会员购买状态
    await this.afterSalePromotionResult(ctx, afterSale, order);
    //退款退积分
    await this.afterSaleRefundPoints(ctx, afterSale, order);
    //售后完成产品销量减少
    await this.salesVolumeReduce(ctx, afterSale);
    //兑换商品售后完成需要退回兑换券
    await this.returnUserCoupon(ctx, afterSale, order);
    //订单项取消
    await this.orderLineCancel(ctx, afterSale, order);
    //订单全部售后成功则取消订单——>订单全部项的可退款金额为0则取消订单
    await this.orderPromotionResultService.checkOrderNeedsToBeCancelled(ctx, order);
    //售后完成修改内存的产品购买数量
    await this.productLimitation(ctx, afterSale, order);
    //售后完成修改优惠结果金额
    await this.afterSaleRefundRecord(ctx, afterSale, order);
    // 售后完成修改分销的订单有效金额
    await this.afterSaleDistributorAmount(ctx, order, afterSale);
    // 售后完成退回支付有礼赠送的优惠券
    await this.paymentRewardActivityService.refundOrderPaymentRewardCoupon(ctx, order.id);
    // 售后完成退回购买的优惠券
    await this.payCouponService.refundOrderPayCoupon(ctx, order.id);
  }

  async afterSaleDistributorAmount(ctx: RequestContext, order: Order, afterSale: AfterSale) {
    // 换货不需要扣除分销员的订单有效金额
    if (afterSale.mode === AfterSaleMode.ExchangeGoods) {
      return;
    }
    // 售后状态不是成功退款的不需要扣除分销员的订单有效金额
    if (afterSale.state !== AfterSaleState.SuccessfulRefund) {
      return;
    }
    const price = afterSale.price;
    await this.distributorService.subtractDistributorDetailOrderAmount(ctx, order, price);
  }

  // 兑换商品售后完成需要退回兑换券
  // 退回优惠券
  async returnUserCoupon(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    const orderId = order.id;
    const afterSaleLineIds = afterSale.afterSaleLines.map(line => {
      return line.orderLine.id;
    });
    const promotionResult = await this.connection
      .getRepository(ctx, OrderPromotionResult)
      .findOne({where: {order: {id: orderId}}});
    const orderLinePromResults = promotionResult?.promResult?.orderLinePromResults;
    //获取orderLinePromResults中使用了优惠券的orderLineId
    const orderLineIds = orderLinePromResults?.filter(item => {
      if (item?.discountDetails) {
        return item.discountDetails.map(detail => detail?.type).includes(PromotionType.Coupon);
      }
      return false;
    });
    // 使用优惠券的orderLineId不存在或者长度不为1则不需要退回优惠券
    // 只需要退回兑换券 一个订单只能使用一个兑换券
    if (!orderLineIds || orderLineIds.length !== 1) {
      Logger.info(`orderLineIds not exist or length is not 1`);
      return;
    }
    const orderLineId = orderLineIds[0]?.orderLineId;
    // 使用优惠券的orderLine不是售后的orderLine则不需要退回优惠券
    if (!orderLineId || !afterSaleLineIds.includes(orderLineId)) {
      Logger.info(`after sale line not exist`);
      return;
    }
    // 获取优惠券
    const couponProm = promotionResult?.promResult?.coupons?.find(item => item?.selected);
    if (!couponProm) {
      Logger.info(`coupon not exist`);
      return;
    }
    const userCouponId = couponProm.couponId;
    if (!userCouponId) {
      Logger.info(`user coupon id not exist`);
      return;
    }
    const userCoupon = await this.connection
      .getRepository(ctx, UserCoupon)
      .findOne({where: {id: userCouponId}, relations: ['coupon']});
    if (!userCoupon) {
      Logger.info(`coupon not exist`);
      return;
    }
    if (userCoupon.coupon.type !== CouponType.Exchange) {
      Logger.info(`coupon type is not exchange`);
      return;
    }
    await this.couponService.couponUnlocking(ctx, userCouponId, orderId);
  }

  async orderLineCancel(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    // 批量退款和单个退款的处理 都没上传售后数量的话则默认为全部退款
    const afterSaleLines = afterSale.afterSaleLines;
    const afterSaleLineId = afterSaleLines.map(afterSaleLine => afterSaleLine.orderLine.id);
    const orderLine = order.lines.filter(line => afterSaleLineId.includes(line.id));
    const orderLinesInput = orderLine.map(line => {
      return {
        orderLineId: line.id,
        quantity: line.quantity,
      };
    });
    // await this.orderService.cancelOrder(ctx, {
    //   orderId: afterSale.order.id,
    //   lines: orderLinesInput.map(line => {
    //     return {
    //       orderLineId: line.id,
    //       quantity: line.quantity,
    //     };
    //   }),
    // });
    await this.orderPromotionResultService.checkOrderLineToBeCancelled(ctx, order, orderLinesInput);
  }

  async afterSaleRefundRecord(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    const promotionResultsDetail = await this.connection.getRepository(ctx, PromotionResultDetail).findOne({
      where: {
        order: {id: order.id},
      },
    });
    if (!promotionResultsDetail) {
      return;
    }
    const refundAmount = afterSale.price;
    promotionResultsDetail.refundPrice = Number(promotionResultsDetail.refundPrice + refundAmount);
    await this.connection.getRepository(ctx, PromotionResultDetail).save(promotionResultsDetail);
    return;
  }

  async afterSalePromotionResult(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    let memberProductCount = 0;
    const afterSaleLines = afterSale.afterSaleLines;
    for (const afterSaleLine of afterSaleLines) {
      if ((afterSaleLine.orderLine.productVariant.product.customFields as ProductCustomFields).isVipProduct) {
        memberProductCount += afterSaleLine.quantity;
      }
    }
    await this.orderPromotionResultService.afterSalePromotionResult(ctx, order, memberProductCount);
  }

  async productLimitation(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    const customerId = order.customer?.id;
    if (!customerId) {
      Logger.error(`售后完成修改内存的产品购买数量失败,customerId not exist`);
      return;
    }
    const customerCtx = await this.commonService.getCtxByCustomerAndChannels(
      order.channels,
      order.customer as Customer,
    );
    if (!customerCtx) {
      return;
    }
    const afterSaleLines = afterSale.afterSaleLines;
    for (const afterSaleLine of afterSaleLines) {
      const orderLine = afterSaleLine.orderLine;
      if ((orderLine.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium) {
        continue;
      }
      const productId = orderLine.productVariant.productId;
      if (productId) {
        await this.kvsService.productLimitation.del(`${customerId}_${productId}`);
        const pointsExchangeInfo = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
        if (pointsExchangeInfo) {
          await this.kvsService.productLimitation.del(
            `Exchangeable:${customerId}_${productId}_${pointsExchangeInfo.id}`,
          );
        }
      }
    }
  }

  async salesVolumeReduce(ctx: RequestContext, afterSale: AfterSale) {
    const afterSaleLines = afterSale.afterSaleLines;
    for (const afterSaleLine of afterSaleLines) {
      const orderLine = afterSaleLine.orderLine;
      const product = await this.productService.findOne(ctx, orderLine.productVariant.productId);
      if (!product) {
        continue;
      }
      let {salesVolume} = product.customFields as ProductCustomFields;
      salesVolume = salesVolume ? salesVolume : 0;
      if (salesVolume < afterSaleLine.quantity) {
        salesVolume = 0;
      } else {
        salesVolume = salesVolume - afterSaleLine.quantity;
      }
      product.customFields = {
        salesVolume: salesVolume,
      };
      await this.connection.getRepository(ctx, Product).save(product);
    }
  }
  /**
   * 检查订单是否全部售后成功 全部售后成功的话则需要取消订单—————>订单全部项的可退款金额为0则取消订单
   * @param ctx
   * @param order
   */
  // async checkOrderNeedsToBeCancelled(ctx: RequestContext, order: Order) {
  // const orderAllAfterSales = await this.connection.getRepository(ctx, AfterSale).find({
  //   where: {
  //     order: {id: order.id},
  //     state: AfterSaleState.SuccessfulRefund,
  //   },
  //   relations: ['afterSaleLines', 'afterSaleLines.orderLine'],
  // });
  // let orderAllAfterSaleLineIds: ID[] = [];
  // for (const orderAllAfterSale of orderAllAfterSales) {
  //   const afterSaleIds = orderAllAfterSale.afterSaleLines.map(line => line.orderLine.id);
  //   orderAllAfterSaleLineIds = orderAllAfterSaleLineIds.concat(afterSaleIds);
  // }
  // const orderLineIds = order.lines.map(line => line.id);
  // if (orderLineIds.length === orderAllAfterSaleLineIds.length) {
  //   await this.orderService.cancelOrder(ctx, {orderId: order.id});
  // }
  // }

  /**
   * 退款退积分
   * @param ctx
   * @param afterSale
   * @param order
   * @returns
   */
  async afterSaleRefundPoints(ctx: RequestContext, afterSale: AfterSale, order: Order) {
    const orderPromResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
    const orderTotal = orderPromResult?.promResult?.orderTotalPrice ?? order.subTotal;
    const afterSaleTotal = await this.getAfterSaleTotalAmount(ctx, afterSale.id);
    const pointsHistory = await this.pointsService.findPointByType(
      ctx,
      order.id,
      PointsSourceType.Order,
      SymbolType.In,
    );
    if (!pointsHistory) {
      Logger.error(`pointsHistory not exist`);
      return;
    }
    const customer = pointsHistory.customer;
    if (!customer) {
      Logger.error(`customer not exist`);
      return;
    }
    const pointsTotal = pointsHistory.points;
    //退款需要扣除的积分 = 订单总积分 * （售后总金额 / 订单总金额）
    let points = Math.floor(pointsTotal * (afterSaleTotal / orderTotal));
    const orderLineIds = order?.lines?.map(line => line.id);
    if (orderLineIds && orderLineIds.length > 0) {
      const getRefundableAmount = await this.orderPromotionResultService.getRefundableAmount(
        ctx,
        order.id,
        orderLineIds,
      );
      if (getRefundableAmount <= 0) {
        // 已经退回的总积分
        const qb = this.connection.getRepository(ctx, PointsHistory).createQueryBuilder('pointsHistory');
        qb.leftJoin('pointsHistory.customer', 'customer');
        qb.andWhere('pointsHistory.deletedAt is null');
        qb.andWhere('customer.id = :customerId', {customerId: customer.id});
        qb.andWhere('pointsHistory.sourceId = :sourceId', {sourceId: order.id});
        qb.andWhere('pointsHistory.symbolType = :symbolType', {symbolType: SymbolType.Out});
        qb.andWhere('pointsHistory.channelId = :channelId', {channelId: ctx.channelId});
        qb.andWhere('pointsHistory.sourceType  in (:...sourceType)', {
          sourceType: [PointsSourceType.AfterSale, PointsSourceType.VoluntaryRefund],
        });
        qb.select('SUM(pointsHistory.points)', 'totalPoints');
        const pointsHistoryTotal = await qb.getRawOne<{totalPoints: number}>();
        // 已经退回的总积分
        const totalPoints = pointsHistoryTotal?.totalPoints ?? 0;
        // 剩余积分
        const surplusPoints = pointsTotal - totalPoints;
        if (points !== surplusPoints) {
          points = surplusPoints;
        }
      }
    }

    if (points <= 0) {
      Logger.error(`points is less than 0`);
      return;
    }
    await this.customerPointsService.updateCustomerPoints(ctx, customer.id, points, SymbolType.Out);

    Logger.debug(`用户售后更新积分成功`);
    const newPointsHistory = new PointsHistory({
      customer,
      sourceId: order.id,
      sourceType: PointsSourceType.AfterSale,
      points: points,
      symbolType: SymbolType.Out,
      description: `订单退款成功,扣除积分`,
      channelId: ctx.channelId,
    });
    await this.connection.getRepository(ctx, PointsHistory).save(newPointsHistory);
    this.eventBus.publish(
      new PointsChangeEvent(ctx, customer, points, SymbolType.Out, newPointsHistory.description, newPointsHistory),
    );
    Logger.debug(`保存积分历史成功`);
  }

  async getAfterSaleTotalAmount(ctx: RequestContext, afterSaleId: ID) {
    const aftersale = await this.findOne(ctx, afterSaleId, undefined, ['afterSaleLines', 'afterSaleLines.orderLine']);
    if (!aftersale) {
      throw new Error('after sale not exist');
    }
    return aftersale.price;
    // let total = 0;
    // for (const afterSaleLine of aftersale.afterSaleLines) {
    //   const orderLine = afterSaleLine.orderLine;
    //   if (orderLine) {
    //     total += orderLine.unitPrice * afterSaleLine.quantity;
    //   }
    // }
    // return total;
  }

  async timeUpdateLogistics() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const sixHoursAgo = DateTime.now().minus({hours: 6}).toJSDate();
    const logistics = await this.connection
      .getRepository(ctx, AfterSaleLogistics)
      .createQueryBuilder('afterSaleLogistics')
      // 顺丰快递时需要获取订单的收件人信息
      .leftJoinAndSelect('afterSaleLogistics.afterSale', 'afterSale')
      .leftJoinAndSelect('afterSale.order', 'order')
      .where('afterSaleLogistics.state != :state', {state: LogisticState.SignFor})
      .andWhere(
        new Brackets(qbSql => {
          qbSql
            .orWhere('afterSaleLogistics.lastUpdateTime IS NULL')
            .orWhere('afterSaleLogistics.lastUpdateTime <= :sixHoursAgo', {sixHoursAgo: sixHoursAgo});
        }),
      )
      .getMany();
    for (const logistic of logistics) {
      const {company, logisticCode, afterSale} = logistic;
      const logisticCodeSF = await this.getNewLogisticsCode(ctx, company, logisticCode, afterSale);
      const resultInfo = await this.logisticsService.getLogisticInfo(logisticCodeSF);
      const state = this.logisticsService.stateTransition(resultInfo.State);
      const logisticsInfo = this.logisticsService.infoTransition(resultInfo, state);
      logistic.logisticInfo = logisticsInfo;
      logistic.state = state;
      logistic.lastUpdateTime = new Date();
      await this.connection.getRepository(ctx, AfterSaleLogistics).save(logistic);
    }
  }

  @Transaction()
  async updateOrderCustomField(ctx: RequestContext, order: Order, state: string) {
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      if (state === 'PaymentSettled') {
        (order.customFields as OrderCustomFields).isAvailableAfterSale = true;
        await this.orderService.updateCustomFields(ctx, order.id, {
          isAvailableAfterSale: true,
        });
      } else if (state === 'Cancelled') {
        (order.customFields as OrderCustomFields).isAvailableAfterSale = false;
        await this.orderService.updateCustomFields(ctx, order.id, {
          isAvailableAfterSale: false,
        });
        const orderLineIds = order.lines.map(line => line.id);
        await this.orderCustomCommonService.closeOrderLineAfterSale(ctx, order, orderLineIds);
      }
    } catch (error) {
      Logger.error(error);
      throw new Error('update order custom field error');
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async closeOrderAfterSaleEntrance() {
    const ctx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const confirmReceiptTime = DateTime.now()
      .minus({days: Number(process.env.CONFIRM_RECEIPT_GOODS_CLOSE_AFTER_SALE || 0)})
      .toJSDate();
    const orders = await this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.lines', 'lines')
      .andWhere('order.customFieldsConfirmreceipttime < :confirmReceiptTime', {confirmReceiptTime})
      .andWhere('order.customFieldsIsavailableaftersale = :isAvailableAfterSale', {
        isAvailableAfterSale: true,
      })
      .getMany();
    for (const order of orders) {
      const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
      try {
        Logger.debug(`订单${order.id}超过售后时间,修改订单为不可售后订单`);
        await this.orderService.updateCustomFields(ctx, order.id, {
          isAvailableAfterSale: false,
        });
        const orderLineIds = order.lines.map(line => line.id);
        await this.orderCustomCommonService.closeOrderLineAfterSale(ctx, order, orderLineIds);
      } catch (error) {
        Logger.error(error);
      } finally {
        await this.redLockService.unlockResource(lock);
      }
    }
  }

  /**
   * 获取指定时间段内的成功退款订单数
   * @param ctx
   * @param productId
   * @param startTime
   * @param endTime
   */
  async getNumberOfSuccessfulRefundOrders(
    ctx: RequestContext,
    productId: ID,
    startTime: Date,
    endTime: Date,
  ): Promise<number> {
    const numberOfSuccessfulRefundOrders = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.order', 'order')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('product.id = :productId', {productId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('afterSale.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .getCount();
    return Number(numberOfSuccessfulRefundOrders ?? 0);
  }
  /**
   * 获取指定时间段内的申请退款件数
   * @param ctx
   * @param productId
   * @param startTime
   * @param endTime
   */
  async getNumberOfRefundItems(ctx: RequestContext, productId: ID, startTime: Date, endTime: Date): Promise<number> {
    const numberOfRefundItems = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('product.id = :productId', {productId})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('afterSale.mode != :mode', {mode: AfterSaleMode.ExchangeGoods})
      .andWhere('afterSale.state != :state', {state: AfterSaleState.Cancel})
      .andWhere('afterSale.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .select('SUM(afterSaleLines.quantity)', 'quantity')
      .getRawOne();
    return Number(numberOfRefundItems?.quantity ?? 0);
  }
  /**
   * 获取指定时间段内的成功退款件数
   * @param ctx
   * @param productId
   * @param startTime
   * @param endTime
   */
  async getNumberOfSuccessfulRefunds(
    ctx: RequestContext,
    productId: ID,
    startTime: Date,
    endTime: Date,
  ): Promise<number> {
    const numberOfSuccessfulRefunds = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.order', 'order')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('product.id = :productId', {productId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('afterSale.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .select('SUM(afterSaleLines.quantity)', 'quantity')
      .getRawOne();
    return Number(numberOfSuccessfulRefunds?.quantity ?? 0);
  }
  /**
   * 获取指定时间段内的成功退款人数
   * @param ctx
   * @param productId
   * @param startTime
   * @param endTime
   */
  async getNumberOfSuccessfulRefundPeople(
    ctx: RequestContext,
    productId: ID,
    startTime: Date,
    endTime: Date,
  ): Promise<number> {
    const numberOfSuccessfulRefundPeople = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .leftJoin('orderLine.order', 'order')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('product.id = :productId', {productId})
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .andWhere('afterSale.createdAt BETWEEN :startTime AND :endTime', {startTime, endTime})
      .select('COUNT(DISTINCT order.customerId)', 'count')
      .getRawOne();
    return Number(numberOfSuccessfulRefundPeople?.count ?? 0);
  }

  generateRandomNumber(length: number) {
    const randomNumbers = [];
    for (let i = 0; i < length; i++) {
      const randomNumber = Math.floor(Math.random() * 10); // 生成0到9之间的随机数字
      randomNumbers.push(randomNumber);
    }
    return randomNumbers.join('');
  }

  generateDateString() {
    const now = new Date();
    const year = now.getFullYear().toString().padStart(4, '0');
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const randomDigits = this.generateRandomNumber(10);

    const dateString = `${year}${month}${day}${hours}${minutes}${seconds}${randomDigits}`;
    return dateString;
  }

  async getRefundProductStatistics(
    ctx: RequestContext,
    distributor: Distributor,
    startTime: Date,
    endTime: Date,
    productIds: ID[],
  ): Promise<
    {
      productId: ID;
      numberOfRefundItems: number;
      numberOfSuccessfulRefunds: number;
      numberOfSuccessfulRefundPeople: number;
      numberOfSuccessfulRefundOrders: number;
    }[]
  > {
    if (productIds.length === 0) {
      return [];
    }
    const subQuery = this.connection
      .getRepository(ctx, DistributorOrder)
      .createQueryBuilder('distributorOrder')
      .select('order.id', 'orderId')
      .leftJoin('distributorOrder.order', 'order')
      .leftJoin('distributorOrder.distributor', 'distributor')
      .leftJoin('order.lines', 'lines')
      .leftJoin('lines.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('distributor.id = :distributorId')
      .andWhere('order.id is not null')
      .andWhere('product.id IN (:...productIds)')
      .andWhere('distributorOrder.createdAt BETWEEN :startDateTime AND :endDateTime')
      .getQuery();
    // 成功售后数据
    const numberOfSuccessfulRefunds = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.order', 'order')
      .leftJoin('order.customer', 'customer')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('product.id IN (:...productIds)', {productIds})
      .andWhere('order.id IN (' + subQuery + ')')
      .setParameters({
        distributorId: distributor.id,
        productIds,
        startDateTime: startTime,
        endDateTime: endTime,
      })
      .andWhere('afterSale.state = :state', {state: AfterSaleState.SuccessfulRefund})
      .select('product.id', 'productId')
      .addSelect('SUM(afterSaleLines.quantity)', 'quantity')
      .addSelect('COUNT(DISTINCT customer.id)', 'customerCount')
      .addSelect('COUNT(DISTINCT order.id)', 'orderCount')
      .groupBy('product.id')
      .getRawMany<{
        productId: ID;
        quantity: string;
        customerCount: string;
        orderCount: string;
      }>();
    // 申请退款数据
    const numberOfRefundItems = await this.connection
      .getRepository(ctx, AfterSale)
      .createQueryBuilder('afterSale')
      .leftJoin('afterSale.channels', 'channel')
      .leftJoin('afterSale.afterSaleLines', 'afterSaleLines')
      .leftJoin('afterSaleLines.orderLine', 'orderLine')
      .leftJoin('orderLine.productVariant', 'productVariant')
      .leftJoin('productVariant.product', 'product')
      .leftJoin('orderLine.order', 'order')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('product.id IN (:...productIds)', {productIds})
      .andWhere('order.id IN (' + subQuery + ')')
      .setParameters({
        distributorId: distributor.id,
        productIds,
        startDateTime: startTime,
        endDateTime: endTime,
      })
      .andWhere('afterSale.mode != :mode', {mode: AfterSaleMode.ExchangeGoods})
      .andWhere('afterSale.state != :state', {state: AfterSaleState.Cancel})
      .select('product.id', 'productId')
      .addSelect('SUM(afterSaleLines.quantity)', 'quantity')
      .groupBy('product.id')
      .getRawMany<{
        productId: ID;
        quantity: string;
      }>();
    // 数据整合
    const result = productIds.map(productId => {
      const refundItem = numberOfRefundItems?.find(item => String(item.productId) === String(productId));
      const successfulRefund = numberOfSuccessfulRefunds?.find(item => String(item.productId) === String(productId));
      return {
        productId,
        numberOfRefundItems: Number(refundItem?.quantity ?? 0),
        numberOfSuccessfulRefunds: Number(successfulRefund?.quantity ?? 0),
        numberOfSuccessfulRefundPeople: Number(successfulRefund?.customerCount ?? 0),
        numberOfSuccessfulRefundOrders: Number(successfulRefund?.orderCount ?? 0),
      };
    });
    return result;
  }
}
