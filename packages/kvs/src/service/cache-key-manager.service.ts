import {Injectable} from '@nestjs/common';
import {ID, RequestContext} from '@vendure/core';

/**
 * 缓存Key管理器
 * 统一管理项目中所有的缓存key生成逻辑
 */
@Injectable()
export class CacheKeyManagerService {
  private static readonly CACHE_PREFIX = 'Query';

  /**
   * 生成基础缓存key
   * @param parts key的各个部分
   * @returns 完整的缓存key
   */
  private static generateKey(...parts: (string | number | ID)[]): string {
    return `${this.CACHE_PREFIX}:${parts.join(':')}`;
  }

  // ==================== Product相关缓存key ====================

  /**
   * 商品基础缓存key
   */
  static product(productId: ID, channelId: ID): string {
    return this.generateKey('Product', productId, channelId);
  }

  /**
   * 商品查找缓存key
   */
  static productFindOne(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'FindOne', productId, channelId);
  }

  /**
   * 商品水合缓存key
   */
  static productHydrate(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'hydrate', productId, channelId);
  }

  /**
   * 商品分组缓存key
   */
  static productCollections(collectionId: ID, channelId: ID): string {
    return this.generateKey('Product', 'Collections', collectionId, channelId);
  }

  /**
   * 商品总库存缓存key
   */
  static productTotalStock(productId: ID, channelId: ID): string {
    return this.generateKey('ProductTotalStock', productId, channelId);
  }

  /**
   * 商品特色资产缓存key
   */
  static productFeaturedAsset(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'FeaturedAsset', productId, channelId);
  }

  /**
   * 商品实体资产缓存key
   */
  static productEntityAssets(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'EntityAssets', productId, channelId);
  }

  /**
   * 商品购买权限缓存key
   */
  static productPurchasePermission(productId: ID, channelId: ID): string {
    return this.generateKey('ProductPurchasePermission', productId, channelId);
  }

  /**
   * 积分商品缓存key
   */
  static pointsProduct(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'PointsProduct', productId, channelId);
  }

  // ==================== ProductVariant相关缓存key ====================

  /**
   * 商品变体缓存key
   */
  static productVariants(productId: ID, channelId: ID): string {
    return this.generateKey('Variants', productId, channelId);
  }

  /**
   * 商品变体库存水平缓存key
   */
  static productVariantStockLevel(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariant', 'StockLevel', variantId, channelId);
  }

  // ==================== Collection相关缓存key ====================

  /**
   * 分组缓存key
   */
  static collection(productId: ID, channelId: ID): string {
    return this.generateKey('Collection', productId, channelId);
  }

  // ==================== Promotion相关缓存key ====================

  /**
   * 促销活动缓存key
   */
  static promotion(promotionId: ID, channelId: ID): string {
    return this.generateKey('Promotion', promotionId, channelId);
  }

  /**
   * 促销活动（包含失败）缓存key
   */
  static promotionIncludeFailure(promotionId: ID, channelId: ID): string {
    return this.generateKey('Promotion', 'IncludeFailure', promotionId, channelId);
  }

  /**
   * 商品促销活动（全部）缓存key
   */
  static productPromotionActiveAll(channelId: ID, includeNotStarted = false): string {
    if (includeNotStarted) {
      return this.generateKey('ProductPromotionActive', 'All', 'IncludeNotStarted', channelId);
    }
    return this.generateKey('ProductPromotionActive', 'All', channelId);
  }

  /**
   * 商品促销活动（不可用商品）缓存key
   */
  static productPromotionActiveUnusableGoods(productId: ID, channelId: ID, includeNotStarted = false): string {
    if (includeNotStarted) {
      return this.generateKey('ProductPromotionActive', 'UnusableGoods', 'IncludeNotStarted', productId, channelId);
    }
    return this.generateKey('ProductPromotionActive', 'UnusableGoods', productId, channelId);
  }

  // ==================== CustomPage相关缓存key ====================

  /**
   * 自定义页面缓存key
   */
  static customPage(pageId: ID, channelId: ID): string {
    return this.generateKey('CustomPage', pageId, channelId);
  }

  /**
   * 自定义页面类型缓存key
   */
  static customPageByType(pageType: string, channelId: ID): string {
    return this.generateKey('CustomPage', pageType, channelId);
  }

  // ==================== Activity相关缓存key ====================

  /**
   * 选择性礼品活动缓存key
   */
  static selectiveGiftActivity(activityId: ID, channelId: ID): string {
    return this.generateKey('SelectiveGiftActivity', activityId, channelId);
  }

  /**
   * 购物积分领取活动缓存key
   */
  static shoppingCreditsClaimActivity(activityId: ID, channelId: ID): string {
    return this.generateKey('ShoppingCreditsClaimActivity', activityId, channelId);
  }

  /**
   * 购物积分扣减活动缓存key
   */
  static shoppingCreditsDeductionActivity(activityId: ID, channelId: ID): string {
    return this.generateKey('ShoppingCreditsDeductionActivity', activityId, channelId);
  }

  /**
   * 排斥组缓存key
   */
  static exclusionGroup(groupId: ID, channelId: ID): string {
    return this.generateKey('ExclusionGroup', groupId, channelId);
  }

  /**
   * 排斥组产品缓存key
   */
  static exclusionProductExclusionGroup(groupId: ID, channelId: ID): string {
    return this.generateKey('ExclusionProduct:ExclusionGroup', groupId, channelId);
  }

  /**
   * 排斥组类型缓存key
   */
  static exclusionGroupType(type: string, channelId: ID): string {
    return this.generateKey('ExclusionGroup:Type', type, channelId);
  }

  /**
   * 套餐折扣缓存key
   */
  static packageDiscount(id: ID, channelId: ID): string {
    return this.generateKey('PackageDiscount', id, channelId);
  }

  /**
   * 购买溢价活动缓存key
   */
  static purchasePremium(premiumId: ID, channelId: ID): string {
    return this.generateKey('PurchasePremium', premiumId, channelId);
  }

  // ==================== 其他缓存key ====================

  /**
   * 排除组（全部）缓存key
   */
  static exclusionGroupAll(channelId: ID): string {
    return this.generateKey('ExclusionGroup', 'All', channelId);
  }

  /**
   * 会员价格缓存key
   */
  static memberPrice(memberPriceId: ID, channelId: ID): string {
    return this.generateKey('MemberPrice', memberPriceId, channelId);
  }

  /**
   * 会员价格（无会员价格ID）缓存key
   */
  static memberPriceNotMemberPriceId(channelId: ID): string {
    return this.generateKey('MemberPrice', 'NotMemberPriceId', channelId);
  }

  /**
   * 积分配置缓存key
   */
  static pointsConfig(channelId: ID): string {
    return this.generateKey('PointsConfig', channelId);
  }

  /**
   * 选项组缓存key
   */
  static optionGroups(productId: ID, channelId: ID): string {
    return this.generateKey('OptionGroups', productId, channelId);
  }

  /**
   * 选项组选项缓存key
   */
  static optionGroupsOptions(productId: ID, channelId: ID): string {
    return this.generateKey('OptionGroups', 'Options', productId, channelId);
  }

  /**
   * 面值缓存key
   */
  static facetValues(productId: ID, channelId: ID): string {
    return this.generateKey('FacetValues', productId, channelId);
  }

  /**
   * 通用实体特色资产缓存key
   */
  static entityFeaturedAsset(cacheKey: string, entityId: ID, channelId: ID): string {
    return this.generateKey(cacheKey, 'FeaturedAsset', entityId, channelId);
  }

  /**
   * 通用实体资产缓存key
   */
  static entityAssets(cacheKey: string, entityId: ID, channelId: ID): string {
    return this.generateKey(cacheKey, 'EntityAssets', entityId, channelId);
  }

  // ==================== Member相关缓存key ====================

  /**
   * 会员缓存key
   */
  static member(customerId: ID, channelId: ID): string {
    return this.generateKey('Member', customerId, channelId);
  }

  /**
   * 会员（未激活）缓存key
   */
  static memberNotActivate(customerId: ID, channelId: ID): string {
    return this.generateKey('Member', 'NotActivate', customerId, channelId);
  }

  /**
   * 会员订单缓存key
   */
  static memberOrders(memberId: ID, channelId: ID): string {
    return this.generateKey('MemberOrders', memberId, channelId);
  }

  /**
   * 会员计划缓存key
   */
  static membershipPlan(membershipPlanId: ID, channelId: ID): string {
    return this.generateKey('MembershipPlan', membershipPlanId, channelId);
  }

  /**
   * 会员计划（全部）缓存key
   */
  static membershipPlanAll(channelId: ID): string {
    return this.generateKey('MembershipPlan', channelId);
  }

  /**
   * 会员计划优惠券缓存key
   */
  static membershipPlanCoupon(membershipPlanId: ID, channelId: ID): string {
    return this.generateKey('MembershipPlan', 'Coupon', membershipPlanId, channelId);
  }

  // ==================== Order相关缓存key ====================

  /**
   * 订单物流缓存key
   */
  static orderLogistics(orderId: ID, channelId: ID): string {
    return this.generateKey('Order', 'logistics', orderId, channelId);
  }

  /**
   * 订单促销结果缓存key
   */
  static orderPromotionResult(orderId: ID, channelId: ID): string {
    return this.generateKey('Order', 'orderPromotionResult', orderId, channelId);
  }

  /**
   * 订单订阅缓存key
   */
  static orderSubscription(orderId: ID, channelId: ID): string {
    return this.generateKey('Order', 'subscription', orderId, channelId);
  }

  /**
   * 订单订阅计划缓存key
   */
  static orderSubscriptionPlan(orderId: ID, channelId: ID): string {
    return this.generateKey('Order', 'subscriptionPlan', orderId, channelId);
  }

  // ==================== OrderLine相关缓存key ====================

  /**
   * 订单行售后缓存key
   */
  static orderLineAfterSale(orderLineId: ID, channelId: ID): string {
    return this.generateKey('OrderLine', 'afterSale', orderLineId, channelId);
  }

  /**
   * 订单行售后行缓存key
   */
  static orderLineAfterSaleLine(orderLineId: ID, channelId: ID): string {
    return this.generateKey('OrderLine', 'afterSaleLine', orderLineId, channelId);
  }

  // ==================== 更多Activity相关缓存key ====================

  /**
   * 折扣活动缓存key
   */
  static discountActivity(activityId: ID, channelId: ID): string {
    return this.generateKey('DiscountActivity', activityId, channelId);
  }

  /**
   * 满减赠送缓存key
   */
  static fullDiscountPresent(presentId: ID, channelId: ID): string {
    return this.generateKey('FullDiscountPresent', presentId, channelId);
  }

  // ==================== ProductVariant扩展缓存key ====================
  /**
   * 商品变体缓存key
   */
  static productVariant(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariant', variantId, channelId);
  }

  /**
   * 商品变体（包含删除）缓存key
   */
  static productVariantIncludeDelete(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariant', 'IncludeDelete', variantId, channelId);
  }

  /**
   * 商品变体水合缓存key
   */
  static productVariantHydrate(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariant', 'hydrate', variantId, channelId);
  }

  /**
   * 商品变体面值缓存key
   */
  static productVariantFacetValues(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariantFacetValues', variantId, channelId);
  }

  /**
   * 商品变体选项缓存key
   */
  static productVariantOptions(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariantOptions', variantId, channelId);
  }

  /**
   * 商品变体价格缓存key
   */
  static productVariantPrices(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariantPrices', variantId, channelId);
  }

  /**
   * 商品变体产品缓存key
   */
  static productVariantProduct(productId: ID, channelId: ID): string {
    return this.generateKey('ProductVariantProduct', productId, channelId);
  }

  /**
   * 商品变体税收类别缓存key
   */
  static productVariantTaxCategory(variantId: ID, channelId: ID): string {
    return this.generateKey('ProductVariantTaxCategory', variantId, channelId);
  }

  // ==================== ExclusionGroup扩展缓存key ====================
  /**
   * 排除组类型缓存key
   */
  static exclusionGroupByType(type: string, channelId: ID): string {
    return this.generateKey('ExclusionGroup', 'Type', type, channelId);
  }

  /**
   * 排除组使用中缓存key
   */
  static exclusionGroupUsing(channelId: ID): string {
    return this.generateKey('ExclusionGroup', 'Using', channelId);
  }

  /**
   * 排除产品组缓存key
   */
  static exclusionProductGroup(groupId: ID, channelId: ID): string {
    return this.generateKey('ExclusionProduct', 'ExclusionGroup', groupId, channelId);
  }

  // ==================== 其他扩展缓存key ====================

  /**
   * 客户ID用户ID缓存key
   */
  static customerIdUserId(userId: ID, channelId: ID): string {
    return this.generateKey('CustomerId', 'UserId', userId, channelId);
  }

  /**
   * 客户分销商缓存key
   */
  static customerDistributor(customerId: ID, channelId: ID): string {
    return this.generateKey('Customer', 'distributor', customerId, channelId);
  }

  /**
   * 商品选项组缓存key
   */
  static productOptionGroup(groupId: ID, channelId: ID): string {
    return this.generateKey('ProductOptionGroup', groupId, channelId);
  }

  /**
   * 商品选项组选项缓存key
   */
  static productOptionGroupOption(groupId: ID, channelId: ID): string {
    return this.generateKey('ProductOptionGroup', 'ProductOption', groupId, channelId);
  }

  /**
   * 商品变体价格（产品级别）缓存key
   */
  static productVariantPricesForProduct(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'ProductVariantPrices', productId, channelId);
  }

  /**
   * 商品购买权限（显示禁用）缓存key
   */
  static productPurchasePermissionShowDisable(productId: ID, channelId: ID): string {
    return this.generateKey('ProductPurchasePermission', 'ShowDisable', productId, channelId);
  }

  /**
   * 商品订阅计划缓存key
   */
  static productSubscriptionPlan(productId: ID, channelId: ID): string {
    return this.generateKey('Product', 'subscriptionPlan', productId, channelId);
  }

  /**
   * 税收类别缓存key
   */
  static taxCategory(channelId: ID): string {
    return this.generateKey('TaxCategory', channelId);
  }

  /**
   * 个人中心缓存key
   */
  static personalCenter(channelId: ID): string {
    return this.generateKey('PersonalCenter', channelId);
  }

  // ==================== Forum相关缓存key ====================

  /**
   * 论坛活跃标签缓存key
   */
  static forumActiveTagsAll(channelId: ID): string {
    return this.generateKey('findAllActiveForumTags', channelId);
  }

  /**
   * 论坛活动帖子数量缓存key
   */
  static forumActivityPostCount(activityId: ID, channelId: ID): string {
    return this.generateKey('forumActivityPostCount', activityId, channelId);
  }

  /**
   * 论坛标签帖子数量缓存key
   */
  static forumTagPostCount(tagId: ID, channelId: ID): string {
    return this.generateKey('forumTagPostCount', tagId, channelId);
  }

  // ==================== ProductPromotionActive扩展缓存key ====================

  /**
   * 商品促销活动（可用商品）缓存key
   */
  static productPromotionActiveAvailableGoods(productId: ID, channelId: ID, includeNotStarted = false): string {
    if (includeNotStarted) {
      return this.generateKey('ProductPromotionActive', 'AvailableGoods', 'IncludeNotStarted', productId, channelId);
    }
    return this.generateKey('ProductPromotionActive', 'AvailableGoods', productId, channelId);
  }

  /**
   * 商品促销活动（基础）缓存key
   */
  static productPromotionActiveBasic(productId: ID, channelId: ID, includeNotStarted = false): string {
    if (includeNotStarted) {
      return this.generateKey('ProductPromotionActive', 'IncludeNotStarted', productId, channelId);
    }
    return this.generateKey('ProductPromotionActive', productId, channelId);
  }

  // ==================== CustomPage扩展缓存key ====================

  /**
   * 自定义页面组件缓存key
   */
  static customPageComponent(pageId: ID, channelId: ID): string {
    return this.generateKey('CustomPage', 'Component', pageId, channelId);
  }

  /**
   * 自定义页面组件类型缓存key
   */
  static customPageComponentByType(pageType: string, channelId: ID): string {
    return this.generateKey('CustomPage', 'Component', pageType, channelId);
  }

  static entityHydrate(entityType: string, entityId: ID, channelId: ID): string {
    return this.generateKey(entityType, 'hydrate', entityId, channelId);
  }

  static customFieldRelation(entityName: string, fieldDefName: string, entityId: ID, channelId: ID): string {
    return this.generateKey(entityName, fieldDefName, entityId, channelId);
  }

  // ==================== 便捷方法 ====================

  /**
   * 从RequestContext生成缓存key的便捷方法
   */
  static fromContext = {
    product: (ctx: RequestContext, productId: ID) => CacheKeyManagerService.product(productId, ctx.channelId),
    productCollections: (ctx: RequestContext, collectionId: ID) =>
      CacheKeyManagerService.productCollections(collectionId, ctx.channelId),
    promotion: (ctx: RequestContext, promotionId: ID) => CacheKeyManagerService.promotion(promotionId, ctx.channelId),
    customPage: (ctx: RequestContext, pageId: ID) => CacheKeyManagerService.customPage(pageId, ctx.channelId),
    pointsConfig: (ctx: RequestContext) => CacheKeyManagerService.pointsConfig(ctx.channelId),
    memberPrice: (ctx: RequestContext, memberPriceId: ID) =>
      CacheKeyManagerService.memberPrice(memberPriceId, ctx.channelId),
    exclusionGroupAll: (ctx: RequestContext) => CacheKeyManagerService.exclusionGroupAll(ctx.channelId),
    member: (ctx: RequestContext, customerId: ID) => CacheKeyManagerService.member(customerId, ctx.channelId),
    productVariant: (ctx: RequestContext, variantId: ID) =>
      CacheKeyManagerService.productVariant(variantId, ctx.channelId),
    discountActivity: (ctx: RequestContext, activityId: ID) =>
      CacheKeyManagerService.discountActivity(activityId, ctx.channelId),
  };
}
