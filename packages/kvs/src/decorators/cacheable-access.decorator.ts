/* eslint-disable @typescript-eslint/no-explicit-any */
import {Logger} from '@vendure/core';

/**
 * 用于存储每个缓存键对应的正在进行的查询
 * 这是一个全局变量，用于在不同实例之间共享
 */
const pendingQueries: Map<string, Promise<any>> = new Map();

/**
 * CacheableAccess 装饰器选项
 */
export interface CacheableAccessOptions {
  /**
   * 缓存键生成函数，用于从方法参数生成缓存键
   * 如果不提供，将使用默认的缓存键生成函数
   */
  cacheKeyFn?: (...args: any[]) => string;

  /**
   * 缓存服务的获取方法名，默认为 'memoryStorageService'
   */
  cacheServiceName?: string;

  /**
   * 缓存服务的 get 方法名，默认为 'get'
   */
  getMethodName?: string;

  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

/**
 * CacheableAccess 装饰器
 * 用于控制方法的并发访问，确保在高并发情况下只有一个请求执行方法，其他请求等待
 * 该装饰器不改变原有方法的缓存逻辑，只负责控制并发访问
 *
 * @param options 装饰器选项
 */
export function cacheableAccess(options?: CacheableAccessOptions) {
  const {cacheKeyFn, cacheServiceName = 'memoryStorageService', getMethodName = 'get', debug = false} = options || {};

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // 保存原始方法
    const originalMethod = descriptor.value;

    // 替换为新方法
    descriptor.value = async function (...args: any[]) {
      // 获取缓存服务
      const cacheService = (this as any)[cacheServiceName];
      if (!cacheService) {
        if (debug) {
          Logger.warn(`CacheableAccess: 缓存服务 '${cacheServiceName}' 不存在，将直接执行原始方法`, 'CacheableAccess');
        }
        return originalMethod.apply(this, args);
      }

      // 生成缓存键
      let cacheKey: string;
      if (cacheKeyFn) {
        cacheKey = cacheKeyFn.apply(this, args);
      } else {
        return originalMethod.apply(this, args);
      }

      if (!cacheKey) {
        return originalMethod.apply(this, args);
      }

      // 检查是否是 shop API 调用
      const isShopApi = args[0]?.apiType === 'shop';

      // 如果不是 shop API 调用，直接执行原始方法
      if (!isShopApi) {
        return originalMethod.apply(this, args);
      }

      // 尝试从缓存获取数据
      const cachedData = cacheService[getMethodName](cacheKey);
      if (cachedData) {
        if (debug) {
          Logger.debug(`CacheableAccess: 缓存命中 [${cacheKey}]，直接返回原始方法`, 'CacheableAccess');
        }
        // 缓存存在，直接执行原始方法
        // 原始方法中会再次检查缓存并返回缓存数据
        return originalMethod.apply(this, args);
      }

      // 检查是否有正在进行的相同查询
      if (pendingQueries.has(cacheKey)) {
        if (debug) {
          Logger.debug(`CacheableAccess: 等待已有查询完成 [${cacheKey}]`, 'CacheableAccess');
        }
        // 等待已有查询完成
        try {
          await pendingQueries.get(cacheKey);
        } catch (e) {
          Logger.error(`CacheableAccess: 等待已有查询完成时出错 [${cacheKey}]: ${e}`, 'CacheableAccess');
        }
        // 然后执行原始方法
        // 此时缓存应该已经被设置，原始方法会检查缓存并返回
        return originalMethod.apply(this, args);
      }

      // 创建新的查询
      if (debug) {
        Logger.debug(`CacheableAccess: 创建新查询 [${cacheKey}]`, 'CacheableAccess');
      }

      // 创建查询 Promise 并存储
      const queryPromise = (async () => {
        try {
          // 执行原始方法
          const result = await originalMethod.apply(this, args);
          return result;
        } catch (error) {
          Logger.error(`CacheableAccess: 执行查询时出错 [${cacheKey}]: ${error}`, 'CacheableAccess');
          throw error;
        } finally {
          // 无论成功还是失败，都从 pendingQueries 中移除
          pendingQueries.delete(cacheKey);
          if (debug) {
            Logger.debug(`CacheableAccess: 查询已完成并从等待队列中移除 [${cacheKey}]`, 'CacheableAccess');
          }
        }
      })();

      // 存储查询 Promise
      pendingQueries.set(cacheKey, queryPromise);

      // 返回查询 Promise
      return queryPromise;
    };

    return descriptor;
  };
}
