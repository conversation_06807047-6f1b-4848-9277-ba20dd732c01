import {PluginCommonModule, VendurePlugin} from '@vendure/core';
import {CacheService} from './service/cache.service';
import {CacheKeyManagerService} from './service/cache-key-manager.service';
import {KvsService} from './service/kvs.service';
import {MemoryStorageService} from './service/memory-storage.service';
import {RedisKeyspaceNotificationService} from './service/redis-keyspace-notification.service';

@VendurePlugin({
  imports: [PluginCommonModule],
  providers: [KvsService, RedisKeyspaceNotificationService, MemoryStorageService, CacheService, CacheKeyManagerService],
})
export class KvsPlugin {}
