/* eslint-disable @typescript-eslint/no-explicit-any */
import {Injectable} from '@nestjs/common';
import {ID} from '@vendure/common/lib/shared-types';
import {FindOptionsUtils} from 'typeorm';

import {Translatable} from '@vendure/core/dist/common/types/locale-types';
import {ConfigService} from '@vendure/core/dist/config/config.service';
import {RelationCustomFieldConfig} from '@vendure/core/dist/config/custom-field/custom-field-types';
import {TransactionalConnection} from '@vendure/core/dist/connection/transactional-connection';
import {VendureEntity} from '@vendure/core/dist/entity/base/base.entity';
import {ProductVariant} from '@vendure/core/dist/entity/product-variant/product-variant.entity';
import {ProductPriceApplicator} from '@vendure/core/dist/service/helpers/product-price-applicator/product-price-applicator';
import {TranslatorService} from '@vendure/core/dist/service/helpers/translator/translator.service';

import {RequestContext} from '@vendure/core/dist/api/common/request-context';
import {CacheKeyManagerService} from '@scmally/kvs';

export interface ResolveRelationConfig {
  ctx: RequestContext;
  entityId: ID;
  entityName: string;
  fieldDef: RelationCustomFieldConfig;
}

@Injectable()
export class MyCustomFieldRelationResolverService {
  constructor(
    private connection: TransactionalConnection,
    private configService: ConfigService,
    private productPriceApplicator: ProductPriceApplicator,
    private translator: TranslatorService,
  ) {}

  /**
   * @description
   * Used to dynamically resolve related entities in custom fields. Based on the field
   * config, this method is able to query the correct entity or entities from the database
   * to be returned through the GraphQL API.
   */
  async resolveRelation(config: ResolveRelationConfig): Promise<VendureEntity | VendureEntity[]> {
    const {ctx, entityId, entityName, fieldDef} = config;

    const subQb = this.connection
      .getRepository(ctx, entityName)
      .createQueryBuilder('entity')
      .leftJoin(`entity.customFields.${fieldDef.name}`, 'relationEntity')
      .select('relationEntity.id')
      .where('entity.id = :id');

    const qb = this.connection
      .getRepository(ctx, fieldDef.entity)
      .createQueryBuilder('relation')
      .where(`relation.id IN (${subQb.getQuery()})`)
      .setParameters({id: entityId});
    if (ctx.apiType === 'shop') {
      const cacheId = CacheKeyManagerService.customFieldRelation(entityName, fieldDef.name, entityId, ctx.channelId);
      const milliseconds = 1000 * 60 * 60 * 24;
      qb.cache(cacheId, milliseconds);
    }
    FindOptionsUtils.joinEagerRelations(qb, qb.alias, qb.expressionMap.mainAlias!.metadata);

    const result = fieldDef.list ? await qb.getMany() : await qb.take(1).getOne();

    if (fieldDef.entity === ProductVariant) {
      if (Array.isArray(result)) {
        await Promise.all(result.map(r => this.applyVariantPrices(ctx, r as any)));
      } else {
        await this.applyVariantPrices(ctx, result as any);
      }
    }

    const translated: any = Array.isArray(result)
      ? result.map(r => (this.isTranslatable(r) ? this.translator.translate(r, ctx) : r))
      : this.isTranslatable(result)
      ? this.translator.translate(result, ctx)
      : result;

    return translated;
  }

  private isTranslatable(input: unknown): input is Translatable {
    // eslint-disable-next-line no-prototype-builtins
    return typeof input === 'object' && input != null && input.hasOwnProperty('translations');
  }

  private async applyVariantPrices(ctx: RequestContext, variant: ProductVariant): Promise<ProductVariant> {
    const qb = this.connection.getRepository(ctx, ProductVariant).createQueryBuilder();
    if (ctx.apiType === 'shop') {
      qb.cache(CacheKeyManagerService.productVariantTaxCategory(variant.id, ctx.channelId), 1000 * 60 * 60 * 24);
    }
    const taxCategory = await qb.relation('taxCategory').of(variant).loadOne();
    variant.taxCategory = taxCategory;
    return this.productPriceApplicator.applyChannelPriceAndTax(variant, ctx);
  }
}
